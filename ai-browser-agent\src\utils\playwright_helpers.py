"""Playwright browser helpers for Chrome automation."""

import logging
import os
from pathlib import Path

from playwright.async_api import Browser<PERSON><PERSON>xt
from playwright.async_api import async_playwright

logger = logging.getLogger(__name__)


async def launch_chrome_context(
    headless: bool | None = None,
    user_data_dir: str | None = None,
    executable_path: str | None = None,
    **kwargs
) -> BrowserContext:
    """Launch persistent Chrome context for CartonCloud automation.
    
    Args:
        headless: Run in headless mode (defaults to PLAYWRIGHT_HEADLESS env var)
        user_data_dir: Chrome user data directory (defaults to %USERPROFILE%\\.pw_chrome)
        executable_path: Chrome executable path (defaults to CHROME_EXECUTABLE_PATH env var)
        **kwargs: Additional launch options
        
    Returns:
        Persistent browser context
    """
    playwright = await async_playwright().start()

    # Configure defaults
    if headless is None:
        headless = os.getenv("PLAYWRIGHT_HEADLESS", "false").lower() == "true"

    if user_data_dir is None:
        user_data_dir = str(Path.home() / ".pw_chrome")

    if executable_path is None:
        executable_path = os.getenv("CHROME_EXECUTABLE_PATH")

    # Launch options
    launch_options = {
        "headless": headless,
        "user_data_dir": user_data_dir,
        "args": ["--start-maximized"],
        **kwargs
    }

    # Use channel or executable path
    if executable_path:
        launch_options["executable_path"] = executable_path
    else:
        launch_options["channel"] = "chrome"

    logger.info(f"Launching Chrome context with user_data_dir: {user_data_dir}")
    context = await playwright.chromium.launch_persistent_context(**launch_options)

    return context


async def create_new_page(context: BrowserContext, url: str | None = None):
    """Create a new page in the browser context.
    
    Args:
        context: Browser context
        url: URL to navigate to (optional)
        
    Returns:
        New page instance
    """
    page = await context.new_page()

    if url:
        await page.goto(url)
        await page.wait_for_load_state("networkidle")

    return page
