"""OAuth 2.0 authentication for CartonCloud API."""

import os
from typing import Optional
import requests
from tenacity import retry, stop_after_attempt, wait_exponential
import logging

logger = logging.getLogger(__name__)


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=2, min=2, max=8))
def get_token(
    client_id: Optional[str] = None,
    client_secret: Optional[str] = None,
    base_url: Optional[str] = None,
) -> str:
    """Obtain OAuth 2.0 Bearer token from CartonCloud API.
    
    Args:
        client_id: OAuth client ID (defaults to CARTONCLOUD_CLIENT_ID env var)
        client_secret: OAuth client secret (defaults to CARTONCLOUD_CLIENT_SECRET env var)
        base_url: API base URL (defaults to CARTONCLOUD_BASE_URL env var)
        
    Returns:
        Bearer token string
        
    Raises:
        ValueError: If credentials are missing
        requests.RequestException: If authentication fails
    """
    client_id = client_id or os.getenv("CARTONCLOUD_CLIENT_ID")
    client_secret = client_secret or os.getenv("CARTONCLOUD_CLIENT_SECRET")
    base_url = base_url or os.getenv("CARTONCLOUD_BASE_URL", "https://api.cartoncloud.com")
    
    if not client_id or not client_secret:
        raise ValueError("CartonCloud client ID and secret are required")
    
    token_url = f"{base_url}/oauth/token"
    
    data = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }
    
    logger.debug(f"Requesting token from {token_url}")
    response = requests.post(token_url, data=data, timeout=30)
    response.raise_for_status()
    
    token_data = response.json()
    return token_data["access_token"]
