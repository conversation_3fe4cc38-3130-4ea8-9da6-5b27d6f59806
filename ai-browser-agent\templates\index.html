{% extends "base.html" %}

{% block content %}
<div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <!-- Header -->
        <div class="p-4 border-b border-gray-200">
            <h1 class="text-xl font-bold text-gray-800">AI Browser Agent</h1>
            <button
                hx-post="/api/chat"
                hx-vals='{"title": "New Chat"}'
                hx-target="#chat-list"
                hx-swap="afterbegin"
                class="mt-3 w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                + New Chat
            </button>
        </div>

        <!-- Chat List -->
        <div id="chat-list" class="flex-1 overflow-y-auto custom-scrollbar p-2">
            {% for chat in chats %}
            <div class="chat-item p-3 mb-2 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                 hx-get="/api/chat/{{ chat.id }}"
                 hx-target="#chat-body"
                 hx-swap="innerHTML"
                 onclick="setActiveChat('{{ chat.id }}', '{{ chat.title }}')">
                <div class="font-medium text-gray-800 truncate">{{ chat.title }}</div>
                <div class="text-sm text-gray-500">{{ chat.created_at.strftime('%m/%d %H:%M') }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="flex-1 flex flex-col">
        <!-- Chat Header -->
        <div class="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
            <h2 id="chat-title" class="text-lg font-semibold text-gray-800">Select a chat or start a new one</h2>
            <div class="flex space-x-2">
                <button
                    id="undo-btn"
                    hx-delete=""
                    hx-target="#chat-body"
                    hx-swap="innerHTML"
                    class="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors disabled:opacity-50"
                    disabled>
                    Undo
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div id="chat-body" class="flex-1 overflow-y-auto custom-scrollbar p-4 space-y-4">
            <div class="text-center text-gray-500 mt-20">
                <div class="text-6xl mb-4">🤖</div>
                <p>Welcome to AI Browser Agent</p>
                <p class="text-sm">Start a conversation to begin</p>
            </div>
        </div>

        <!-- Message Input -->
        <div class="bg-white border-t border-gray-200 p-4">
            <form
                id="message-form"
                hx-post=""
                hx-target="#chat-body"
                hx-swap="beforeend"
                hx-on::after-request="startSSE()"
                class="flex space-x-3">
                <input
                    id="message-input"
                    name="content"
                    type="text"
                    placeholder="Type your message..."
                    class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    autocomplete="off">
                <button
                    type="submit"
                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Send
                </button>
            </form>
        </div>
    </div>
</div>

<script>
    let currentChatId = null;
    let currentEventSource = null;

    function setActiveChat(chatId, title) {
        currentChatId = chatId;
        document.getElementById('chat-title').textContent = title;

        // Update form action
        const form = document.getElementById('message-form');
        form.setAttribute('hx-post', `/api/chat/${chatId}`);

        // Update undo button
        const undoBtn = document.getElementById('undo-btn');
        undoBtn.setAttribute('hx-delete', `/api/chat/${chatId}/undo`);
        undoBtn.disabled = false;

        // Remove active class from all chat items
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('bg-blue-100', 'border-blue-300');
        });

        // Add active class to current chat
        event.target.closest('.chat-item').classList.add('bg-blue-100', 'border-blue-300');
    }

    function startSSE() {
        if (!currentChatId) return;

        // Close existing connection
        if (currentEventSource) {
            currentEventSource.close();
        }

        // Start new SSE connection
        currentEventSource = new EventSource(`/api/stream/${currentChatId}`);

        currentEventSource.onmessage = function(event) {
            if (event.data === '[DONE]') {
                currentEventSource.close();
                // Remove streaming indicator
                const streamingEl = document.getElementById('assistant-response');
                if (streamingEl) {
                    streamingEl.classList.remove('streaming');
                    streamingEl.removeAttribute('id');
                }
                return;
            }

            // Update the assistant response
            const responseEl = document.getElementById('assistant-response');
            if (responseEl) {
                const contentEl = responseEl.querySelector('.bg-gray-200');
                if (contentEl) {
                    if (contentEl.textContent === '🤖 Thinking...') {
                        contentEl.textContent = event.data;
                    } else {
                        contentEl.textContent += event.data;
                    }
                }
            }

            // Auto-scroll to bottom
            scrollToBottom();
        };

        currentEventSource.onerror = function(event) {
            console.error('SSE error:', event);
            currentEventSource.close();
            const streamingEl = document.getElementById('assistant-response');
            if (streamingEl) {
                streamingEl.classList.remove('streaming');
                const contentEl = streamingEl.querySelector('.bg-gray-200');
                if (contentEl && contentEl.textContent === '🤖 Thinking...') {
                    contentEl.textContent = '❌ Error occurred while processing your request.';
                }
            }
        };
    }

    // Handle new chat creation
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'chat-list' && evt.detail.xhr.status === 201) {
            // New chat was created, click on it
            const newChatItem = evt.target.firstElementChild;
            if (newChatItem) {
                newChatItem.click();
            }
        }
    });
</script>
{% endblock %}
