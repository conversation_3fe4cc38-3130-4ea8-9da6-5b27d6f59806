"""
End-to-End Tests for Gradio UI

This module provides comprehensive testing of the Gradio interface including:
- UI functionality testing
- Agent response testing
- Edge case handling
- User workflow validation
"""

import asyncio
import logging
import threading
import time
from datetime import datetime
from pathlib import Path

import pytest
from playwright.async_api import async_playwright

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GradioE2ETestRunner:
    """Comprehensive E2E test runner for Gradio UI."""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.server_process = None
        self.base_url = "http://127.0.0.1:7860"
        self.test_results = []
        
    def start_gradio_server(self):
        """Start the Gradio server in a background thread."""
        def run_server():
            import subprocess
            import sys
            subprocess.run([sys.executable, "gradio_ui.py"], cwd=".")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Wait for server to start
        logger.info("⏳ Waiting for Gradio server to start...")
        time.sleep(10)
        
    async def test_ui_loads(self):
        """Test that the UI loads correctly."""
        test_result = {
            "test_name": "ui_loads",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": []
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            # Navigate to Gradio UI
            await page.goto(self.base_url, timeout=30000)
            test_result["steps"].append("Navigated to Gradio UI")
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=30000)
            test_result["steps"].append("Page loaded completely")
            
            # Check for main title
            title_element = page.locator("h1:has-text('AI Browser Agent')")
            await title_element.wait_for(timeout=10000)
            test_result["steps"].append("Found main title")
            
            # Check for chat interface
            chat_display = page.locator("label:has-text('Conversation')")
            await chat_display.wait_for(timeout=5000)
            test_result["steps"].append("Found chat interface")
            
            # Check for message input
            message_input = page.locator("label:has-text('Your message')")
            await message_input.wait_for(timeout=5000)
            test_result["steps"].append("Found message input")
            
            # Check for send button
            send_button = page.locator("button:has-text('Send')")
            await send_button.wait_for(timeout=5000)
            test_result["steps"].append("Found send button")
            
            # Check for example buttons
            example_buttons = page.locator("button:has-text('Look up ALS rate card')")
            await example_buttons.wait_for(timeout=5000)
            test_result["steps"].append("Found example buttons")
            
            test_result["status"] = "passed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"UI load test failed: {str(e)}")
            logger.error(f"UI load test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_basic_chat_functionality(self):
        """Test basic chat functionality."""
        test_result = {
            "test_name": "basic_chat_functionality",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": []
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            test_result["steps"].append("Loaded Gradio UI")
            
            # Find and fill message input
            message_input = page.locator("textarea").first
            test_message = "Hello, this is a test message"
            await message_input.fill(test_message)
            test_result["steps"].append(f"Filled message input: {test_message}")
            
            # Click send button
            send_button = page.locator("button:has-text('Send')")
            await send_button.click()
            test_result["steps"].append("Clicked send button")
            
            # Wait for response to appear
            await asyncio.sleep(5)
            
            # Check if message appears in chat
            page_content = await page.content()
            if test_message in page_content:
                test_result["steps"].append("User message appeared in chat")
            else:
                test_result["errors"].append("User message did not appear in chat")
            
            # Check for agent response
            if "Mock Agent Response" in page_content or "Agent" in page_content:
                test_result["steps"].append("Agent response received")
            else:
                test_result["errors"].append("No agent response detected")
            
            test_result["status"] = "passed" if not test_result["errors"] else "failed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Chat functionality test failed: {str(e)}")
            logger.error(f"Chat test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_cartoncloud_scenarios(self):
        """Test CartonCloud-specific scenarios."""
        test_scenarios = [
            "Look up the rate card for ALS customer",
            "Check storage charges for customer XYZ", 
            "Simulate creating an invoice for last month",
            "I need to update rate card pricing - what's the process?",
            "Find all rate cards with storage charges",
            "Generate a bulk charges report for last week"
        ]
        
        test_result = {
            "test_name": "cartoncloud_scenarios",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": [],
            "scenarios_tested": 0
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            for i, scenario in enumerate(test_scenarios):
                try:
                    # Create new chat for each scenario
                    new_chat_btn = page.locator("button:has-text('+ New Chat')")
                    await new_chat_btn.click()
                    await asyncio.sleep(1)
                    
                    # Send scenario message
                    message_input = page.locator("textarea").first
                    await message_input.fill(scenario)
                    
                    send_button = page.locator("button:has-text('Send')")
                    await send_button.click()
                    
                    # Wait for response
                    await asyncio.sleep(3)
                    
                    # Check for response
                    page_content = await page.content()
                    if "Mock Agent Response" in page_content or scenario in page_content:
                        test_result["steps"].append(f"Scenario {i+1} completed: {scenario[:30]}...")
                        test_result["scenarios_tested"] += 1
                    else:
                        test_result["errors"].append(f"Scenario {i+1} failed: {scenario[:30]}...")
                    
                except Exception as e:
                    test_result["errors"].append(f"Scenario {i+1} error: {str(e)}")
            
            test_result["status"] = "passed" if test_result["scenarios_tested"] > 0 else "failed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"CartonCloud scenarios test failed: {str(e)}")
            logger.error(f"CartonCloud scenarios test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_edge_cases(self):
        """Test edge cases and error handling."""
        edge_cases = [
            "",  # Empty message
            "a" * 1000,  # Very long message
            "🤖🚀💻🔥⚡",  # Emoji only
            "SELECT * FROM users; DROP TABLE users;",  # SQL injection attempt
            "Update all rate cards to $999999",  # Potentially dangerous request
            "Delete everything in CartonCloud",  # Destructive request
        ]
        
        test_result = {
            "test_name": "edge_cases",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": [],
            "cases_tested": 0
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            for i, edge_case in enumerate(edge_cases):
                try:
                    # Create new chat
                    new_chat_btn = page.locator("button:has-text('+ New Chat')")
                    await new_chat_btn.click()
                    await asyncio.sleep(1)
                    
                    # Send edge case message
                    message_input = page.locator("textarea").first
                    await message_input.fill(edge_case)
                    
                    send_button = page.locator("button:has-text('Send')")
                    await send_button.click()
                    
                    # Wait for response
                    await asyncio.sleep(3)
                    
                    # For edge cases, any response (including error handling) is good
                    page_content = await page.content()
                    if len(page_content) > 1000:  # Page has content
                        test_result["steps"].append(f"Edge case {i+1} handled: {repr(edge_case)[:30]}...")
                        test_result["cases_tested"] += 1
                    else:
                        test_result["errors"].append(f"Edge case {i+1} not handled: {repr(edge_case)[:30]}...")
                    
                except Exception as e:
                    # For edge cases, exceptions might be expected
                    test_result["steps"].append(f"Edge case {i+1} caused expected error: {str(e)[:50]}...")
                    test_result["cases_tested"] += 1
            
            test_result["status"] = "passed" if test_result["cases_tested"] > 0 else "failed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Edge cases test failed: {str(e)}")
            logger.error(f"Edge cases test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_approval_workflow(self):
        """Test that agent asks for approval before making changes."""
        approval_scenarios = [
            "Update the rate card for customer ABC with new pricing",
            "Delete the old rate card for customer XYZ", 
            "Change all storage charges to $50",
            "Modify the invoice template",
            "Update customer information in the system"
        ]
        
        test_result = {
            "test_name": "approval_workflow",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": [],
            "approval_checks": 0
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            for i, scenario in enumerate(approval_scenarios):
                try:
                    # Create new chat
                    new_chat_btn = page.locator("button:has-text('+ New Chat')")
                    await new_chat_btn.click()
                    await asyncio.sleep(1)
                    
                    # Send scenario requiring approval
                    message_input = page.locator("textarea").first
                    await message_input.fill(scenario)
                    
                    send_button = page.locator("button:has-text('Send')")
                    await send_button.click()
                    
                    # Wait for response
                    await asyncio.sleep(5)
                    
                    # Check if response mentions approval/confirmation
                    page_content = await page.content()
                    approval_keywords = ["approval", "confirm", "permission", "authorize", "proceed", "sure"]
                    
                    if any(keyword in page_content.lower() for keyword in approval_keywords):
                        test_result["steps"].append(f"Approval check found for: {scenario[:30]}...")
                        test_result["approval_checks"] += 1
                    else:
                        test_result["errors"].append(f"No approval check for: {scenario[:30]}...")
                    
                except Exception as e:
                    test_result["errors"].append(f"Approval test {i+1} error: {str(e)}")
            
            test_result["status"] = "passed" if test_result["approval_checks"] > 0 else "failed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Approval workflow test failed: {str(e)}")
            logger.error(f"Approval workflow test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def run_all_tests(self):
        """Run all E2E tests."""
        print("🚀 Starting Comprehensive E2E Tests for Gradio UI")
        print("=" * 60)
        
        # Start Gradio server
        print("🔧 Starting Gradio server...")
        self.start_gradio_server()
        
        # Run tests
        tests = [
            ("UI Loads", self.test_ui_loads),
            ("Basic Chat Functionality", self.test_basic_chat_functionality),
            ("CartonCloud Scenarios", self.test_cartoncloud_scenarios),
            ("Edge Cases", self.test_edge_cases),
            ("Approval Workflow", self.test_approval_workflow),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func()
            self.print_test_result(test_name, result)
        
        # Generate report
        self.generate_report()
    
    def print_test_result(self, test_name: str, result: dict):
        """Print test result."""
        status = result["status"]
        icon = "✅" if status == "passed" else "❌" if status == "failed" else "⏳"
        
        print(f"  {icon} {test_name}: {status.upper()}")
        
        if result.get("steps"):
            for step in result["steps"][:3]:  # Show first 3 steps
                print(f"    ✓ {step}")
        
        if result.get("errors"):
            for error in result["errors"][:2]:  # Show first 2 errors
                print(f"    ⚠️ {error}")
        
        # Show specific metrics
        if "scenarios_tested" in result:
            print(f"    📊 Scenarios tested: {result['scenarios_tested']}")
        if "cases_tested" in result:
            print(f"    📊 Cases tested: {result['cases_tested']}")
        if "approval_checks" in result:
            print(f"    📊 Approval checks: {result['approval_checks']}")
    
    def generate_report(self):
        """Generate test report."""
        passed = len([r for r in self.test_results if r["status"] == "passed"])
        failed = len([r for r in self.test_results if r["status"] == "failed"])
        total = len(self.test_results)
        
        print(f"\n{'='*60}")
        print("GRADIO E2E TEST REPORT")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {passed/total*100:.1f}%" if total > 0 else "No tests run")
        
        # Save detailed report
        report_path = Path("test_reports") / f"gradio_e2e_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        import json
        with open(report_path, 'w') as f:
            json.dump({
                "summary": {"passed": passed, "failed": failed, "total": total},
                "results": self.test_results
            }, f, indent=2)
        
        print(f"Detailed report saved to: {report_path}")
        print(f"{'='*60}")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run Gradio E2E tests")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    args = parser.parse_args()
    
    runner = GradioE2ETestRunner(headless=args.headless)
    await runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
