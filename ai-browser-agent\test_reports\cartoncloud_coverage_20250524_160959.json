{"summary": {"total": 5, "passed": 4, "partial": 1, "failed": 0, "coverage_score": 94.0, "assessment": "VERY GOOD - Near Complete Coverage"}, "results": [{"test_name": "concept_coverage", "status": "passed", "start_time": "2025-05-24T16:09:52.643903", "errors": [], "concepts_tested": ["✅ rate_card", "✅ customer", "✅ storage_charge", "✅ purchase_order", "✅ sales_order", "✅ invoice", "✅ bulk_charges_report", "✅ warehouse"], "coverage_percentage": 100.0, "end_time": "2025-05-24T16:09:52.659374"}, {"test_name": "operation_understanding", "status": "passed", "start_time": "2025-05-24T16:09:52.661380", "errors": [], "operations_tested": ["✅ Look up ALS rate card -> search", "✅ Create new customer record -> create", "✅ Update rate card pricing -> update", "✅ Delete old purchase order -> delete", "✅ Generate bulk charges report -> report", "✅ Show customer details -> read"], "accuracy_percentage": 100.0, "end_time": "2025-05-24T16:09:52.661380"}, {"test_name": "approval_workflow", "status": "partial", "start_time": "2025-05-24T16:09:52.661380", "errors": ["Look up customer details: Expected approval=False, got True", "Create new customer: Expected approval=False, got True"], "approval_tests": ["✅ Update rate card pricing: Approval required", "✅ Delete rate card: Approval required", "✅ Generate report: No approval needed", "✅ Modify invoice: Approval required"], "accuracy_percentage": 66.66666666666666, "end_time": "2025-05-24T16:09:52.661380"}, {"test_name": "business_scenarios", "status": "passed", "start_time": "2025-05-24T16:09:52.664258", "errors": [], "scenarios_tested": ["✅ Look up the rate card for ALS customer...", "   ✅ Contains CartonCloud concepts", "✅ Check storage charges for customer XYZ f...", "   ✅ Contains CartonCloud concepts", "✅ Update rate card pricing for customer AB...", "   ✅ Contains CartonCloud concepts", "   ✅ Correctly identifies approval needed", "✅ Create a new purchase order for supplier...", "   ✅ Contains CartonCloud concepts", "✅ Generate bulk charges report for January...", "   ✅ Contains CartonCloud concepts", "✅ Simulate invoice for customer GHI...", "   ✅ Contains CartonCloud concepts", "✅ Find all customers with active rate card...", "   ✅ Contains CartonCloud concepts", "✅ Show warehouse capacity and utilization...", "   ✅ Contains CartonCloud concepts", "✅ Delete old purchase order PO-2023-001...", "   ✅ Contains CartonCloud concepts", "   ✅ Correctly identifies approval needed", "✅ Update customer contact information for ...", "   ✅ Contains CartonCloud concepts", "   ✅ Correctly identifies approval needed"], "success_rate": 100.0, "end_time": "2025-05-24T16:09:59.234797"}, {"test_name": "edge_cases_cartoncloud", "status": "passed", "start_time": "2025-05-24T16:09:59.236810", "errors": [], "edge_cases_tested": ["✅ rate card with no customer", "✅ invoice for non-existent customer", "✅ storage charges for empty warehouse", "✅ purchase order with negative quantities", "✅ rate card with past effective date", "✅ customer with duplicate code", "✅ warehouse with zero capacity", "✅ charges with invalid currency"], "handling_rate": 100.0, "end_time": "2025-05-24T16:09:59.236810"}], "timestamp": "2025-05-24T16:09:59.236810"}