"""Database models for chat sessions and messages."""

from datetime import datetime

from sqlmodel import Field
from sqlmodel import Relationship
from sqlmodel import SQLModel


class Chat(SQLModel, table=True):
    """Chat session model."""

    id: str = Field(primary_key=True)  # UUID4
    title: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationship to messages
    messages: list["Message"] = Relationship(back_populates="chat")


class Message(SQLModel, table=True):
    """Message model for chat history."""

    id: str = Field(primary_key=True)  # UUID4
    chat_id: str = Field(foreign_key="chat.id")
    role: str  # 'user' | 'assistant' | 'system'
    content: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationship to chat
    chat: Chat | None = Relationship(back_populates="messages")
