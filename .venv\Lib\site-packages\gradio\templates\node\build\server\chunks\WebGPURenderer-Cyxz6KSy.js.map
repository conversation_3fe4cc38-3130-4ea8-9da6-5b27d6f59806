{"version": 3, "file": "WebGPURenderer-Cyxz6KSy.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/WebGPURenderer.js"], "sourcesContent": ["import { S as State, g as getTextureBatchBindGroup, E as ExtensionType, f as fastCopy, D as DOMAdapter, b as STENCIL_MODES, B as <PERSON>ufferUsage, c as Buffer, d as BindGroup, h as createIdFromString, w as warn, i as CLEAR, j as CanvasSource, k as TextureSource, l as UniformGroup, m as CanvasPool, M as Matrix, n as compileHighShaderGpuProgram, o as colorBit, p as generateTextureBatchBit, q as getMaxTexturesPerBatch, s as roundPixelsBit, t as Shader, u as Texture, e as extensions, A as AbstractRenderer, R as RendererType } from \"./Index3.js\";\nimport { l as localUniformBitGroup2, a as localUniformBit } from \"./colorToUniform.js\";\nimport { c as createUboSyncFunction, u as uboSyncFunctionsWGSL, U as UboSystem, B as BufferResource, G as GpuStencilModesToPixi, e as ensureAttributes, R as RenderTargetSystem, t as textureBit, S as SharedSystems, a as SharedRenderPipes } from \"./SharedSystems.js\";\nconst tempState = State.for2d();\nclass GpuBatchAdaptor {\n  start(batchPipe, geometry, shader) {\n    const renderer = batchPipe.renderer;\n    const encoder = renderer.encoder;\n    const program = shader.gpuProgram;\n    this._shader = shader;\n    this._geometry = geometry;\n    encoder.setGeometry(geometry, program);\n    tempState.blendMode = \"normal\";\n    renderer.pipeline.getPipeline(\n      geometry,\n      program,\n      tempState\n    );\n    const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n    encoder.resetBindGroup(1);\n    encoder.setBindGroup(0, globalUniformsBindGroup, program);\n  }\n  execute(batchPipe, batch) {\n    const program = this._shader.gpuProgram;\n    const renderer = batchPipe.renderer;\n    const encoder = renderer.encoder;\n    if (!batch.bindGroup) {\n      const textureBatch = batch.textures;\n      batch.bindGroup = getTextureBatchBindGroup(textureBatch.textures, textureBatch.count);\n    }\n    tempState.blendMode = batch.blendMode;\n    const gpuBindGroup = renderer.bindGroup.getBindGroup(\n      batch.bindGroup,\n      program,\n      1\n    );\n    const pipeline = renderer.pipeline.getPipeline(\n      this._geometry,\n      program,\n      tempState,\n      batch.topology\n    );\n    batch.bindGroup._touch(renderer.textureGC.count);\n    encoder.setPipeline(pipeline);\n    encoder.renderPassEncoder.setBindGroup(1, gpuBindGroup);\n    encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n  }\n}\nGpuBatchAdaptor.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"batch\"\n};\nclass BindGroupSystem {\n  constructor(renderer) {\n    this._hash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_hash\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  getBindGroup(bindGroup, program, groupIndex) {\n    bindGroup._updateKey();\n    const gpuBindGroup = this._hash[bindGroup._key] || this._createBindGroup(bindGroup, program, groupIndex);\n    return gpuBindGroup;\n  }\n  _createBindGroup(group, program, groupIndex) {\n    const device = this._gpu.device;\n    const groupLayout = program.layout[groupIndex];\n    const entries = [];\n    const renderer = this._renderer;\n    for (const j in groupLayout) {\n      const resource = group.resources[j] ?? group.resources[groupLayout[j]];\n      let gpuResource;\n      if (resource._resourceType === \"uniformGroup\") {\n        const uniformGroup = resource;\n        renderer.ubo.updateUniformGroup(uniformGroup);\n        const buffer = uniformGroup.buffer;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(buffer),\n          offset: 0,\n          size: buffer.descriptor.size\n        };\n      } else if (resource._resourceType === \"buffer\") {\n        const buffer = resource;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(buffer),\n          offset: 0,\n          size: buffer.descriptor.size\n        };\n      } else if (resource._resourceType === \"bufferResource\") {\n        const bufferResource = resource;\n        gpuResource = {\n          buffer: renderer.buffer.getGPUBuffer(bufferResource.buffer),\n          offset: bufferResource.offset,\n          size: bufferResource.size\n        };\n      } else if (resource._resourceType === \"textureSampler\") {\n        const sampler = resource;\n        gpuResource = renderer.texture.getGpuSampler(sampler);\n      } else if (resource._resourceType === \"textureSource\") {\n        const texture = resource;\n        gpuResource = renderer.texture.getGpuSource(texture).createView({});\n      }\n      entries.push({\n        binding: groupLayout[j],\n        resource: gpuResource\n      });\n    }\n    const layout = renderer.shader.getProgramData(program).bindGroups[groupIndex];\n    const gpuBindGroup = device.createBindGroup({\n      layout,\n      entries\n    });\n    this._hash[group._key] = gpuBindGroup;\n    return gpuBindGroup;\n  }\n  destroy() {\n    for (const key of Object.keys(this._hash)) {\n      this._hash[key] = null;\n    }\n    this._hash = null;\n    this._renderer = null;\n  }\n}\nBindGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"bindGroup\"\n};\nclass GpuBufferSystem {\n  constructor(renderer) {\n    this._gpuBuffers = /* @__PURE__ */ Object.create(null);\n    this._managedBuffers = [];\n    renderer.renderableGC.addManagedHash(this, \"_gpuBuffers\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  getGPUBuffer(buffer) {\n    return this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n  }\n  updateBuffer(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid] || this.createGPUBuffer(buffer);\n    const data = buffer.data;\n    if (buffer._updateID && data) {\n      buffer._updateID = 0;\n      this._gpu.device.queue.writeBuffer(\n        gpuBuffer,\n        0,\n        data.buffer,\n        0,\n        // round to the nearest 4 bytes\n        (buffer._updateSize || data.byteLength) + 3 & ~3\n      );\n    }\n    return gpuBuffer;\n  }\n  /** dispose all WebGL resources of all managed buffers */\n  destroyAll() {\n    for (const id in this._gpuBuffers) {\n      this._gpuBuffers[id].destroy();\n    }\n    this._gpuBuffers = {};\n  }\n  createGPUBuffer(buffer) {\n    if (!this._gpuBuffers[buffer.uid]) {\n      buffer.on(\"update\", this.updateBuffer, this);\n      buffer.on(\"change\", this.onBufferChange, this);\n      buffer.on(\"destroy\", this.onBufferDestroy, this);\n      this._managedBuffers.push(buffer);\n    }\n    const gpuBuffer = this._gpu.device.createBuffer(buffer.descriptor);\n    buffer._updateID = 0;\n    if (buffer.data) {\n      fastCopy(buffer.data.buffer, gpuBuffer.getMappedRange());\n      gpuBuffer.unmap();\n    }\n    this._gpuBuffers[buffer.uid] = gpuBuffer;\n    return gpuBuffer;\n  }\n  onBufferChange(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid];\n    gpuBuffer.destroy();\n    buffer._updateID = 0;\n    this._gpuBuffers[buffer.uid] = this.createGPUBuffer(buffer);\n  }\n  /**\n   * Disposes buffer\n   * @param buffer - buffer with data\n   */\n  onBufferDestroy(buffer) {\n    this._managedBuffers.splice(this._managedBuffers.indexOf(buffer), 1);\n    this._destroyBuffer(buffer);\n  }\n  destroy() {\n    this._managedBuffers.forEach((buffer) => this._destroyBuffer(buffer));\n    this._managedBuffers = null;\n    this._gpuBuffers = null;\n  }\n  _destroyBuffer(buffer) {\n    const gpuBuffer = this._gpuBuffers[buffer.uid];\n    gpuBuffer.destroy();\n    buffer.off(\"update\", this.updateBuffer, this);\n    buffer.off(\"change\", this.onBufferChange, this);\n    buffer.off(\"destroy\", this.onBufferDestroy, this);\n    this._gpuBuffers[buffer.uid] = null;\n  }\n}\nGpuBufferSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"buffer\"\n};\nclass UboBatch {\n  constructor({ minUniformOffsetAlignment: minUniformOffsetAlignment2 }) {\n    this._minUniformOffsetAlignment = 256;\n    this.byteIndex = 0;\n    this._minUniformOffsetAlignment = minUniformOffsetAlignment2;\n    this.data = new Float32Array(65535);\n  }\n  clear() {\n    this.byteIndex = 0;\n  }\n  addEmptyGroup(size) {\n    if (size > this._minUniformOffsetAlignment / 4) {\n      throw new Error(`UniformBufferBatch: array is too large: ${size * 4}`);\n    }\n    const start = this.byteIndex;\n    let newSize = start + size * 4;\n    newSize = Math.ceil(newSize / this._minUniformOffsetAlignment) * this._minUniformOffsetAlignment;\n    if (newSize > this.data.length * 4) {\n      throw new Error(\"UniformBufferBatch: ubo batch got too big\");\n    }\n    this.byteIndex = newSize;\n    return start;\n  }\n  addGroup(array) {\n    const offset = this.addEmptyGroup(array.length);\n    for (let i = 0; i < array.length; i++) {\n      this.data[offset / 4 + i] = array[i];\n    }\n    return offset;\n  }\n  destroy() {\n    this.data = null;\n  }\n}\nclass GpuColorMaskSystem {\n  constructor(renderer) {\n    this._colorMaskCache = 15;\n    this._renderer = renderer;\n  }\n  setMask(colorMask) {\n    if (this._colorMaskCache === colorMask)\n      return;\n    this._colorMaskCache = colorMask;\n    this._renderer.pipeline.setColorMask(colorMask);\n  }\n  destroy() {\n    this._renderer = null;\n    this._colorMaskCache = null;\n  }\n}\nGpuColorMaskSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"colorMask\"\n};\nclass GpuDeviceSystem {\n  /**\n   * @param {WebGPURenderer} renderer - The renderer this System works for.\n   */\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  async init(options) {\n    if (this._initPromise)\n      return this._initPromise;\n    this._initPromise = this._createDeviceAndAdaptor(options).then((gpu) => {\n      this.gpu = gpu;\n      this._renderer.runners.contextChange.emit(this.gpu);\n    });\n    return this._initPromise;\n  }\n  /**\n   * Handle the context change event\n   * @param gpu\n   */\n  contextChange(gpu) {\n    this._renderer.gpu = gpu;\n  }\n  /**\n   * Helper class to create a WebGL Context\n   * @param {object} options - An options object that gets passed in to the canvas element containing the\n   *    context attributes\n   * @see https://developer.mozilla.org/en/docs/Web/API/HTMLCanvasElement/getContext\n   * @returns {WebGLRenderingContext} the WebGL context\n   */\n  async _createDeviceAndAdaptor(options) {\n    const adapter = await DOMAdapter.get().getNavigator().gpu.requestAdapter({\n      powerPreference: options.powerPreference,\n      forceFallbackAdapter: options.forceFallbackAdapter\n    });\n    const requiredFeatures = [\n      \"texture-compression-bc\",\n      \"texture-compression-astc\",\n      \"texture-compression-etc2\"\n    ].filter((feature) => adapter.features.has(feature));\n    const device = await adapter.requestDevice({\n      requiredFeatures\n    });\n    return { adapter, device };\n  }\n  destroy() {\n    this.gpu = null;\n    this._renderer = null;\n  }\n}\nGpuDeviceSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"device\"\n};\nGpuDeviceSystem.defaultOptions = {\n  /**\n   * {@link WebGPUOptions.powerPreference}\n   * @default default\n   */\n  powerPreference: void 0,\n  /**\n   * Force the use of the fallback adapter\n   * @default false\n   */\n  forceFallbackAdapter: false\n};\nclass GpuEncoderSystem {\n  constructor(renderer) {\n    this._boundBindGroup = /* @__PURE__ */ Object.create(null);\n    this._boundVertexBuffer = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n  }\n  renderStart() {\n    this.commandFinished = new Promise((resolve) => {\n      this._resolveCommandFinished = resolve;\n    });\n    this.commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n  }\n  beginRenderPass(gpuRenderTarget) {\n    this.endRenderPass();\n    this._clearCache();\n    this.renderPassEncoder = this.commandEncoder.beginRenderPass(gpuRenderTarget.descriptor);\n  }\n  endRenderPass() {\n    if (this.renderPassEncoder) {\n      this.renderPassEncoder.end();\n    }\n    this.renderPassEncoder = null;\n  }\n  setViewport(viewport) {\n    this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n  }\n  setPipelineFromGeometryProgramAndState(geometry, program, state, topology) {\n    const pipeline = this._renderer.pipeline.getPipeline(geometry, program, state, topology);\n    this.setPipeline(pipeline);\n  }\n  setPipeline(pipeline) {\n    if (this._boundPipeline === pipeline)\n      return;\n    this._boundPipeline = pipeline;\n    this.renderPassEncoder.setPipeline(pipeline);\n  }\n  _setVertexBuffer(index, buffer) {\n    if (this._boundVertexBuffer[index] === buffer)\n      return;\n    this._boundVertexBuffer[index] = buffer;\n    this.renderPassEncoder.setVertexBuffer(index, this._renderer.buffer.updateBuffer(buffer));\n  }\n  _setIndexBuffer(buffer) {\n    if (this._boundIndexBuffer === buffer)\n      return;\n    this._boundIndexBuffer = buffer;\n    const indexFormat = buffer.data.BYTES_PER_ELEMENT === 2 ? \"uint16\" : \"uint32\";\n    this.renderPassEncoder.setIndexBuffer(this._renderer.buffer.updateBuffer(buffer), indexFormat);\n  }\n  resetBindGroup(index) {\n    this._boundBindGroup[index] = null;\n  }\n  setBindGroup(index, bindGroup, program) {\n    if (this._boundBindGroup[index] === bindGroup)\n      return;\n    this._boundBindGroup[index] = bindGroup;\n    bindGroup._touch(this._renderer.textureGC.count);\n    const gpuBindGroup = this._renderer.bindGroup.getBindGroup(bindGroup, program, index);\n    this.renderPassEncoder.setBindGroup(index, gpuBindGroup);\n  }\n  setGeometry(geometry, program) {\n    const buffersToBind = this._renderer.pipeline.getBufferNamesToBind(geometry, program);\n    for (const i in buffersToBind) {\n      this._setVertexBuffer(i, geometry.attributes[buffersToBind[i]].buffer);\n    }\n    if (geometry.indexBuffer) {\n      this._setIndexBuffer(geometry.indexBuffer);\n    }\n  }\n  _setShaderBindGroups(shader, skipSync) {\n    for (const i in shader.groups) {\n      const bindGroup = shader.groups[i];\n      if (!skipSync) {\n        this._syncBindGroup(bindGroup);\n      }\n      this.setBindGroup(i, bindGroup, shader.gpuProgram);\n    }\n  }\n  _syncBindGroup(bindGroup) {\n    for (const j in bindGroup.resources) {\n      const resource = bindGroup.resources[j];\n      if (resource.isUniformGroup) {\n        this._renderer.ubo.updateUniformGroup(resource);\n      }\n    }\n  }\n  draw(options) {\n    const { geometry, shader, state, topology, size, start, instanceCount, skipSync } = options;\n    this.setPipelineFromGeometryProgramAndState(geometry, shader.gpuProgram, state, topology);\n    this.setGeometry(geometry, shader.gpuProgram);\n    this._setShaderBindGroups(shader, skipSync);\n    if (geometry.indexBuffer) {\n      this.renderPassEncoder.drawIndexed(\n        size || geometry.indexBuffer.data.length,\n        instanceCount ?? geometry.instanceCount,\n        start || 0\n      );\n    } else {\n      this.renderPassEncoder.draw(size || geometry.getSize(), instanceCount ?? geometry.instanceCount, start || 0);\n    }\n  }\n  finishRenderPass() {\n    if (this.renderPassEncoder) {\n      this.renderPassEncoder.end();\n      this.renderPassEncoder = null;\n    }\n  }\n  postrender() {\n    this.finishRenderPass();\n    this._gpu.device.queue.submit([this.commandEncoder.finish()]);\n    this._resolveCommandFinished();\n    this.commandEncoder = null;\n  }\n  // restores a render pass if finishRenderPass was called\n  // not optimised as really used for debugging!\n  // used when we want to stop drawing and log a texture..\n  restoreRenderPass() {\n    const descriptor = this._renderer.renderTarget.adaptor.getDescriptor(\n      this._renderer.renderTarget.renderTarget,\n      false,\n      [0, 0, 0, 1]\n    );\n    this.renderPassEncoder = this.commandEncoder.beginRenderPass(descriptor);\n    const boundPipeline = this._boundPipeline;\n    const boundVertexBuffer = { ...this._boundVertexBuffer };\n    const boundIndexBuffer = this._boundIndexBuffer;\n    const boundBindGroup = { ...this._boundBindGroup };\n    this._clearCache();\n    const viewport = this._renderer.renderTarget.viewport;\n    this.renderPassEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n    this.setPipeline(boundPipeline);\n    for (const i in boundVertexBuffer) {\n      this._setVertexBuffer(i, boundVertexBuffer[i]);\n    }\n    for (const i in boundBindGroup) {\n      this.setBindGroup(i, boundBindGroup[i], null);\n    }\n    this._setIndexBuffer(boundIndexBuffer);\n  }\n  _clearCache() {\n    for (let i = 0; i < 16; i++) {\n      this._boundBindGroup[i] = null;\n      this._boundVertexBuffer[i] = null;\n    }\n    this._boundIndexBuffer = null;\n    this._boundPipeline = null;\n  }\n  destroy() {\n    this._renderer = null;\n    this._gpu = null;\n    this._boundBindGroup = null;\n    this._boundVertexBuffer = null;\n    this._boundIndexBuffer = null;\n    this._boundPipeline = null;\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n}\nGpuEncoderSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"encoder\",\n  priority: 1\n};\nclass GpuStencilSystem {\n  constructor(renderer) {\n    this._renderTargetStencilState = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    renderer.renderTarget.onRenderTargetChange.add(this);\n  }\n  onRenderTargetChange(renderTarget) {\n    let stencilState = this._renderTargetStencilState[renderTarget.uid];\n    if (!stencilState) {\n      stencilState = this._renderTargetStencilState[renderTarget.uid] = {\n        stencilMode: STENCIL_MODES.DISABLED,\n        stencilReference: 0\n      };\n    }\n    this._activeRenderTarget = renderTarget;\n    this.setStencilMode(stencilState.stencilMode, stencilState.stencilReference);\n  }\n  setStencilMode(stencilMode, stencilReference) {\n    const stencilState = this._renderTargetStencilState[this._activeRenderTarget.uid];\n    stencilState.stencilMode = stencilMode;\n    stencilState.stencilReference = stencilReference;\n    const renderer = this._renderer;\n    renderer.pipeline.setStencilMode(stencilMode);\n    renderer.encoder.renderPassEncoder.setStencilReference(stencilReference);\n  }\n  destroy() {\n    this._renderer.renderTarget.onRenderTargetChange.remove(this);\n    this._renderer = null;\n    this._activeRenderTarget = null;\n    this._renderTargetStencilState = null;\n  }\n}\nGpuStencilSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"stencil\"\n};\nconst WGSL_ALIGN_SIZE_DATA = {\n  i32: { align: 4, size: 4 },\n  u32: { align: 4, size: 4 },\n  f32: { align: 4, size: 4 },\n  f16: { align: 2, size: 2 },\n  \"vec2<i32>\": { align: 8, size: 8 },\n  \"vec2<u32>\": { align: 8, size: 8 },\n  \"vec2<f32>\": { align: 8, size: 8 },\n  \"vec2<f16>\": { align: 4, size: 4 },\n  \"vec3<i32>\": { align: 16, size: 12 },\n  \"vec3<u32>\": { align: 16, size: 12 },\n  \"vec3<f32>\": { align: 16, size: 12 },\n  \"vec3<f16>\": { align: 8, size: 6 },\n  \"vec4<i32>\": { align: 16, size: 16 },\n  \"vec4<u32>\": { align: 16, size: 16 },\n  \"vec4<f32>\": { align: 16, size: 16 },\n  \"vec4<f16>\": { align: 8, size: 8 },\n  \"mat2x2<f32>\": { align: 8, size: 16 },\n  \"mat2x2<f16>\": { align: 4, size: 8 },\n  \"mat3x2<f32>\": { align: 8, size: 24 },\n  \"mat3x2<f16>\": { align: 4, size: 12 },\n  \"mat4x2<f32>\": { align: 8, size: 32 },\n  \"mat4x2<f16>\": { align: 4, size: 16 },\n  \"mat2x3<f32>\": { align: 16, size: 32 },\n  \"mat2x3<f16>\": { align: 8, size: 16 },\n  \"mat3x3<f32>\": { align: 16, size: 48 },\n  \"mat3x3<f16>\": { align: 8, size: 24 },\n  \"mat4x3<f32>\": { align: 16, size: 64 },\n  \"mat4x3<f16>\": { align: 8, size: 32 },\n  \"mat2x4<f32>\": { align: 16, size: 32 },\n  \"mat2x4<f16>\": { align: 8, size: 16 },\n  \"mat3x4<f32>\": { align: 16, size: 48 },\n  \"mat3x4<f16>\": { align: 8, size: 24 },\n  \"mat4x4<f32>\": { align: 16, size: 64 },\n  \"mat4x4<f16>\": { align: 8, size: 32 }\n};\nfunction createUboElementsWGSL(uniformData) {\n  const uboElements = uniformData.map((data) => ({\n    data,\n    offset: 0,\n    size: 0\n  }));\n  let offset = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    let size = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].size;\n    const align = WGSL_ALIGN_SIZE_DATA[uboElement.data.type].align;\n    if (!WGSL_ALIGN_SIZE_DATA[uboElement.data.type]) {\n      throw new Error(`[Pixi.js] WebGPU UniformBuffer: Unknown type ${uboElement.data.type}`);\n    }\n    if (uboElement.data.size > 1) {\n      size = Math.max(size, align) * uboElement.data.size;\n    }\n    offset = Math.ceil(offset / align) * align;\n    uboElement.size = size;\n    uboElement.offset = offset;\n    offset += size;\n  }\n  offset = Math.ceil(offset / 16) * 16;\n  return { uboElements, size: offset };\n}\nfunction generateArraySyncWGSL(uboElement, offsetToAdd) {\n  const { size, align } = WGSL_ALIGN_SIZE_DATA[uboElement.data.type];\n  const remainder = (align - size) / 4;\n  const data = uboElement.data.type.indexOf(\"i32\") >= 0 ? \"dataInt32\" : \"data\";\n  return `\n         v = uv.${uboElement.data.name};\n         ${offsetToAdd !== 0 ? `offset += ${offsetToAdd};` : \"\"}\n\n         arrayOffset = offset;\n\n         t = 0;\n\n         for(var i=0; i < ${uboElement.data.size * (size / 4)}; i++)\n         {\n             for(var j = 0; j < ${size / 4}; j++)\n             {\n                 ${data}[arrayOffset++] = v[t++];\n             }\n             ${remainder !== 0 ? `arrayOffset += ${remainder};` : \"\"}\n         }\n     `;\n}\nfunction createUboSyncFunctionWGSL(uboElements) {\n  return createUboSyncFunction(\n    uboElements,\n    \"uboWgsl\",\n    generateArraySyncWGSL,\n    uboSyncFunctionsWGSL\n  );\n}\nclass GpuUboSystem extends UboSystem {\n  constructor() {\n    super({\n      createUboElements: createUboElementsWGSL,\n      generateUboSync: createUboSyncFunctionWGSL\n    });\n  }\n}\nGpuUboSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"ubo\"\n};\nconst minUniformOffsetAlignment = 128;\nclass GpuUniformBatchPipe {\n  constructor(renderer) {\n    this._bindGroupHash = /* @__PURE__ */ Object.create(null);\n    this._buffers = [];\n    this._bindGroups = [];\n    this._bufferResources = [];\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_bindGroupHash\");\n    this._batchBuffer = new UboBatch({ minUniformOffsetAlignment });\n    const totalBuffers = 256 / minUniformOffsetAlignment;\n    for (let i = 0; i < totalBuffers; i++) {\n      let usage = BufferUsage.UNIFORM | BufferUsage.COPY_DST;\n      if (i === 0)\n        usage |= BufferUsage.COPY_SRC;\n      this._buffers.push(new Buffer({\n        data: this._batchBuffer.data,\n        usage\n      }));\n    }\n  }\n  renderEnd() {\n    this._uploadBindGroups();\n    this._resetBindGroups();\n  }\n  _resetBindGroups() {\n    for (const i in this._bindGroupHash) {\n      this._bindGroupHash[i] = null;\n    }\n    this._batchBuffer.clear();\n  }\n  // just works for single bind groups for now\n  getUniformBindGroup(group, duplicate) {\n    if (!duplicate && this._bindGroupHash[group.uid]) {\n      return this._bindGroupHash[group.uid];\n    }\n    this._renderer.ubo.ensureUniformGroup(group);\n    const data = group.buffer.data;\n    const offset = this._batchBuffer.addEmptyGroup(data.length);\n    this._renderer.ubo.syncUniformGroup(group, this._batchBuffer.data, offset / 4);\n    this._bindGroupHash[group.uid] = this._getBindGroup(offset / minUniformOffsetAlignment);\n    return this._bindGroupHash[group.uid];\n  }\n  getUboResource(group) {\n    this._renderer.ubo.updateUniformGroup(group);\n    const data = group.buffer.data;\n    const offset = this._batchBuffer.addGroup(data);\n    return this._getBufferResource(offset / minUniformOffsetAlignment);\n  }\n  getArrayBindGroup(data) {\n    const offset = this._batchBuffer.addGroup(data);\n    return this._getBindGroup(offset / minUniformOffsetAlignment);\n  }\n  getArrayBufferResource(data) {\n    const offset = this._batchBuffer.addGroup(data);\n    const index = offset / minUniformOffsetAlignment;\n    return this._getBufferResource(index);\n  }\n  _getBufferResource(index) {\n    if (!this._bufferResources[index]) {\n      const buffer = this._buffers[index % 2];\n      this._bufferResources[index] = new BufferResource({\n        buffer,\n        offset: (index / 2 | 0) * 256,\n        size: minUniformOffsetAlignment\n      });\n    }\n    return this._bufferResources[index];\n  }\n  _getBindGroup(index) {\n    if (!this._bindGroups[index]) {\n      const bindGroup = new BindGroup({\n        0: this._getBufferResource(index)\n      });\n      this._bindGroups[index] = bindGroup;\n    }\n    return this._bindGroups[index];\n  }\n  _uploadBindGroups() {\n    const bufferSystem = this._renderer.buffer;\n    const firstBuffer = this._buffers[0];\n    firstBuffer.update(this._batchBuffer.byteIndex);\n    bufferSystem.updateBuffer(firstBuffer);\n    const commandEncoder = this._renderer.gpu.device.createCommandEncoder();\n    for (let i = 1; i < this._buffers.length; i++) {\n      const buffer = this._buffers[i];\n      commandEncoder.copyBufferToBuffer(\n        bufferSystem.getGPUBuffer(firstBuffer),\n        minUniformOffsetAlignment,\n        bufferSystem.getGPUBuffer(buffer),\n        0,\n        this._batchBuffer.byteIndex\n      );\n    }\n    this._renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n  }\n  destroy() {\n    for (let i = 0; i < this._bindGroups.length; i++) {\n      this._bindGroups[i].destroy();\n    }\n    this._bindGroups = null;\n    this._bindGroupHash = null;\n    for (let i = 0; i < this._buffers.length; i++) {\n      this._buffers[i].destroy();\n    }\n    this._buffers = null;\n    for (let i = 0; i < this._bufferResources.length; i++) {\n      this._bufferResources[i].destroy();\n    }\n    this._bufferResources = null;\n    this._batchBuffer.destroy();\n    this._bindGroupHash = null;\n    this._renderer = null;\n  }\n}\nGpuUniformBatchPipe.extension = {\n  type: [\n    ExtensionType.WebGPUPipes\n  ],\n  name: \"uniformBatch\"\n};\nconst topologyStringToId = {\n  \"point-list\": 0,\n  \"line-list\": 1,\n  \"line-strip\": 2,\n  \"triangle-list\": 3,\n  \"triangle-strip\": 4\n};\nfunction getGraphicsStateKey(geometryLayout, shaderKey, state, blendMode, topology) {\n  return geometryLayout << 24 | shaderKey << 16 | state << 10 | blendMode << 5 | topology;\n}\nfunction getGlobalStateKey(stencilStateId, multiSampleCount, colorMask, renderTarget) {\n  return colorMask << 6 | stencilStateId << 3 | renderTarget << 1 | multiSampleCount;\n}\nclass PipelineSystem {\n  constructor(renderer) {\n    this._moduleCache = /* @__PURE__ */ Object.create(null);\n    this._bufferLayoutsCache = /* @__PURE__ */ Object.create(null);\n    this._bindingNamesCache = /* @__PURE__ */ Object.create(null);\n    this._pipeCache = /* @__PURE__ */ Object.create(null);\n    this._pipeStateCaches = /* @__PURE__ */ Object.create(null);\n    this._colorMask = 15;\n    this._multisampleCount = 1;\n    this._renderer = renderer;\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n    this.setStencilMode(STENCIL_MODES.DISABLED);\n    this._updatePipeHash();\n  }\n  setMultisampleCount(multisampleCount) {\n    if (this._multisampleCount === multisampleCount)\n      return;\n    this._multisampleCount = multisampleCount;\n    this._updatePipeHash();\n  }\n  setRenderTarget(renderTarget) {\n    this._multisampleCount = renderTarget.msaaSamples;\n    this._depthStencilAttachment = renderTarget.descriptor.depthStencilAttachment ? 1 : 0;\n    this._updatePipeHash();\n  }\n  setColorMask(colorMask) {\n    if (this._colorMask === colorMask)\n      return;\n    this._colorMask = colorMask;\n    this._updatePipeHash();\n  }\n  setStencilMode(stencilMode) {\n    if (this._stencilMode === stencilMode)\n      return;\n    this._stencilMode = stencilMode;\n    this._stencilState = GpuStencilModesToPixi[stencilMode];\n    this._updatePipeHash();\n  }\n  setPipeline(geometry, program, state, passEncoder) {\n    const pipeline = this.getPipeline(geometry, program, state);\n    passEncoder.setPipeline(pipeline);\n  }\n  getPipeline(geometry, program, state, topology) {\n    if (!geometry._layoutKey) {\n      ensureAttributes(geometry, program.attributeData);\n      this._generateBufferKey(geometry);\n    }\n    topology || (topology = geometry.topology);\n    const key = getGraphicsStateKey(\n      geometry._layoutKey,\n      program._layoutKey,\n      state.data,\n      state._blendModeId,\n      topologyStringToId[topology]\n    );\n    if (this._pipeCache[key])\n      return this._pipeCache[key];\n    this._pipeCache[key] = this._createPipeline(geometry, program, state, topology);\n    return this._pipeCache[key];\n  }\n  _createPipeline(geometry, program, state, topology) {\n    const device = this._gpu.device;\n    const buffers = this._createVertexBufferLayouts(geometry, program);\n    const blendModes = this._renderer.state.getColorTargets(state);\n    blendModes[0].writeMask = this._stencilMode === STENCIL_MODES.RENDERING_MASK_ADD ? 0 : this._colorMask;\n    const layout = this._renderer.shader.getProgramData(program).pipeline;\n    const descriptor = {\n      // TODO later check if its helpful to create..\n      // layout,\n      vertex: {\n        module: this._getModule(program.vertex.source),\n        entryPoint: program.vertex.entryPoint,\n        // geometry..\n        buffers\n      },\n      fragment: {\n        module: this._getModule(program.fragment.source),\n        entryPoint: program.fragment.entryPoint,\n        targets: blendModes\n      },\n      primitive: {\n        topology,\n        cullMode: state.cullMode\n      },\n      layout,\n      multisample: {\n        count: this._multisampleCount\n      },\n      // depthStencil,\n      label: `PIXI Pipeline`\n    };\n    if (this._depthStencilAttachment) {\n      descriptor.depthStencil = {\n        ...this._stencilState,\n        format: \"depth24plus-stencil8\",\n        depthWriteEnabled: state.depthTest,\n        depthCompare: state.depthTest ? \"less\" : \"always\"\n      };\n    }\n    const pipeline = device.createRenderPipeline(descriptor);\n    return pipeline;\n  }\n  _getModule(code) {\n    return this._moduleCache[code] || this._createModule(code);\n  }\n  _createModule(code) {\n    const device = this._gpu.device;\n    this._moduleCache[code] = device.createShaderModule({\n      code\n    });\n    return this._moduleCache[code];\n  }\n  _generateBufferKey(geometry) {\n    const keyGen = [];\n    let index = 0;\n    const attributeKeys = Object.keys(geometry.attributes).sort();\n    for (let i = 0; i < attributeKeys.length; i++) {\n      const attribute = geometry.attributes[attributeKeys[i]];\n      keyGen[index++] = attribute.offset;\n      keyGen[index++] = attribute.format;\n      keyGen[index++] = attribute.stride;\n      keyGen[index++] = attribute.instance;\n    }\n    const stringKey = keyGen.join(\"|\");\n    geometry._layoutKey = createIdFromString(stringKey, \"geometry\");\n    return geometry._layoutKey;\n  }\n  _generateAttributeLocationsKey(program) {\n    const keyGen = [];\n    let index = 0;\n    const attributeKeys = Object.keys(program.attributeData).sort();\n    for (let i = 0; i < attributeKeys.length; i++) {\n      const attribute = program.attributeData[attributeKeys[i]];\n      keyGen[index++] = attribute.location;\n    }\n    const stringKey = keyGen.join(\"|\");\n    program._attributeLocationsKey = createIdFromString(stringKey, \"programAttributes\");\n    return program._attributeLocationsKey;\n  }\n  /**\n   * Returns a hash of buffer names mapped to bind locations.\n   * This is used to bind the correct buffer to the correct location in the shader.\n   * @param geometry - The geometry where to get the buffer names\n   * @param program - The program where to get the buffer names\n   * @returns An object of buffer names mapped to the bind location.\n   */\n  getBufferNamesToBind(geometry, program) {\n    const key = geometry._layoutKey << 16 | program._attributeLocationsKey;\n    if (this._bindingNamesCache[key])\n      return this._bindingNamesCache[key];\n    const data = this._createVertexBufferLayouts(geometry, program);\n    const bufferNamesToBind = /* @__PURE__ */ Object.create(null);\n    const attributeData = program.attributeData;\n    for (let i = 0; i < data.length; i++) {\n      const attributes = Object.values(data[i].attributes);\n      const shaderLocation = attributes[0].shaderLocation;\n      for (const j in attributeData) {\n        if (attributeData[j].location === shaderLocation) {\n          bufferNamesToBind[i] = j;\n          break;\n        }\n      }\n    }\n    this._bindingNamesCache[key] = bufferNamesToBind;\n    return bufferNamesToBind;\n  }\n  _createVertexBufferLayouts(geometry, program) {\n    if (!program._attributeLocationsKey)\n      this._generateAttributeLocationsKey(program);\n    const key = geometry._layoutKey << 16 | program._attributeLocationsKey;\n    if (this._bufferLayoutsCache[key]) {\n      return this._bufferLayoutsCache[key];\n    }\n    const vertexBuffersLayout = [];\n    geometry.buffers.forEach((buffer) => {\n      const bufferEntry = {\n        arrayStride: 0,\n        stepMode: \"vertex\",\n        attributes: []\n      };\n      const bufferEntryAttributes = bufferEntry.attributes;\n      for (const i in program.attributeData) {\n        const attribute = geometry.attributes[i];\n        if ((attribute.divisor ?? 1) !== 1) {\n          warn(`Attribute ${i} has an invalid divisor value of '${attribute.divisor}'. WebGPU only supports a divisor value of 1`);\n        }\n        if (attribute.buffer === buffer) {\n          bufferEntry.arrayStride = attribute.stride;\n          bufferEntry.stepMode = attribute.instance ? \"instance\" : \"vertex\";\n          bufferEntryAttributes.push({\n            shaderLocation: program.attributeData[i].location,\n            offset: attribute.offset,\n            format: attribute.format\n          });\n        }\n      }\n      if (bufferEntryAttributes.length) {\n        vertexBuffersLayout.push(bufferEntry);\n      }\n    });\n    this._bufferLayoutsCache[key] = vertexBuffersLayout;\n    return vertexBuffersLayout;\n  }\n  _updatePipeHash() {\n    const key = getGlobalStateKey(\n      this._stencilMode,\n      this._multisampleCount,\n      this._colorMask,\n      this._depthStencilAttachment\n    );\n    if (!this._pipeStateCaches[key]) {\n      this._pipeStateCaches[key] = /* @__PURE__ */ Object.create(null);\n    }\n    this._pipeCache = this._pipeStateCaches[key];\n  }\n  destroy() {\n    this._renderer = null;\n    this._bufferLayoutsCache = null;\n  }\n}\nPipelineSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"pipeline\"\n};\nclass GpuRenderTarget {\n  constructor() {\n    this.contexts = [];\n    this.msaaTextures = [];\n    this.msaaSamples = 1;\n  }\n}\nclass GpuRenderTargetAdaptor {\n  init(renderer, renderTargetSystem) {\n    this._renderer = renderer;\n    this._renderTargetSystem = renderTargetSystem;\n  }\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    const renderer = this._renderer;\n    const baseGpuTexture = this._getGpuColorTexture(\n      sourceRenderSurfaceTexture\n    );\n    const backGpuTexture = renderer.texture.getGpuSource(\n      destinationTexture.source\n    );\n    renderer.encoder.commandEncoder.copyTextureToTexture(\n      {\n        texture: baseGpuTexture,\n        origin: originSrc\n      },\n      {\n        texture: backGpuTexture,\n        origin: originDest\n      },\n      size\n    );\n    return destinationTexture;\n  }\n  startRenderPass(renderTarget, clear = true, clearColor, viewport) {\n    const renderTargetSystem = this._renderTargetSystem;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    const descriptor = this.getDescriptor(renderTarget, clear, clearColor);\n    gpuRenderTarget.descriptor = descriptor;\n    this._renderer.pipeline.setRenderTarget(gpuRenderTarget);\n    this._renderer.encoder.beginRenderPass(gpuRenderTarget);\n    this._renderer.encoder.setViewport(viewport);\n  }\n  finishRenderPass() {\n    this._renderer.encoder.endRenderPass();\n  }\n  /**\n   * returns the gpu texture for the first color texture in the render target\n   * mainly used by the filter manager to get copy the texture for blending\n   * @param renderTarget\n   * @returns a gpu texture\n   */\n  _getGpuColorTexture(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (gpuRenderTarget.contexts[0]) {\n      return gpuRenderTarget.contexts[0].getCurrentTexture();\n    }\n    return this._renderer.texture.getGpuSource(\n      renderTarget.colorTextures[0].source\n    );\n  }\n  getDescriptor(renderTarget, clear, clearValue) {\n    if (typeof clear === \"boolean\") {\n      clear = clear ? CLEAR.ALL : CLEAR.NONE;\n    }\n    const renderTargetSystem = this._renderTargetSystem;\n    const gpuRenderTarget = renderTargetSystem.getGpuRenderTarget(renderTarget);\n    const colorAttachments = renderTarget.colorTextures.map(\n      (texture, i) => {\n        const context = gpuRenderTarget.contexts[i];\n        let view;\n        let resolveTarget;\n        if (context) {\n          const currentTexture = context.getCurrentTexture();\n          const canvasTextureView = currentTexture.createView();\n          view = canvasTextureView;\n        } else {\n          view = this._renderer.texture.getGpuSource(texture).createView({\n            mipLevelCount: 1\n          });\n        }\n        if (gpuRenderTarget.msaaTextures[i]) {\n          resolveTarget = view;\n          view = this._renderer.texture.getTextureView(\n            gpuRenderTarget.msaaTextures[i]\n          );\n        }\n        const loadOp = clear & CLEAR.COLOR ? \"clear\" : \"load\";\n        clearValue ?? (clearValue = renderTargetSystem.defaultClearColor);\n        return {\n          view,\n          resolveTarget,\n          clearValue,\n          storeOp: \"store\",\n          loadOp\n        };\n      }\n    );\n    let depthStencilAttachment;\n    if ((renderTarget.stencil || renderTarget.depth) && !renderTarget.depthStencilTexture) {\n      renderTarget.ensureDepthStencilTexture();\n      renderTarget.depthStencilTexture.source.sampleCount = gpuRenderTarget.msaa ? 4 : 1;\n    }\n    if (renderTarget.depthStencilTexture) {\n      const stencilLoadOp = clear & CLEAR.STENCIL ? \"clear\" : \"load\";\n      const depthLoadOp = clear & CLEAR.DEPTH ? \"clear\" : \"load\";\n      depthStencilAttachment = {\n        view: this._renderer.texture.getGpuSource(renderTarget.depthStencilTexture.source).createView(),\n        stencilStoreOp: \"store\",\n        stencilLoadOp,\n        depthClearValue: 1,\n        depthLoadOp,\n        depthStoreOp: \"store\"\n      };\n    }\n    const descriptor = {\n      colorAttachments,\n      depthStencilAttachment\n    };\n    return descriptor;\n  }\n  clear(renderTarget, clear = true, clearColor, viewport) {\n    if (!clear)\n      return;\n    const { gpu, encoder } = this._renderer;\n    const device = gpu.device;\n    const standAlone = encoder.commandEncoder === null;\n    if (standAlone) {\n      const commandEncoder = device.createCommandEncoder();\n      const renderPassDescriptor = this.getDescriptor(renderTarget, clear, clearColor);\n      const passEncoder = commandEncoder.beginRenderPass(renderPassDescriptor);\n      passEncoder.setViewport(viewport.x, viewport.y, viewport.width, viewport.height, 0, 1);\n      passEncoder.end();\n      const gpuCommands = commandEncoder.finish();\n      device.queue.submit([gpuCommands]);\n    } else {\n      this.startRenderPass(renderTarget, clear, clearColor, viewport);\n    }\n  }\n  initGpuRenderTarget(renderTarget) {\n    renderTarget.isRoot = true;\n    const gpuRenderTarget = new GpuRenderTarget();\n    renderTarget.colorTextures.forEach((colorTexture, i) => {\n      if (CanvasSource.test(colorTexture.resource)) {\n        const context = colorTexture.resource.getContext(\n          \"webgpu\"\n        );\n        const alphaMode = colorTexture.transparent ? \"premultiplied\" : \"opaque\";\n        try {\n          context.configure({\n            device: this._renderer.gpu.device,\n            usage: GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_DST | GPUTextureUsage.RENDER_ATTACHMENT | GPUTextureUsage.COPY_SRC,\n            format: \"bgra8unorm\",\n            alphaMode\n          });\n        } catch (e) {\n          console.error(e);\n        }\n        gpuRenderTarget.contexts[i] = context;\n      }\n      gpuRenderTarget.msaa = colorTexture.source.antialias;\n      if (colorTexture.source.antialias) {\n        const msaaTexture = new TextureSource({\n          width: 0,\n          height: 0,\n          sampleCount: 4\n        });\n        gpuRenderTarget.msaaTextures[i] = msaaTexture;\n      }\n    });\n    if (gpuRenderTarget.msaa) {\n      gpuRenderTarget.msaaSamples = 4;\n      if (renderTarget.depthStencilTexture) {\n        renderTarget.depthStencilTexture.source.sampleCount = 4;\n      }\n    }\n    return gpuRenderTarget;\n  }\n  destroyGpuRenderTarget(gpuRenderTarget) {\n    gpuRenderTarget.contexts.forEach((context) => {\n      context.unconfigure();\n    });\n    gpuRenderTarget.msaaTextures.forEach((texture) => {\n      texture.destroy();\n    });\n    gpuRenderTarget.msaaTextures.length = 0;\n    gpuRenderTarget.contexts.length = 0;\n  }\n  ensureDepthStencilTexture(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    if (renderTarget.depthStencilTexture && gpuRenderTarget.msaa) {\n      renderTarget.depthStencilTexture.source.sampleCount = 4;\n    }\n  }\n  resizeGpuRenderTarget(renderTarget) {\n    const gpuRenderTarget = this._renderTargetSystem.getGpuRenderTarget(renderTarget);\n    gpuRenderTarget.width = renderTarget.width;\n    gpuRenderTarget.height = renderTarget.height;\n    if (gpuRenderTarget.msaa) {\n      renderTarget.colorTextures.forEach((colorTexture, i) => {\n        const msaaTexture = gpuRenderTarget.msaaTextures[i];\n        msaaTexture?.resize(\n          colorTexture.source.width,\n          colorTexture.source.height,\n          colorTexture.source._resolution\n        );\n      });\n    }\n  }\n}\nclass GpuRenderTargetSystem extends RenderTargetSystem {\n  constructor(renderer) {\n    super(renderer);\n    this.adaptor = new GpuRenderTargetAdaptor();\n    this.adaptor.init(renderer, this);\n  }\n}\nGpuRenderTargetSystem.extension = {\n  type: [ExtensionType.WebGPUSystem],\n  name: \"renderTarget\"\n};\nclass GpuShaderSystem {\n  constructor() {\n    this._gpuProgramData = /* @__PURE__ */ Object.create(null);\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n    this.maxTextures = gpu.device.limits.maxSampledTexturesPerShaderStage;\n  }\n  getProgramData(program) {\n    return this._gpuProgramData[program._layoutKey] || this._createGPUProgramData(program);\n  }\n  _createGPUProgramData(program) {\n    const device = this._gpu.device;\n    const bindGroups = program.gpuLayout.map((group) => device.createBindGroupLayout({ entries: group }));\n    const pipelineLayoutDesc = { bindGroupLayouts: bindGroups };\n    this._gpuProgramData[program._layoutKey] = {\n      bindGroups,\n      pipeline: device.createPipelineLayout(pipelineLayoutDesc)\n    };\n    return this._gpuProgramData[program._layoutKey];\n  }\n  destroy() {\n    this._gpu = null;\n    this._gpuProgramData = null;\n  }\n}\nGpuShaderSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"shader\"\n};\nconst GpuBlendModesToPixi = {};\nGpuBlendModesToPixi.normal = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.add = {\n  alpha: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.multiply = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"dst\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.screen = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.overlay = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.none = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"zero\",\n    dstFactor: \"zero\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"normal-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"add-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi[\"screen-npm\"] = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"src-alpha\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.erase = {\n  alpha: {\n    srcFactor: \"zero\",\n    dstFactor: \"one-minus-src-alpha\",\n    operation: \"add\"\n  },\n  color: {\n    srcFactor: \"zero\",\n    dstFactor: \"one-minus-src\",\n    operation: \"add\"\n  }\n};\nGpuBlendModesToPixi.min = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"min\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"min\"\n  }\n};\nGpuBlendModesToPixi.max = {\n  alpha: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"max\"\n  },\n  color: {\n    srcFactor: \"one\",\n    dstFactor: \"one\",\n    operation: \"max\"\n  }\n};\nclass GpuStateSystem {\n  constructor() {\n    this.defaultState = new State();\n    this.defaultState.blend = true;\n  }\n  contextChange(gpu) {\n    this.gpu = gpu;\n  }\n  /**\n   * Gets the blend mode data for the current state\n   * @param state - The state to get the blend mode from\n   */\n  getColorTargets(state) {\n    const blend = GpuBlendModesToPixi[state.blendMode] || GpuBlendModesToPixi.normal;\n    return [\n      {\n        format: \"bgra8unorm\",\n        writeMask: 0,\n        blend\n      }\n    ];\n  }\n  destroy() {\n    this.gpu = null;\n  }\n}\nGpuStateSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"state\"\n};\nconst gpuUploadBufferImageResource = {\n  type: \"image\",\n  upload(source, gpuTexture, gpu) {\n    const resource = source.resource;\n    const total = (source.pixelWidth | 0) * (source.pixelHeight | 0);\n    const bytesPerPixel = resource.byteLength / total;\n    gpu.device.queue.writeTexture(\n      { texture: gpuTexture },\n      resource,\n      {\n        offset: 0,\n        rowsPerImage: source.pixelHeight,\n        bytesPerRow: source.pixelHeight * bytesPerPixel\n      },\n      {\n        width: source.pixelWidth,\n        height: source.pixelHeight,\n        depthOrArrayLayers: 1\n      }\n    );\n  }\n};\nconst blockDataMap = {\n  \"bc1-rgba-unorm\": { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n  \"bc2-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"bc3-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"bc7-rgba-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"etc1-rgb-unorm\": { blockBytes: 8, blockWidth: 4, blockHeight: 4 },\n  \"etc2-rgba8unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 },\n  \"astc-4x4-unorm\": { blockBytes: 16, blockWidth: 4, blockHeight: 4 }\n};\nconst defaultBlockData = { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\nconst gpuUploadCompressedTextureResource = {\n  type: \"compressed\",\n  upload(source, gpuTexture, gpu) {\n    let mipWidth = source.pixelWidth;\n    let mipHeight = source.pixelHeight;\n    const blockData = blockDataMap[source.format] || defaultBlockData;\n    for (let i = 0; i < source.resource.length; i++) {\n      const levelBuffer = source.resource[i];\n      const bytesPerRow = Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockBytes;\n      gpu.device.queue.writeTexture(\n        {\n          texture: gpuTexture,\n          mipLevel: i\n        },\n        levelBuffer,\n        {\n          offset: 0,\n          bytesPerRow\n        },\n        {\n          width: Math.ceil(mipWidth / blockData.blockWidth) * blockData.blockWidth,\n          height: Math.ceil(mipHeight / blockData.blockHeight) * blockData.blockHeight,\n          depthOrArrayLayers: 1\n        }\n      );\n      mipWidth = Math.max(mipWidth >> 1, 1);\n      mipHeight = Math.max(mipHeight >> 1, 1);\n    }\n  }\n};\nconst gpuUploadImageResource = {\n  type: \"image\",\n  upload(source, gpuTexture, gpu) {\n    const resource = source.resource;\n    if (!resource)\n      return;\n    const width = Math.min(gpuTexture.width, source.resourceWidth || source.pixelWidth);\n    const height = Math.min(gpuTexture.height, source.resourceHeight || source.pixelHeight);\n    const premultipliedAlpha = source.alphaMode === \"premultiply-alpha-on-upload\";\n    gpu.device.queue.copyExternalImageToTexture(\n      { source: resource },\n      { texture: gpuTexture, premultipliedAlpha },\n      {\n        width,\n        height\n      }\n    );\n  }\n};\nconst gpuUploadVideoResource = {\n  type: \"video\",\n  upload(source, gpuTexture, gpu) {\n    gpuUploadImageResource.upload(source, gpuTexture, gpu);\n  }\n};\nclass GpuMipmapGenerator {\n  constructor(device) {\n    this.device = device;\n    this.sampler = device.createSampler({ minFilter: \"linear\" });\n    this.pipelines = {};\n  }\n  _getMipmapPipeline(format) {\n    let pipeline = this.pipelines[format];\n    if (!pipeline) {\n      if (!this.mipmapShaderModule) {\n        this.mipmapShaderModule = this.device.createShaderModule({\n          code: (\n            /* wgsl */\n            `\n                        var<private> pos : array<vec2<f32>, 3> = array<vec2<f32>, 3>(\n                        vec2<f32>(-1.0, -1.0), vec2<f32>(-1.0, 3.0), vec2<f32>(3.0, -1.0));\n\n                        struct VertexOutput {\n                        @builtin(position) position : vec4<f32>,\n                        @location(0) texCoord : vec2<f32>,\n                        };\n\n                        @vertex\n                        fn vertexMain(@builtin(vertex_index) vertexIndex : u32) -> VertexOutput {\n                        var output : VertexOutput;\n                        output.texCoord = pos[vertexIndex] * vec2<f32>(0.5, -0.5) + vec2<f32>(0.5);\n                        output.position = vec4<f32>(pos[vertexIndex], 0.0, 1.0);\n                        return output;\n                        }\n\n                        @group(0) @binding(0) var imgSampler : sampler;\n                        @group(0) @binding(1) var img : texture_2d<f32>;\n\n                        @fragment\n                        fn fragmentMain(@location(0) texCoord : vec2<f32>) -> @location(0) vec4<f32> {\n                        return textureSample(img, imgSampler, texCoord);\n                        }\n                    `\n          )\n        });\n      }\n      pipeline = this.device.createRenderPipeline({\n        layout: \"auto\",\n        vertex: {\n          module: this.mipmapShaderModule,\n          entryPoint: \"vertexMain\"\n        },\n        fragment: {\n          module: this.mipmapShaderModule,\n          entryPoint: \"fragmentMain\",\n          targets: [{ format }]\n        }\n      });\n      this.pipelines[format] = pipeline;\n    }\n    return pipeline;\n  }\n  /**\n   * Generates mipmaps for the given GPUTexture from the data in level 0.\n   * @param {module:External.GPUTexture} texture - Texture to generate mipmaps for.\n   * @returns {module:External.GPUTexture} - The originally passed texture\n   */\n  generateMipmap(texture) {\n    const pipeline = this._getMipmapPipeline(texture.format);\n    if (texture.dimension === \"3d\" || texture.dimension === \"1d\") {\n      throw new Error(\"Generating mipmaps for non-2d textures is currently unsupported!\");\n    }\n    let mipTexture = texture;\n    const arrayLayerCount = texture.depthOrArrayLayers || 1;\n    const renderToSource = texture.usage & GPUTextureUsage.RENDER_ATTACHMENT;\n    if (!renderToSource) {\n      const mipTextureDescriptor = {\n        size: {\n          width: Math.ceil(texture.width / 2),\n          height: Math.ceil(texture.height / 2),\n          depthOrArrayLayers: arrayLayerCount\n        },\n        format: texture.format,\n        usage: GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_SRC | GPUTextureUsage.RENDER_ATTACHMENT,\n        mipLevelCount: texture.mipLevelCount - 1\n      };\n      mipTexture = this.device.createTexture(mipTextureDescriptor);\n    }\n    const commandEncoder = this.device.createCommandEncoder({});\n    const bindGroupLayout = pipeline.getBindGroupLayout(0);\n    for (let arrayLayer = 0; arrayLayer < arrayLayerCount; ++arrayLayer) {\n      let srcView = texture.createView({\n        baseMipLevel: 0,\n        mipLevelCount: 1,\n        dimension: \"2d\",\n        baseArrayLayer: arrayLayer,\n        arrayLayerCount: 1\n      });\n      let dstMipLevel = renderToSource ? 1 : 0;\n      for (let i = 1; i < texture.mipLevelCount; ++i) {\n        const dstView = mipTexture.createView({\n          baseMipLevel: dstMipLevel++,\n          mipLevelCount: 1,\n          dimension: \"2d\",\n          baseArrayLayer: arrayLayer,\n          arrayLayerCount: 1\n        });\n        const passEncoder = commandEncoder.beginRenderPass({\n          colorAttachments: [{\n            view: dstView,\n            storeOp: \"store\",\n            loadOp: \"clear\",\n            clearValue: { r: 0, g: 0, b: 0, a: 0 }\n          }]\n        });\n        const bindGroup = this.device.createBindGroup({\n          layout: bindGroupLayout,\n          entries: [{\n            binding: 0,\n            resource: this.sampler\n          }, {\n            binding: 1,\n            resource: srcView\n          }]\n        });\n        passEncoder.setPipeline(pipeline);\n        passEncoder.setBindGroup(0, bindGroup);\n        passEncoder.draw(3, 1, 0, 0);\n        passEncoder.end();\n        srcView = dstView;\n      }\n    }\n    if (!renderToSource) {\n      const mipLevelSize = {\n        width: Math.ceil(texture.width / 2),\n        height: Math.ceil(texture.height / 2),\n        depthOrArrayLayers: arrayLayerCount\n      };\n      for (let i = 1; i < texture.mipLevelCount; ++i) {\n        commandEncoder.copyTextureToTexture({\n          texture: mipTexture,\n          mipLevel: i - 1\n        }, {\n          texture,\n          mipLevel: i\n        }, mipLevelSize);\n        mipLevelSize.width = Math.ceil(mipLevelSize.width / 2);\n        mipLevelSize.height = Math.ceil(mipLevelSize.height / 2);\n      }\n    }\n    this.device.queue.submit([commandEncoder.finish()]);\n    if (!renderToSource) {\n      mipTexture.destroy();\n    }\n    return texture;\n  }\n}\nclass GpuTextureSystem {\n  constructor(renderer) {\n    this.managedTextures = [];\n    this._gpuSources = /* @__PURE__ */ Object.create(null);\n    this._gpuSamplers = /* @__PURE__ */ Object.create(null);\n    this._bindGroupHash = /* @__PURE__ */ Object.create(null);\n    this._textureViewHash = /* @__PURE__ */ Object.create(null);\n    this._uploads = {\n      image: gpuUploadImageResource,\n      buffer: gpuUploadBufferImageResource,\n      video: gpuUploadVideoResource,\n      compressed: gpuUploadCompressedTextureResource\n    };\n    this._renderer = renderer;\n    renderer.renderableGC.addManagedHash(this, \"_gpuSources\");\n    renderer.renderableGC.addManagedHash(this, \"_gpuSamplers\");\n    renderer.renderableGC.addManagedHash(this, \"_bindGroupHash\");\n    renderer.renderableGC.addManagedHash(this, \"_textureViewHash\");\n  }\n  contextChange(gpu) {\n    this._gpu = gpu;\n  }\n  initSource(source) {\n    if (source.autoGenerateMipmaps) {\n      const biggestDimension = Math.max(source.pixelWidth, source.pixelHeight);\n      source.mipLevelCount = Math.floor(Math.log2(biggestDimension)) + 1;\n    }\n    let usage = GPUTextureUsage.TEXTURE_BINDING | GPUTextureUsage.COPY_DST;\n    if (source.uploadMethodId !== \"compressed\") {\n      usage |= GPUTextureUsage.RENDER_ATTACHMENT;\n      usage |= GPUTextureUsage.COPY_SRC;\n    }\n    const blockData = blockDataMap[source.format] || { blockBytes: 4, blockWidth: 1, blockHeight: 1 };\n    const width = Math.ceil(source.pixelWidth / blockData.blockWidth) * blockData.blockWidth;\n    const height = Math.ceil(source.pixelHeight / blockData.blockHeight) * blockData.blockHeight;\n    const textureDescriptor = {\n      label: source.label,\n      size: { width, height },\n      format: source.format,\n      sampleCount: source.sampleCount,\n      mipLevelCount: source.mipLevelCount,\n      dimension: source.dimension,\n      usage\n    };\n    const gpuTexture = this._gpu.device.createTexture(textureDescriptor);\n    this._gpuSources[source.uid] = gpuTexture;\n    if (!this.managedTextures.includes(source)) {\n      source.on(\"update\", this.onSourceUpdate, this);\n      source.on(\"resize\", this.onSourceResize, this);\n      source.on(\"destroy\", this.onSourceDestroy, this);\n      source.on(\"unload\", this.onSourceUnload, this);\n      source.on(\"updateMipmaps\", this.onUpdateMipmaps, this);\n      this.managedTextures.push(source);\n    }\n    this.onSourceUpdate(source);\n    return gpuTexture;\n  }\n  onSourceUpdate(source) {\n    const gpuTexture = this.getGpuSource(source);\n    if (!gpuTexture)\n      return;\n    if (this._uploads[source.uploadMethodId]) {\n      this._uploads[source.uploadMethodId].upload(source, gpuTexture, this._gpu);\n    }\n    if (source.autoGenerateMipmaps && source.mipLevelCount > 1) {\n      this.onUpdateMipmaps(source);\n    }\n  }\n  onSourceUnload(source) {\n    const gpuTexture = this._gpuSources[source.uid];\n    if (gpuTexture) {\n      this._gpuSources[source.uid] = null;\n      gpuTexture.destroy();\n    }\n  }\n  onUpdateMipmaps(source) {\n    if (!this._mipmapGenerator) {\n      this._mipmapGenerator = new GpuMipmapGenerator(this._gpu.device);\n    }\n    const gpuTexture = this.getGpuSource(source);\n    this._mipmapGenerator.generateMipmap(gpuTexture);\n  }\n  onSourceDestroy(source) {\n    source.off(\"update\", this.onSourceUpdate, this);\n    source.off(\"unload\", this.onSourceUnload, this);\n    source.off(\"destroy\", this.onSourceDestroy, this);\n    source.off(\"resize\", this.onSourceResize, this);\n    source.off(\"updateMipmaps\", this.onUpdateMipmaps, this);\n    this.managedTextures.splice(this.managedTextures.indexOf(source), 1);\n    this.onSourceUnload(source);\n  }\n  onSourceResize(source) {\n    const gpuTexture = this._gpuSources[source.uid];\n    if (!gpuTexture) {\n      this.initSource(source);\n    } else if (gpuTexture.width !== source.pixelWidth || gpuTexture.height !== source.pixelHeight) {\n      this._textureViewHash[source.uid] = null;\n      this._bindGroupHash[source.uid] = null;\n      this.onSourceUnload(source);\n      this.initSource(source);\n    }\n  }\n  _initSampler(sampler) {\n    this._gpuSamplers[sampler._resourceId] = this._gpu.device.createSampler(sampler);\n    return this._gpuSamplers[sampler._resourceId];\n  }\n  getGpuSampler(sampler) {\n    return this._gpuSamplers[sampler._resourceId] || this._initSampler(sampler);\n  }\n  getGpuSource(source) {\n    return this._gpuSources[source.uid] || this.initSource(source);\n  }\n  /**\n   * this returns s bind group for a specific texture, the bind group contains\n   * - the texture source\n   * - the texture style\n   * - the texture matrix\n   * This is cached so the bind group should only be created once per texture\n   * @param texture - the texture you want the bindgroup for\n   * @returns the bind group for the texture\n   */\n  getTextureBindGroup(texture) {\n    return this._bindGroupHash[texture.uid] ?? this._createTextureBindGroup(texture);\n  }\n  _createTextureBindGroup(texture) {\n    const source = texture.source;\n    this._bindGroupHash[texture.uid] = new BindGroup({\n      0: source,\n      1: source.style,\n      2: new UniformGroup({\n        uTextureMatrix: { type: \"mat3x3<f32>\", value: texture.textureMatrix.mapCoord }\n      })\n    });\n    return this._bindGroupHash[texture.uid];\n  }\n  getTextureView(texture) {\n    const source = texture.source;\n    return this._textureViewHash[source.uid] ?? this._createTextureView(source);\n  }\n  _createTextureView(texture) {\n    this._textureViewHash[texture.uid] = this.getGpuSource(texture).createView();\n    return this._textureViewHash[texture.uid];\n  }\n  generateCanvas(texture) {\n    const renderer = this._renderer;\n    const commandEncoder = renderer.gpu.device.createCommandEncoder();\n    const canvas = DOMAdapter.get().createCanvas();\n    canvas.width = texture.source.pixelWidth;\n    canvas.height = texture.source.pixelHeight;\n    const context = canvas.getContext(\"webgpu\");\n    context.configure({\n      device: renderer.gpu.device,\n      usage: GPUTextureUsage.COPY_DST | GPUTextureUsage.COPY_SRC,\n      format: DOMAdapter.get().getNavigator().gpu.getPreferredCanvasFormat(),\n      alphaMode: \"premultiplied\"\n    });\n    commandEncoder.copyTextureToTexture({\n      texture: renderer.texture.getGpuSource(texture.source),\n      origin: {\n        x: 0,\n        y: 0\n      }\n    }, {\n      texture: context.getCurrentTexture()\n    }, {\n      width: canvas.width,\n      height: canvas.height\n    });\n    renderer.gpu.device.queue.submit([commandEncoder.finish()]);\n    return canvas;\n  }\n  getPixels(texture) {\n    const webGPUCanvas = this.generateCanvas(texture);\n    const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(webGPUCanvas.width, webGPUCanvas.height);\n    const context = canvasAndContext.context;\n    context.drawImage(webGPUCanvas, 0, 0);\n    const { width, height } = webGPUCanvas;\n    const imageData = context.getImageData(0, 0, width, height);\n    const pixels = new Uint8ClampedArray(imageData.data.buffer);\n    CanvasPool.returnCanvasAndContext(canvasAndContext);\n    return { pixels, width, height };\n  }\n  destroy() {\n    this.managedTextures.slice().forEach((source) => this.onSourceDestroy(source));\n    this.managedTextures = null;\n    for (const k of Object.keys(this._bindGroupHash)) {\n      const key = Number(k);\n      const bindGroup = this._bindGroupHash[key];\n      bindGroup?.destroy();\n      this._bindGroupHash[key] = null;\n    }\n    this._gpu = null;\n    this._mipmapGenerator = null;\n    this._gpuSources = null;\n    this._bindGroupHash = null;\n    this._textureViewHash = null;\n    this._gpuSamplers = null;\n  }\n}\nGpuTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"texture\"\n};\nclass GpuGraphicsAdaptor {\n  init() {\n    const localUniforms = new UniformGroup({\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const gpuProgram = compileHighShaderGpuProgram({\n      name: \"graphics\",\n      bits: [\n        colorBit,\n        generateTextureBatchBit(getMaxTexturesPerBatch()),\n        localUniformBitGroup2,\n        roundPixelsBit\n      ]\n    });\n    this.shader = new Shader({\n      gpuProgram,\n      resources: {\n        // added on the fly!\n        localUniforms\n      }\n    });\n  }\n  execute(graphicsPipe, renderable) {\n    const context = renderable.context;\n    const shader = context.customShader || this.shader;\n    const renderer = graphicsPipe.renderer;\n    const contextSystem = renderer.graphicsContext;\n    const {\n      batcher,\n      instructions\n    } = contextSystem.getContextRenderData(context);\n    const encoder = renderer.encoder;\n    encoder.setGeometry(batcher.geometry, shader.gpuProgram);\n    const globalUniformsBindGroup = renderer.globalUniforms.bindGroup;\n    encoder.setBindGroup(0, globalUniformsBindGroup, shader.gpuProgram);\n    const localBindGroup = renderer.renderPipes.uniformBatch.getUniformBindGroup(shader.resources.localUniforms, true);\n    encoder.setBindGroup(2, localBindGroup, shader.gpuProgram);\n    const batches = instructions.instructions;\n    let topology = null;\n    for (let i = 0; i < instructions.instructionSize; i++) {\n      const batch = batches[i];\n      if (batch.topology !== topology) {\n        topology = batch.topology;\n        encoder.setPipelineFromGeometryProgramAndState(\n          batcher.geometry,\n          shader.gpuProgram,\n          graphicsPipe.state,\n          batch.topology\n        );\n      }\n      shader.groups[1] = batch.bindGroup;\n      if (!batch.gpuBindGroup) {\n        const textureBatch = batch.textures;\n        batch.bindGroup = getTextureBatchBindGroup(textureBatch.textures, textureBatch.count);\n        batch.gpuBindGroup = renderer.bindGroup.getBindGroup(\n          batch.bindGroup,\n          shader.gpuProgram,\n          1\n        );\n      }\n      encoder.setBindGroup(1, batch.bindGroup, shader.gpuProgram);\n      encoder.renderPassEncoder.drawIndexed(batch.size, 1, batch.start);\n    }\n  }\n  destroy() {\n    this.shader.destroy(true);\n    this.shader = null;\n  }\n}\nGpuGraphicsAdaptor.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"graphics\"\n};\nclass GpuMeshAdapter {\n  init() {\n    const gpuProgram = compileHighShaderGpuProgram({\n      name: \"mesh\",\n      bits: [\n        localUniformBit,\n        textureBit,\n        roundPixelsBit\n      ]\n    });\n    this._shader = new Shader({\n      gpuProgram,\n      resources: {\n        uTexture: Texture.EMPTY._source,\n        uSampler: Texture.EMPTY._source.style,\n        textureUniforms: {\n          uTextureMatrix: { type: \"mat3x3<f32>\", value: new Matrix() }\n        }\n      }\n    });\n  }\n  execute(meshPipe, mesh) {\n    const renderer = meshPipe.renderer;\n    let shader = mesh._shader;\n    if (!shader) {\n      shader = this._shader;\n      shader.groups[2] = renderer.texture.getTextureBindGroup(mesh.texture);\n    } else if (!shader.gpuProgram) {\n      warn(\"Mesh shader has no gpuProgram\", mesh.shader);\n      return;\n    }\n    const gpuProgram = shader.gpuProgram;\n    if (gpuProgram.autoAssignGlobalUniforms) {\n      shader.groups[0] = renderer.globalUniforms.bindGroup;\n    }\n    if (gpuProgram.autoAssignLocalUniforms) {\n      const localUniforms = meshPipe.localUniforms;\n      shader.groups[1] = renderer.renderPipes.uniformBatch.getUniformBindGroup(localUniforms, true);\n    }\n    renderer.encoder.draw({\n      geometry: mesh._geometry,\n      shader,\n      state: mesh.state\n    });\n  }\n  destroy() {\n    this._shader.destroy(true);\n    this._shader = null;\n  }\n}\nGpuMeshAdapter.extension = {\n  type: [\n    ExtensionType.WebGPUPipesAdaptor\n  ],\n  name: \"mesh\"\n};\nconst DefaultWebGPUSystems = [\n  ...SharedSystems,\n  GpuUboSystem,\n  GpuEncoderSystem,\n  GpuDeviceSystem,\n  GpuBufferSystem,\n  GpuTextureSystem,\n  GpuRenderTargetSystem,\n  GpuShaderSystem,\n  GpuStateSystem,\n  PipelineSystem,\n  GpuColorMaskSystem,\n  GpuStencilSystem,\n  BindGroupSystem\n];\nconst DefaultWebGPUPipes = [...SharedRenderPipes, GpuUniformBatchPipe];\nconst DefaultWebGPUAdapters = [GpuBatchAdaptor, GpuMeshAdapter, GpuGraphicsAdaptor];\nconst systems = [];\nconst renderPipes = [];\nconst renderPipeAdaptors = [];\nextensions.handleByNamedList(ExtensionType.WebGPUSystem, systems);\nextensions.handleByNamedList(ExtensionType.WebGPUPipes, renderPipes);\nextensions.handleByNamedList(ExtensionType.WebGPUPipesAdaptor, renderPipeAdaptors);\nextensions.add(...DefaultWebGPUSystems, ...DefaultWebGPUPipes, ...DefaultWebGPUAdapters);\nclass WebGPURenderer extends AbstractRenderer {\n  constructor() {\n    const systemConfig = {\n      name: \"webgpu\",\n      type: RendererType.WEBGPU,\n      systems,\n      renderPipes,\n      renderPipeAdaptors\n    };\n    super(systemConfig);\n  }\n}\nexport {\n  WebGPURenderer\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAChC,MAAM,eAAe,CAAC;AACtB,EAAE,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;AACrC,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC;AACtC,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AAC1B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC3C,IAAI,SAAS,CAAC,SAAS,GAAG,QAAQ,CAAC;AACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW;AACjC,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,SAAS;AACf,KAAK,CAAC;AACN,IAAI,MAAM,uBAAuB,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AACtE,IAAI,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,EAAE;AAC5B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;AAC1B,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC1C,MAAM,KAAK,CAAC,SAAS,GAAG,wBAAwB,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;AAC5F,KAAK;AACL,IAAI,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAC1C,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,YAAY;AACxD,MAAM,KAAK,CAAC,SAAS;AACrB,MAAM,OAAO;AACb,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW;AAClD,MAAM,IAAI,CAAC,SAAS;AACpB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,KAAK,CAAC,QAAQ;AACpB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACtE,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,kBAAkB;AACpC,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,KAAK,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,GAAG;AACH,EAAE,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE;AAC/C,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;AAC3B,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7G,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE;AAC/C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACnD,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE;AACjC,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,MAAM,IAAI,WAAW,CAAC;AACtB,MAAM,IAAI,QAAQ,CAAC,aAAa,KAAK,cAAc,EAAE;AACrD,QAAQ,MAAM,YAAY,GAAG,QAAQ,CAAC;AACtC,QAAQ,QAAQ,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACtD,QAAQ,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AAC3C,QAAQ,WAAW,GAAG;AACtB,UAAU,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;AACtC,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,QAAQ,CAAC,aAAa,KAAK,QAAQ,EAAE;AACtD,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC;AAChC,QAAQ,WAAW,GAAG;AACtB,UAAU,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,IAAI;AACtC,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,QAAQ,CAAC,aAAa,KAAK,gBAAgB,EAAE;AAC9D,QAAQ,MAAM,cAAc,GAAG,QAAQ,CAAC;AACxC,QAAQ,WAAW,GAAG;AACtB,UAAU,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC;AACrE,UAAU,MAAM,EAAE,cAAc,CAAC,MAAM;AACvC,UAAU,IAAI,EAAE,cAAc,CAAC,IAAI;AACnC,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,QAAQ,CAAC,aAAa,KAAK,gBAAgB,EAAE;AAC9D,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC;AACjC,QAAQ,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC9D,OAAO,MAAM,IAAI,QAAQ,CAAC,aAAa,KAAK,eAAe,EAAE;AAC7D,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC;AACjC,QAAQ,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,OAAO,CAAC,IAAI,CAAC;AACnB,QAAQ,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;AAC/B,QAAQ,QAAQ,EAAE,WAAW;AAC7B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AAClF,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC;AAChD,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;AAC1C,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC/C,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,GAAG;AACH,EAAE,YAAY,CAAC,MAAM,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACxE,GAAG;AACH,EAAE,YAAY,CAAC,MAAM,EAAE;AACvB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACnF,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC7B,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,EAAE;AAClC,MAAM,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW;AACxC,QAAQ,SAAS;AACjB,QAAQ,CAAC;AACT,QAAQ,IAAI,CAAC,MAAM;AACnB,QAAQ,CAAC;AACT;AACA,QAAQ,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC;AACxD,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH;AACA,EAAE,UAAU,GAAG;AACf,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;AACvC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AACvC,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AACnD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACvD,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACvE,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE;AACrB,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;AAC/D,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;AAC7C,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnD,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;AACxB,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAChE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnD,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;AACxB,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAClD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,QAAQ,CAAC;AACf,EAAE,WAAW,CAAC,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,EAAE;AACzE,IAAI,IAAI,CAAC,0BAA0B,GAAG,GAAG,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,0BAA0B,GAAG,0BAA0B,CAAC;AACjE,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,0BAA0B,GAAG,CAAC,EAAE;AACpD,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,wCAAwC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AACnC,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AACrG,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACxC,MAAM,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;AAC7B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS;AAC1C,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACrC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,CAAC;AACD,kBAAkB,CAAC,SAAS,GAAG;AAC/B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,YAAY;AACzB,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AAC5E,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1D,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,uBAAuB,CAAC,OAAO,EAAE;AACzC,IAAI,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC;AAC7E,MAAM,eAAe,EAAE,OAAO,CAAC,eAAe;AAC9C,MAAM,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;AACxD,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,wBAAwB;AAC9B,MAAM,0BAA0B;AAChC,MAAM,0BAA0B;AAChC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;AACzD,IAAI,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC;AAC/C,MAAM,gBAAgB;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,eAAe,CAAC,cAAc,GAAG;AACjC;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA,EAAE,oBAAoB,EAAE,KAAK;AAC7B,CAAC,CAAC;AACF,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,kBAAkB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK;AACpD,MAAM,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC;AAC7C,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;AAC3E,GAAG;AACH,EAAE,eAAe,CAAC,eAAe,EAAE;AACnC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC7F,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChC,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AACnC,KAAK;AACL,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtG,GAAG;AACH,EAAE,sCAAsC,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC7E,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7F,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ;AACxC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;AACnC,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE;AAClC,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,MAAM;AACjD,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AAC5C,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9F,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM;AACzC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;AACpC,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAClF,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC,CAAC;AACnG,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACvC,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE;AAC1C,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,SAAS;AACjD,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;AAC5C,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1F,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1F,IAAI,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE;AACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH,EAAE,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE;AACzC,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;AACnC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,CAAC,QAAQ,EAAE;AACrB,QAAQ,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AACvC,OAAO;AACP,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACzD,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,KAAK,MAAM,CAAC,IAAI,SAAS,CAAC,SAAS,EAAE;AACzC,MAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9C,MAAM,IAAI,QAAQ,CAAC,cAAc,EAAE;AACnC,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxD,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAChG,IAAI,IAAI,CAAC,sCAAsC,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC9F,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChD,IAAI,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC9B,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW;AACxC,QAAQ,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM;AAChD,QAAQ,aAAa,IAAI,QAAQ,CAAC,aAAa;AAC/C,QAAQ,KAAK,IAAI,CAAC;AAClB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE,aAAa,IAAI,QAAQ,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AACnH,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAChC,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AACnC,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH;AACA;AACA;AACA,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa;AACxE,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY;AAC9C,MAAM,KAAK;AACX,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC7E,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,IAAI,MAAM,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC7D,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACpD,IAAI,MAAM,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AACvD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC1D,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtG,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AACpC,IAAI,KAAK,MAAM,CAAC,IAAI,iBAAiB,EAAE;AACvC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,KAAK,MAAM,CAAC,IAAI,cAAc,EAAE;AACpC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACjC,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrC,MAAM,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC;AACpC,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,yBAAyB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,QAAQ,CAAC,YAAY,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,oBAAoB,CAAC,YAAY,EAAE;AACrC,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG;AACxE,QAAQ,WAAW,EAAE,aAAa,CAAC,QAAQ;AAC3C,QAAQ,gBAAgB,EAAE,CAAC;AAC3B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC;AAC5C,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACjF,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE,gBAAgB,EAAE;AAChD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;AACtF,IAAI,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3C,IAAI,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACrD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAClD,IAAI,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAC7E,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACpC,IAAI,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AAC1C,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,MAAM,oBAAoB,GAAG;AAC7B,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AAC5B,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AAC5B,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AAC5B,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AAC5B,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACpC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE;AACtC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,CAAC,CAAC;AACF,SAAS,qBAAqB,CAAC,WAAW,EAAE;AAC5C,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACjD,IAAI,IAAI;AACR,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,IAAI,EAAE,CAAC;AACX,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,IAAI,IAAI,GAAG,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC/D,IAAI,MAAM,KAAK,GAAG,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AACnE,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACrD,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,6CAA6C,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9F,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;AAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1D,KAAK;AACL,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;AAC/C,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B,IAAI,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,IAAI,MAAM,IAAI,IAAI,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;AACvC,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AACvC,CAAC;AACD,SAAS,qBAAqB,CAAC,UAAU,EAAE,WAAW,EAAE;AACxD,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC;AACvC,EAAE,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC;AAC/E,EAAE,OAAO,CAAC;AACV,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACvC,SAAS,EAAE,WAAW,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA,0BAA0B,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;AAC9D;AACA,gCAAgC,EAAE,IAAI,GAAG,CAAC,CAAC;AAC3C;AACA,iBAAiB,EAAE,IAAI,CAAC;AACxB;AACA,aAAa,EAAE,SAAS,KAAK,CAAC,GAAG,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACrE;AACA,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,yBAAyB,CAAC,WAAW,EAAE;AAChD,EAAE,OAAO,qBAAqB;AAC9B,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,qBAAqB;AACzB,IAAI,oBAAoB;AACxB,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,YAAY,SAAS,SAAS,CAAC;AACrC,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC;AACV,MAAM,iBAAiB,EAAE,qBAAqB;AAC9C,MAAM,eAAe,EAAE,yBAAyB;AAChD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC;AACpC,EAAE,IAAI,EAAE,KAAK;AACb,CAAC,CAAC;AACF,MAAM,yBAAyB,GAAG,GAAG,CAAC;AACtC,MAAM,mBAAmB,CAAC;AAC1B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACvE,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC,EAAE,yBAAyB,EAAE,CAAC,CAAC;AACpE,IAAI,MAAM,YAAY,GAAG,GAAG,GAAG,yBAAyB,CAAC;AACzD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,IAAI,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC;AAC7D,MAAM,IAAI,CAAC,KAAK,CAAC;AACjB,QAAQ,KAAK,IAAI,WAAW,CAAC,QAAQ,CAAC;AACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC;AACpC,QAAQ,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;AACpC,QAAQ,KAAK;AACb,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzC,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;AAC9B,GAAG;AACH;AACA,EAAE,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE;AACxC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACtD,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AACnF,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,yBAAyB,CAAC,CAAC;AAC5F,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,yBAAyB,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,yBAAyB,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,sBAAsB,CAAC,IAAI,EAAE;AAC/B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,KAAK,GAAG,MAAM,GAAG,yBAAyB,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,kBAAkB,CAAC,KAAK,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;AACvC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC9C,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI,cAAc,CAAC;AACxD,QAAQ,MAAM;AACd,QAAQ,MAAM,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AACrC,QAAQ,IAAI,EAAE,yBAAyB;AACvC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,aAAa,CAAC,KAAK,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;AAClC,MAAM,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC;AACtC,QAAQ,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACzC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC/C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AACpD,IAAI,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AAC3C,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;AAC5E,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,cAAc,CAAC,kBAAkB;AACvC,QAAQ,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC;AAC9C,QAAQ,yBAAyB;AACjC,QAAQ,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;AACzC,QAAQ,CAAC;AACT,QAAQ,IAAI,CAAC,YAAY,CAAC,SAAS;AACnC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACtE,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3D,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AAChC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,mBAAmB,CAAC,SAAS,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG;AAC3B,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,eAAe,EAAE,CAAC;AACpB,EAAE,gBAAgB,EAAE,CAAC;AACrB,CAAC,CAAC;AACF,SAAS,mBAAmB,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE;AACpF,EAAE,OAAO,cAAc,IAAI,EAAE,GAAG,SAAS,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,SAAS,IAAI,CAAC,GAAG,QAAQ,CAAC;AAC1F,CAAC;AACD,SAAS,iBAAiB,CAAC,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,YAAY,EAAE;AACtF,EAAE,OAAO,SAAS,IAAI,CAAC,GAAG,cAAc,IAAI,CAAC,GAAG,YAAY,IAAI,CAAC,GAAG,gBAAgB,CAAC;AACrF,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,YAAY,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,mBAAmB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,kBAAkB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,UAAU,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,mBAAmB,CAAC,gBAAgB,EAAE;AACxC,IAAI,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB;AACnD,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;AAC9C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,eAAe,CAAC,YAAY,EAAE;AAChC,IAAI,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,WAAW,CAAC;AACtD,IAAI,IAAI,CAAC,uBAAuB,GAAG,YAAY,CAAC,UAAU,CAAC,sBAAsB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1F,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,YAAY,CAAC,SAAS,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS;AACrC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAChC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE;AAC9B,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,WAAW;AACzC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;AACpC,IAAI,IAAI,CAAC,aAAa,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;AACrD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAChE,IAAI,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;AAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAC9B,MAAM,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACxD,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,QAAQ,KAAK,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,mBAAmB;AACnC,MAAM,QAAQ,CAAC,UAAU;AACzB,MAAM,OAAO,CAAC,UAAU;AACxB,MAAM,KAAK,CAAC,IAAI;AAChB,MAAM,KAAK,CAAC,YAAY;AACxB,MAAM,kBAAkB,CAAC,QAAQ,CAAC;AAClC,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;AAC5B,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACpF,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;AACtD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACvE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACnE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,KAAK,aAAa,CAAC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AAC3G,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG;AACvB;AACA;AACA,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;AACtD,QAAQ,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU;AAC7C;AACA,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AACxD,QAAQ,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU;AAC/C,QAAQ,OAAO,EAAE,UAAU;AAC3B,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,QAAQ;AAChB,QAAQ,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAChC,OAAO;AACP,MAAM,MAAM;AACZ,MAAM,WAAW,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI,CAAC,iBAAiB;AACrC,OAAO;AACP;AACA,MAAM,KAAK,EAAE,CAAC,aAAa,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,uBAAuB,EAAE;AACtC,MAAM,UAAU,CAAC,YAAY,GAAG;AAChC,QAAQ,GAAG,IAAI,CAAC,aAAa;AAC7B,QAAQ,MAAM,EAAE,sBAAsB;AACtC,QAAQ,iBAAiB,EAAE,KAAK,CAAC,SAAS;AAC1C,QAAQ,YAAY,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,GAAG,QAAQ;AACzD,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC7D,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/D,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE;AACtB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC;AACxD,MAAM,IAAI;AACV,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC/B,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;AAClE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AACzC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AACzC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AACzC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;AAC3C,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,IAAI,QAAQ,CAAC,UAAU,GAAG,kBAAkB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACpE,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC;AAC/B,GAAG;AACH,EAAE,8BAA8B,CAAC,OAAO,EAAE;AAC1C,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;AACpE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;AAC3C,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,IAAI,OAAO,CAAC,sBAAsB,GAAG,kBAAkB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;AACxF,IAAI,OAAO,OAAO,CAAC,sBAAsB,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,oBAAoB,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC1C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,GAAG,OAAO,CAAC,sBAAsB,CAAC;AAC3E,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;AACpC,MAAM,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACpE,IAAI,MAAM,iBAAiB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAChD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAC3D,MAAM,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;AAC1D,MAAM,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE;AACrC,QAAQ,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,cAAc,EAAE;AAC1D,UAAU,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACnC,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;AACrD,IAAI,OAAO,iBAAiB,CAAC;AAC7B,GAAG;AACH,EAAE,0BAA0B,CAAC,QAAQ,EAAE,OAAO,EAAE;AAChD,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB;AACvC,MAAM,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;AACnD,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,IAAI,EAAE,GAAG,OAAO,CAAC,sBAAsB,CAAC;AAC3E,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE;AACvC,MAAM,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,MAAM,mBAAmB,GAAG,EAAE,CAAC;AACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACzC,MAAM,MAAM,WAAW,GAAG;AAC1B,QAAQ,WAAW,EAAE,CAAC;AACtB,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,UAAU,EAAE,EAAE;AACtB,OAAO,CAAC;AACR,MAAM,MAAM,qBAAqB,GAAG,WAAW,CAAC,UAAU,CAAC;AAC3D,MAAM,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;AAC7C,QAAQ,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACjD,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;AAC5C,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,CAAC;AACnI,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,MAAM,EAAE;AACzC,UAAU,WAAW,CAAC,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC;AACrD,UAAU,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;AAC5E,UAAU,qBAAqB,CAAC,IAAI,CAAC;AACrC,YAAY,cAAc,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,QAAQ;AAC7D,YAAY,MAAM,EAAE,SAAS,CAAC,MAAM;AACpC,YAAY,MAAM,EAAE,SAAS,CAAC,MAAM;AACpC,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,MAAM,IAAI,qBAAqB,CAAC,MAAM,EAAE;AACxC,QAAQ,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC;AACxD,IAAI,OAAO,mBAAmB,CAAC;AAC/B,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,MAAM,GAAG,GAAG,iBAAiB;AACjC,MAAM,IAAI,CAAC,YAAY;AACvB,MAAM,IAAI,CAAC,iBAAiB;AAC5B,MAAM,IAAI,CAAC,UAAU;AACrB,MAAM,IAAI,CAAC,uBAAuB;AAClC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;AACrC,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACpC,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,GAAG;AACH,CAAC;AACD,MAAM,sBAAsB,CAAC;AAC7B,EAAE,IAAI,CAAC,QAAQ,EAAE,kBAAkB,EAAE;AACrC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;AAClD,GAAG;AACH,EAAE,aAAa,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE;AAC7F,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB;AACnD,MAAM,0BAA0B;AAChC,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY;AACxD,MAAM,kBAAkB,CAAC,MAAM;AAC/B,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,oBAAoB;AACxD,MAAM;AACN,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,MAAM,EAAE,SAAS;AACzB,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,MAAM,EAAE,UAAU;AAC1B,OAAO;AACP,MAAM,IAAI;AACV,KAAK,CAAC;AACN,IAAI,OAAO,kBAAkB,CAAC;AAC9B,GAAG;AACH,EAAE,eAAe,CAAC,YAAY,EAAE,KAAK,GAAG,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE;AACpE,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,eAAe,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAChF,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC3E,IAAI,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,YAAY,EAAE;AACpC,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACtF,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACrC,MAAM,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;AAC7D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY;AAC9C,MAAM,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM;AAC1C,KAAK,CAAC;AACN,GAAG;AACH,EAAE,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE;AACjD,IAAI,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AACpC,MAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,eAAe,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAChF,IAAI,MAAM,gBAAgB,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG;AAC3D,MAAM,CAAC,OAAO,EAAE,CAAC,KAAK;AACtB,QAAQ,MAAM,OAAO,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACpD,QAAQ,IAAI,IAAI,CAAC;AACjB,QAAQ,IAAI,aAAa,CAAC;AAC1B,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,MAAM,cAAc,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;AAC7D,UAAU,MAAM,iBAAiB,GAAG,cAAc,CAAC,UAAU,EAAE,CAAC;AAChE,UAAU,IAAI,GAAG,iBAAiB,CAAC;AACnC,SAAS,MAAM;AACf,UAAU,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;AACzE,YAAY,aAAa,EAAE,CAAC;AAC5B,WAAW,CAAC,CAAC;AACb,SAAS;AACT,QAAQ,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC7C,UAAU,aAAa,GAAG,IAAI,CAAC;AAC/B,UAAU,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc;AACtD,YAAY,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC;AAC3C,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;AAC9D,QAAQ,UAAU,KAAK,UAAU,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AAC1E,QAAQ,OAAO;AACf,UAAU,IAAI;AACd,UAAU,aAAa;AACvB,UAAU,UAAU;AACpB,UAAU,OAAO,EAAE,OAAO;AAC1B,UAAU,MAAM;AAChB,SAAS,CAAC;AACV,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,sBAAsB,CAAC;AAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,YAAY,CAAC,KAAK,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE;AAC3F,MAAM,YAAY,CAAC,yBAAyB,EAAE,CAAC;AAC/C,MAAM,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACzF,KAAK;AACL,IAAI,IAAI,YAAY,CAAC,mBAAmB,EAAE;AAC1C,MAAM,MAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,GAAG,MAAM,CAAC;AACrE,MAAM,MAAM,WAAW,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;AACjE,MAAM,sBAAsB,GAAG;AAC/B,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE;AACvG,QAAQ,cAAc,EAAE,OAAO;AAC/B,QAAQ,aAAa;AACrB,QAAQ,eAAe,EAAE,CAAC;AAC1B,QAAQ,WAAW;AACnB,QAAQ,YAAY,EAAE,OAAO;AAC7B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAC5B,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH,EAAE,KAAK,CAAC,YAAY,EAAE,KAAK,GAAG,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE;AAC1D,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;AAC5C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;AAC9B,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,KAAK,IAAI,CAAC;AACvD,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC,oBAAoB,EAAE,CAAC;AAC3D,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACvF,MAAM,MAAM,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;AAC/E,MAAM,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7F,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC;AACxB,MAAM,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;AAClD,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AACtE,KAAK;AACL,GAAG;AACH,EAAE,mBAAmB,CAAC,YAAY,EAAE;AACpC,IAAI,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;AAC/B,IAAI,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAClD,IAAI,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AAC5D,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;AACpD,QAAQ,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,UAAU;AACxD,UAAU,QAAQ;AAClB,SAAS,CAAC;AACV,QAAQ,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,GAAG,eAAe,GAAG,QAAQ,CAAC;AAChF,QAAQ,IAAI;AACZ,UAAU,OAAO,CAAC,SAAS,CAAC;AAC5B,YAAY,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM;AAC7C,YAAY,KAAK,EAAE,eAAe,CAAC,eAAe,GAAG,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,iBAAiB,GAAG,eAAe,CAAC,QAAQ;AAC5I,YAAY,MAAM,EAAE,YAAY;AAChC,YAAY,SAAS;AACrB,WAAW,CAAC,CAAC;AACb,SAAS,CAAC,OAAO,CAAC,EAAE;AACpB,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3B,SAAS;AACT,QAAQ,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAC9C,OAAO;AACP,MAAM,eAAe,CAAC,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC;AAC3D,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE;AACzC,QAAQ,MAAM,WAAW,GAAG,IAAI,aAAa,CAAC;AAC9C,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,WAAW,EAAE,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,QAAQ,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;AACtD,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,eAAe,CAAC,IAAI,EAAE;AAC9B,MAAM,eAAe,CAAC,WAAW,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,YAAY,CAAC,mBAAmB,EAAE;AAC5C,QAAQ,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;AAChE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG;AACH,EAAE,sBAAsB,CAAC,eAAe,EAAE;AAC1C,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAClD,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,IAAI,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACtD,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,eAAe,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AAC5C,IAAI,eAAe,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,yBAAyB,CAAC,YAAY,EAAE;AAC1C,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACtF,IAAI,IAAI,YAAY,CAAC,mBAAmB,IAAI,eAAe,CAAC,IAAI,EAAE;AAClE,MAAM,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC;AAC9D,KAAK;AACL,GAAG;AACH,EAAE,qBAAqB,CAAC,YAAY,EAAE;AACtC,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACtF,IAAI,eAAe,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AAC/C,IAAI,eAAe,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACjD,IAAI,IAAI,eAAe,CAAC,IAAI,EAAE;AAC9B,MAAM,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AAC9D,QAAQ,MAAM,WAAW,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC5D,QAAQ,WAAW,EAAE,MAAM;AAC3B,UAAU,YAAY,CAAC,MAAM,CAAC,KAAK;AACnC,UAAU,YAAY,CAAC,MAAM,CAAC,MAAM;AACpC,UAAU,YAAY,CAAC,MAAM,CAAC,WAAW;AACzC,SAAS,CAAC;AACV,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,qBAAqB,SAAS,kBAAkB,CAAC;AACvD,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,sBAAsB,EAAE,CAAC;AAChD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH,CAAC;AACD,qBAAqB,CAAC,SAAS,GAAG;AAClC,EAAE,IAAI,EAAE,CAAC,aAAa,CAAC,YAAY,CAAC;AACpC,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,eAAe,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/D,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,gCAAgC,CAAC;AAC1E,GAAG;AACH,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AAC3F,GAAG;AACH,EAAE,qBAAqB,CAAC,OAAO,EAAE;AACjC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,qBAAqB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1G,IAAI,MAAM,kBAAkB,GAAG,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC;AAChE,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG;AAC/C,MAAM,UAAU;AAChB,MAAM,QAAQ,EAAE,MAAM,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;AAC/D,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG,EAAE,CAAC;AAC/B,mBAAmB,CAAC,MAAM,GAAG;AAC7B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,GAAG,GAAG;AAC1B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,QAAQ,GAAG;AAC/B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,MAAM,GAAG;AAC7B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,eAAe;AAC9B,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,OAAO,GAAG;AAC9B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,eAAe;AAC9B,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,IAAI,GAAG;AAC3B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,MAAM;AACrB,IAAI,SAAS,EAAE,MAAM;AACrB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,YAAY,CAAC,GAAG;AACpC,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,SAAS,CAAC,GAAG;AACjC,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,YAAY,CAAC,GAAG;AACpC,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,WAAW;AAC1B,IAAI,SAAS,EAAE,eAAe;AAC9B,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,KAAK,GAAG;AAC5B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,MAAM;AACrB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,MAAM;AACrB,IAAI,SAAS,EAAE,eAAe;AAC9B,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,GAAG,GAAG;AAC1B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,GAAG,GAAG;AAC1B,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,CAAC,CAAC;AACF,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AACnC,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,KAAK,EAAE;AACzB,IAAI,MAAM,KAAK,GAAG,mBAAmB,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,MAAM,CAAC;AACrF,IAAI,OAAO;AACX,MAAM;AACN,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,KAAK;AACb,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACpB,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,CAAC;AACF,MAAM,4BAA4B,GAAG;AACrC,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;AAClC,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,KAAK,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACrE,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC;AACtD,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY;AACjC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE;AAC7B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM,EAAE,CAAC;AACjB,QAAQ,YAAY,EAAE,MAAM,CAAC,WAAW;AACxC,QAAQ,WAAW,EAAE,MAAM,CAAC,WAAW,GAAG,aAAa;AACvD,OAAO;AACP,MAAM;AACN,QAAQ,KAAK,EAAE,MAAM,CAAC,UAAU;AAChC,QAAQ,MAAM,EAAE,MAAM,CAAC,WAAW;AAClC,QAAQ,kBAAkB,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;AACF,MAAM,YAAY,GAAG;AACrB,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACpE,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACrE,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACrE,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACrE,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACpE,EAAE,iBAAiB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACtE,EAAE,gBAAgB,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AACrE,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;AAC1E,MAAM,kCAAkC,GAAG;AAC3C,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;AAClC,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;AACrC,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC;AACvC,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC;AACtE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC;AAC5F,MAAM,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY;AACnC,QAAQ;AACR,UAAU,OAAO,EAAE,UAAU;AAC7B,UAAU,QAAQ,EAAE,CAAC;AACrB,SAAS;AACT,QAAQ,WAAW;AACnB,QAAQ;AACR,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,WAAW;AACrB,SAAS;AACT,QAAQ;AACR,UAAU,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,UAAU;AAClF,UAAU,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,WAAW;AACtF,UAAU,kBAAkB,EAAE,CAAC;AAC/B,SAAS;AACT,OAAO,CAAC;AACR,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,sBAAsB,GAAG;AAC/B,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;AAClC,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACrC,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC;AACxF,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5F,IAAI,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,KAAK,6BAA6B,CAAC;AAClF,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B;AAC/C,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC1B,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE;AACjD,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;AACF,MAAM,sBAAsB,GAAG;AAC/B,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE;AAClC,IAAI,sBAAsB,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;AAC3D,GAAG;AACH,CAAC,CAAC;AACF,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,kBAAkB,CAAC,MAAM,EAAE;AAC7B,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AACpC,QAAQ,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;AACjE,UAAU,IAAI;AACd;AACA,YAAY,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,CAAC;AACrB,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;AAClD,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,MAAM,EAAE;AAChB,UAAU,MAAM,EAAE,IAAI,CAAC,kBAAkB;AACzC,UAAU,UAAU,EAAE,YAAY;AAClC,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,UAAU,MAAM,EAAE,IAAI,CAAC,kBAAkB;AACzC,UAAU,UAAU,EAAE,cAAc;AACpC,UAAU,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;AAC/B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC7D,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,EAAE;AAClE,MAAM,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AAC1F,KAAK;AACL,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC;AAC7B,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;AAC5D,IAAI,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC,iBAAiB,CAAC;AAC7E,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,MAAM,oBAAoB,GAAG;AACnC,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7C,UAAU,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,UAAU,kBAAkB,EAAE,eAAe;AAC7C,SAAS;AACT,QAAQ,MAAM,EAAE,OAAO,CAAC,MAAM;AAC9B,QAAQ,KAAK,EAAE,eAAe,CAAC,eAAe,GAAG,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,iBAAiB;AAC7G,QAAQ,aAAa,EAAE,OAAO,CAAC,aAAa,GAAG,CAAC;AAChD,OAAO,CAAC;AACR,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;AAChE,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAI,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,eAAe,EAAE,EAAE,UAAU,EAAE;AACzE,MAAM,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;AACvC,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,aAAa,EAAE,CAAC;AACxB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,cAAc,EAAE,UAAU;AAClC,QAAQ,eAAe,EAAE,CAAC;AAC1B,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,WAAW,GAAG,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE;AACtD,QAAQ,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC;AAC9C,UAAU,YAAY,EAAE,WAAW,EAAE;AACrC,UAAU,aAAa,EAAE,CAAC;AAC1B,UAAU,SAAS,EAAE,IAAI;AACzB,UAAU,cAAc,EAAE,UAAU;AACpC,UAAU,eAAe,EAAE,CAAC;AAC5B,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,WAAW,GAAG,cAAc,CAAC,eAAe,CAAC;AAC3D,UAAU,gBAAgB,EAAE,CAAC;AAC7B,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,MAAM,EAAE,OAAO;AAC3B,YAAY,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAClD,WAAW,CAAC;AACZ,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC;AACtD,UAAU,MAAM,EAAE,eAAe;AACjC,UAAU,OAAO,EAAE,CAAC;AACpB,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,QAAQ,EAAE,IAAI,CAAC,OAAO;AAClC,WAAW,EAAE;AACb,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,QAAQ,EAAE,OAAO;AAC7B,WAAW,CAAC;AACZ,SAAS,CAAC,CAAC;AACX,QAAQ,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC1C,QAAQ,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,QAAQ,WAAW,CAAC,GAAG,EAAE,CAAC;AAC1B,QAAQ,OAAO,GAAG,OAAO,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,MAAM,YAAY,GAAG;AAC3B,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;AAC3C,QAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7C,QAAQ,kBAAkB,EAAE,eAAe;AAC3C,OAAO,CAAC;AACR,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE;AACtD,QAAQ,cAAc,CAAC,oBAAoB,CAAC;AAC5C,UAAU,OAAO,EAAE,UAAU;AAC7B,UAAU,QAAQ,EAAE,CAAC,GAAG,CAAC;AACzB,SAAS,EAAE;AACX,UAAU,OAAO;AACjB,UAAU,QAAQ,EAAE,CAAC;AACrB,SAAS,EAAE,YAAY,CAAC,CAAC;AACzB,QAAQ,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAC/D,QAAQ,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjE,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,CAAC;AACD,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,YAAY,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,gBAAgB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,QAAQ,GAAG;AACpB,MAAM,KAAK,EAAE,sBAAsB;AACnC,MAAM,MAAM,EAAE,4BAA4B;AAC1C,MAAM,KAAK,EAAE,sBAAsB;AACnC,MAAM,UAAU,EAAE,kCAAkC;AACpD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC9D,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AAC/D,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACjE,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AACnE,GAAG;AACH,EAAE,aAAa,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACpB,GAAG;AACH,EAAE,UAAU,CAAC,MAAM,EAAE;AACrB,IAAI,IAAI,MAAM,CAAC,mBAAmB,EAAE;AACpC,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;AAC/E,MAAM,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;AACzE,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,eAAe,CAAC,eAAe,GAAG,eAAe,CAAC,QAAQ,CAAC;AAC3E,IAAI,IAAI,MAAM,CAAC,cAAc,KAAK,YAAY,EAAE;AAChD,MAAM,KAAK,IAAI,eAAe,CAAC,iBAAiB,CAAC;AACjD,MAAM,KAAK,IAAI,eAAe,CAAC,QAAQ,CAAC;AACxC,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;AACtG,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC;AAC7F,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC;AACjG,IAAI,MAAM,iBAAiB,GAAG;AAC9B,MAAM,KAAK,EAAE,MAAM,CAAC,KAAK;AACzB,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7B,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM;AAC3B,MAAM,WAAW,EAAE,MAAM,CAAC,WAAW;AACrC,MAAM,aAAa,EAAE,MAAM,CAAC,aAAa;AACzC,MAAM,SAAS,EAAE,MAAM,CAAC,SAAS;AACjC,MAAM,KAAK;AACX,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC9C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAChD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACvD,MAAM,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrD,MAAM,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAC7D,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,UAAU;AACnB,MAAM,OAAO;AACb,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;AAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,aAAa,GAAG,CAAC,EAAE;AAChE,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACnC,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACpD,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC1C,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAChC,MAAM,IAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,eAAe,CAAC,MAAM,EAAE;AAC1B,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AACtD,IAAI,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC9B,KAAK,MAAM,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;AACnG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC/C,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC9B,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,OAAO,EAAE;AACxB,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACrF,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,aAAa,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,YAAY,CAAC,MAAM,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACnE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACrF,GAAG;AACH,EAAE,uBAAuB,CAAC,OAAO,EAAE;AACnC,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC;AACrD,MAAM,CAAC,EAAE,MAAM;AACf,MAAM,CAAC,EAAE,MAAM,CAAC,KAAK;AACrB,MAAM,CAAC,EAAE,IAAI,YAAY,CAAC;AAC1B,QAAQ,cAAc,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE;AACtF,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,kBAAkB,CAAC,OAAO,EAAE;AAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;AACjF,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;AACtE,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACnD,IAAI,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;AAC7C,IAAI,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;AAC/C,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAI,OAAO,CAAC,SAAS,CAAC;AACtB,MAAM,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM;AACjC,MAAM,KAAK,EAAE,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ;AAChE,MAAM,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,wBAAwB,EAAE;AAC5E,MAAM,SAAS,EAAE,eAAe;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,cAAc,CAAC,oBAAoB,CAAC;AACxC,MAAM,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC;AAC5D,MAAM,MAAM,EAAE;AACd,QAAQ,CAAC,EAAE,CAAC;AACZ,QAAQ,CAAC,EAAE,CAAC;AACZ,OAAO;AACP,KAAK,EAAE;AACP,MAAM,OAAO,EAAE,OAAO,CAAC,iBAAiB,EAAE;AAC1C,KAAK,EAAE;AACP,MAAM,KAAK,EAAE,MAAM,CAAC,KAAK;AACzB,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM;AAC3B,KAAK,CAAC,CAAC;AACP,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAChE,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,SAAS,CAAC,OAAO,EAAE;AACrB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACtD,IAAI,MAAM,gBAAgB,GAAG,UAAU,CAAC,0BAA0B,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AAC5G,IAAI,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC7C,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,IAAI,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC;AAC3C,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAChE,IAAI,MAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChE,IAAI,UAAU,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;AACxD,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AACnF,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;AACtD,MAAM,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACjD,MAAM,SAAS,EAAE,OAAO,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,aAAa,GAAG,IAAI,YAAY,CAAC;AAC3C,MAAM,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACpE,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC1E,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,UAAU,GAAG,2BAA2B,CAAC;AACnD,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ;AAChB,QAAQ,uBAAuB,CAAC,sBAAsB,EAAE,CAAC;AACzD,QAAQ,qBAAqB;AAC7B,QAAQ,cAAc;AACtB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;AAC7B,MAAM,UAAU;AAChB,MAAM,SAAS,EAAE;AACjB;AACA,QAAQ,aAAa;AACrB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,EAAE;AACpC,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC;AACvD,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;AAC3C,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC;AACnD,IAAI,MAAM;AACV,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACpD,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AAC7D,IAAI,MAAM,uBAAuB,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AACtE,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACxE,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACvH,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AAC/D,IAAI,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC;AAC9C,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC;AACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;AAC3D,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACvC,QAAQ,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AAClC,QAAQ,OAAO,CAAC,sCAAsC;AACtD,UAAU,OAAO,CAAC,QAAQ;AAC1B,UAAU,MAAM,CAAC,UAAU;AAC3B,UAAU,YAAY,CAAC,KAAK;AAC5B,UAAU,KAAK,CAAC,QAAQ;AACxB,SAAS,CAAC;AACV,OAAO;AACP,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC;AACzC,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC/B,QAAQ,MAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC5C,QAAQ,KAAK,CAAC,SAAS,GAAG,wBAAwB,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9F,QAAQ,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,YAAY;AAC5D,UAAU,KAAK,CAAC,SAAS;AACzB,UAAU,MAAM,CAAC,UAAU;AAC3B,UAAU,CAAC;AACX,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AAClE,MAAM,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACxE,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,kBAAkB,CAAC,SAAS,GAAG;AAC/B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,kBAAkB;AACpC,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,cAAc,CAAC;AACrB,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,UAAU,GAAG,2BAA2B,CAAC;AACnD,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE;AACZ,QAAQ,eAAe;AACvB,QAAQ,UAAU;AAClB,QAAQ,cAAc;AACtB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC;AAC9B,MAAM,UAAU;AAChB,MAAM,SAAS,EAAE;AACjB,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO;AACvC,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK;AAC7C,QAAQ,eAAe,EAAE;AACzB,UAAU,cAAc,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;AACtE,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AAC1B,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5E,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACnC,MAAM,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACzD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACzC,IAAI,IAAI,UAAU,CAAC,wBAAwB,EAAE;AAC7C,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,uBAAuB,EAAE;AAC5C,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;AACnD,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACpG,KAAK;AACL,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,MAAM,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC9B,MAAM,MAAM;AACZ,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,kBAAkB;AACpC,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,CAAC;AACF,MAAM,oBAAoB,GAAG;AAC7B,EAAE,GAAG,aAAa;AAClB,EAAE,YAAY;AACd,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,EAAE,eAAe;AACjB,EAAE,gBAAgB;AAClB,EAAE,qBAAqB;AACvB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,EAAE,cAAc;AAChB,EAAE,kBAAkB;AACpB,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG,CAAC,GAAG,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;AACvE,MAAM,qBAAqB,GAAG,CAAC,eAAe,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;AACpF,MAAM,OAAO,GAAG,EAAE,CAAC;AACnB,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAClE,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACrE,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AACnF,UAAU,CAAC,GAAG,CAAC,GAAG,oBAAoB,EAAE,GAAG,kBAAkB,EAAE,GAAG,qBAAqB,CAAC,CAAC;AACzF,MAAM,cAAc,SAAS,gBAAgB,CAAC;AAC9C,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,IAAI,EAAE,YAAY,CAAC,MAAM;AAC/B,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,kBAAkB;AACxB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACxB,GAAG;AACH;;;;"}