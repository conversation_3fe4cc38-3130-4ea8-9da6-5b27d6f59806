# CartonCloud Local Documentation Directories
# Add one directory path per line for local documents to include in the RAG vector store
# Lines starting with # are comments and will be ignored
# Use absolute paths or paths relative to the project root

# Default directories (these are scanned automatically)
docs/cartoncloud
data/cartoncloud

# Common user directories (modify as needed)
# C:\Users\<USER>\Documents\CartonCloud
# C:\Users\<USER>\Downloads\CartonCloud
# C:\CartonCloud\Documentation

# Project-specific directories
# docs/api
# docs/user-guides
# docs/rate-cards

# Network or shared directories
# \\server\shared\CartonCloud\Docs
# Z:\CartonCloud\Documentation
