#!/usr/bin/env python3
"""
Simple Claude API test for CartonCloud integration.
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from src.utils.utils import get_llm_model
from langchain_core.messages import HumanMessage

def test_claude_basic():
    """Test basic Claude functionality using the project's LLM factory."""
    print("🤖 Testing Claude API via project LLM factory...")
    
    try:
        # Use the project's LLM factory which handles initialization properly
        claude = get_llm_model(
            provider="anthropic",
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1
        )
        
        # Test message
        message = HumanMessage(content="Explain CartonCloud rate cards in 2 sentences.")
        response = claude.invoke([message])
        
        print("✅ Claude API working!")
        print(f"Response: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ Claude test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_claude_cartoncloud_specific():
    """Test Claude with CartonCloud-specific queries."""
    print("\n🏗️ Testing Claude with CartonCloud scenarios...")
    
    try:
        claude = get_llm_model(
            provider="anthropic", 
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1
        )
        
        # CartonCloud-specific test queries
        queries = [
            "How would you approach debugging a CartonCloud invoice discrepancy?",
            "What steps would you take to update rate cards via API?",
            "Explain the OAuth authentication flow for CartonCloud API."
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n📝 Query {i}: {query}")
            message = HumanMessage(content=query)
            response = claude.invoke([message])
            print(f"🤖 Claude: {response.content[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ CartonCloud-specific test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Claude API Integration Test for CartonCloud")
    print("=" * 60)
    
    basic_success = test_claude_basic()
    
    if basic_success:
        cartoncloud_success = test_claude_cartoncloud_specific()
        
        if cartoncloud_success:
            print("\n🎉 Claude integration is working perfectly!")
            print("\n📋 How to use Claude in your CartonCloud agent:")
            print("1. In the web UI, set LLM Provider to 'anthropic'")
            print("2. Set Model Name to 'claude-3-5-sonnet-20241022'")
            print("3. Claude will handle all CartonCloud automation tasks")
            print("4. The RAG system will provide documentation context")
        else:
            print("\n⚠️ Basic Claude works, but CartonCloud integration needs refinement")
    else:
        print("\n❌ Claude API not accessible. Check your ANTHROPIC_API_KEY")
