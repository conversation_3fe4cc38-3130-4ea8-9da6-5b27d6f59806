# CartonCloud Test Guide

## Overview
This is a test documentation file for CartonCloud to validate the enhanced RAG system.

## Rate Cards
Rate cards in CartonCloud define pricing structures for various services:
- Warehousing rates
- Transport rates
- Additional service charges

### Rate Card Creation
To create a new rate card:
1. Navigate to Settings > Rate Cards
2. Click "Add New Rate Card"
3. Configure pricing tiers and zones
4. Set effective dates

## API Integration
CartonCloud provides REST API endpoints for:
- Customer management
- Order processing
- Rate card updates
- Billing operations

### Authentication
API authentication uses OAuth 2.0 with bearer tokens.

Example request:
```
GET /api/customers
Authorization: Bearer {token}
```

## Troubleshooting
Common issues and solutions:
- Rate card sync errors
- API authentication failures
- Invoice generation problems
