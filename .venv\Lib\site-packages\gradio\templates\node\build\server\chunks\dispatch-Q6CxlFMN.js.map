{"version": 3, "file": "dispatch-Q6CxlFMN.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/dispatch.js"], "sourcesContent": ["var noop = { value: () => {\n} };\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || t in _ || /[\\s.]/.test(t))\n      throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\nfunction Dispatch(_) {\n  this._ = _;\n}\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0)\n      name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t))\n      throw new Error(\"unknown type: \" + t);\n    return { type: t, name };\n  });\n}\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._, T = parseTypenames(typename + \"\", _), t, i = -1, n = T.length;\n    if (arguments.length < 2) {\n      while (++i < n)\n        if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name)))\n          return t;\n      return;\n    }\n    if (callback != null && typeof callback !== \"function\")\n      throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type)\n        _[t] = set(_[t], typename.name, callback);\n      else if (callback == null)\n        for (t in _)\n          _[t] = set(_[t], typename.name, null);\n    }\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _)\n      copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0)\n      for (var args = new Array(n), i = 0, n, t; i < n; ++i)\n        args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type))\n      throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i)\n      t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type))\n      throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i)\n      t[i].value.apply(that, args);\n  }\n};\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null)\n    type.push({ name, value: callback });\n  return type;\n}\nexport {\n  dispatch as d\n};\n"], "names": [], "mappings": "AAAA,IAAI,IAAI,GAAG,EAAE,KAAK,EAAE,MAAM;AAC1B,CAAC,EAAE,CAAC;AACJ,SAAS,QAAQ,GAAG;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAC/D,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,MAAM,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACd,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrB,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACb,CAAC;AACD,SAAS,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE;AAC1C,EAAE,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AACzD,IAAI,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,IAAI,CAAC;AACd,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC;AACrC,MAAM,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;AAC7B,GAAG,CAAC,CAAC;AACL,CAAC;AACD,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG;AAC1C,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,EAAE,EAAE,SAAS,QAAQ,EAAE,QAAQ,EAAE;AACnC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,QAAQ,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAClF,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,MAAM,OAAO,EAAE,CAAC,GAAG,CAAC;AACpB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC1E,UAAU,OAAO,CAAC,CAAC;AACnB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU;AAC1D,MAAM,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,QAAQ,CAAC,CAAC;AACvD,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE;AACpB,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI;AACpC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAClD,WAAW,IAAI,QAAQ,IAAI,IAAI;AAC/B,QAAQ,KAAK,CAAC,IAAI,CAAC;AACnB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,IAAI,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAC9B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;AACnB,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AAC7B,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE;AAC7B,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC;AACtC,MAAM,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;AAC3D,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;AACpC,MAAM,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;AAC1D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;AACpC,MAAM,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC;AAC9D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACnC,GAAG;AACH,CAAC,CAAC;AACF,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE;AACzB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAClD,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI,EAAE;AACrC,MAAM,OAAO,CAAC,CAAC,KAAK,CAAC;AACrB,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACnC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAC/C,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC/B,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxE,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,IAAI,QAAQ,IAAI,IAAI;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AACzC,EAAE,OAAO,IAAI,CAAC;AACd;;;;"}