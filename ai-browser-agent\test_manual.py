#!/usr/bin/env python3
"""Standalone test to verify CartonCloud API functionality."""

import sys
import os

# Add the current directory to the path so we can import src modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.integrations.cartoncloud.api import CartonCloudAPI, CartonCloudAPIError

def test_error_handling_structure():
    """Test CartonCloudAPIError structure."""
    print("Testing CartonCloudAPIError structure...")
    error = CartonCloudAPIError("Test error", "E001", 400)
    assert error.message == "Test error"
    assert error.code == "E001" 
    assert error.status_code == 400
    
    expected_str = "CartonCloud API Error [E001]: Test error"
    actual_str = str(error)
    assert actual_str == expected_str, f"Expected '{expected_str}', got '{actual_str}'"
    print("✅ Error handling structure test passed")

def test_api_initialization():
    """Test API client initialization."""
    print("Testing API initialization...")
    api = CartonCloudAPI(
        client_id="test_client_id",
        client_secret="test_client_secret"
    )
    assert api.base_url == "https://api.cartoncloud.com"
    assert api.client_id == "test_client_id"
    assert api.client_secret == "test_client_secret"
    print("✅ API initialization test passed")

if __name__ == "__main__":
    print("Running CartonCloud API tests...")
    try:
        test_error_handling_structure()
        test_api_initialization()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
