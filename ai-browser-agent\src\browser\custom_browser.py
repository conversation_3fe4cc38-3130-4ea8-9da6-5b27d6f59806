import asyncio
import pdb

from playwright.async_api import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.async_api import (
    <PERSON><PERSON>er<PERSON>ontext as PlaywrightBrowserContext,
)
from playwright.async_api import (
    Playwright,
    async_playwright,
)
from browser_use.browser.browser import <PERSON><PERSON><PERSON>
from browser_use.browser.context import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserContextConfig
from playwright.async_api import Browser<PERSON>ontext as PlaywrightBrowserContext
import logging

from .custom_context import CustomBrowserContext

logger = logging.getLogger(__name__)


class CustomBrowser(Browser):

    async def new_context(
            self,
            config: BrowserContextConfig = BrowserContextConfig()
    ) -> CustomBrowserContext:
        return CustomBrowserContext(config=config, browser=self)
