"""
Standalone E2E Test Runner for AI Browser Agent

This script runs comprehensive end-to-end tests including:
- UI functionality testing
- Agent behavior testing  
- Edge case handling
- Performance testing
- Bug detection and reporting

Usage:
    python run_e2e_tests.py [--headless] [--scenarios basic,workflows] [--verbose]
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from tests.e2e.test_e2e_framework import E2ETestFramework
from tests.e2e.test_scenarios import TestScenarios, UserQuestions

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('e2e_tests.log')
    ]
)
logger = logging.getLogger(__name__)


class E2ETestRunner:
    """Main test runner for comprehensive E2E testing."""
    
    def __init__(self, headless: bool = True, verbose: bool = False):
        self.headless = headless
        self.verbose = verbose
        self.framework = E2ETestFramework()
        
    async def run_all_tests(self, scenario_types: list = None):
        """Run all E2E tests."""
        print("🚀 Starting Comprehensive E2E Testing for AI Browser Agent")
        print("=" * 70)
        
        try:
            # Setup framework
            await self.framework.setup(headless=self.headless)
            
            # Run UI tests
            print("\n📱 Testing UI Functionality...")
            ui_result = await self.framework.test_ui_functionality()
            self.print_test_result("UI Functionality", ui_result)
            
            # Run scenario tests
            if not scenario_types:
                scenario_types = ["basic", "workflows", "edge_cases"]
                
            all_scenarios = TestScenarios.get_all_scenarios()
            
            for scenario_type in scenario_types:
                if scenario_type in all_scenarios:
                    print(f"\n🎯 Testing {scenario_type.title()} Scenarios...")
                    scenarios = all_scenarios[scenario_type]
                    results = await self.framework.test_agent_scenarios(scenarios)
                    
                    for result in results:
                        self.print_test_result(result["test_name"], result)
            
            # Run edge case tests
            print("\n⚠️ Testing Edge Cases...")
            edge_results = await self.framework.test_edge_cases()
            
            for result in edge_results:
                self.print_test_result(result["test_name"], result)
            
            # Run user question tests
            print("\n❓ Testing with Real User Questions...")
            await self.test_user_questions()
            
        except Exception as e:
            logger.error(f"Test runner error: {e}")
            print(f"❌ Test runner failed: {e}")
        finally:
            await self.framework.teardown()
    
    async def test_user_questions(self):
        """Test with various user questions."""
        question_sets = [
            ("Simple Questions", UserQuestions.SIMPLE_QUESTIONS[:3]),
            ("Complex Questions", UserQuestions.COMPLEX_QUESTIONS[:2]),
        ]
        
        for set_name, questions in question_sets:
            print(f"\n  📝 {set_name}:")
            
            for i, question in enumerate(questions):
                scenario = {
                    "name": f"{set_name.lower().replace(' ', '_')}_{i}",
                    "description": f"User question: {question[:50]}...",
                    "user_input": question,
                    "success_criteria": ["response generated"],
                    "max_steps": 10,
                    "timeout": 60
                }
                
                result = await self.framework.run_scenario_test(scenario)
                self.print_test_result(f"Q{i+1}", result, compact=True)
    
    def print_test_result(self, test_name: str, result: dict, compact: bool = False):
        """Print formatted test result."""
        status = result["status"]
        
        if status == "passed":
            status_icon = "✅"
            status_color = ""
        elif status == "failed":
            status_icon = "❌"
            status_color = ""
        else:
            status_icon = "⏳"
            status_color = ""
        
        if compact:
            print(f"    {status_icon} {test_name}: {status}")
        else:
            print(f"  {status_icon} {test_name}: {status}")
            
            if self.verbose and result.get("errors"):
                for error in result["errors"][:3]:  # Show first 3 errors
                    print(f"      ⚠️ {error}")
            
            if result.get("execution_time"):
                print(f"      ⏱️ Duration: {result['execution_time']:.2f}s")
    
    async def run_bug_detection(self):
        """Run specific tests to detect common bugs."""
        print("\n🐛 Running Bug Detection Tests...")
        
        bug_scenarios = [
            {
                "name": "memory_leak_test",
                "description": "Test for memory leaks with repeated operations",
                "user_input": "Search for 'test' on Google",
                "repeat": 5,
                "success_criteria": ["no memory issues"]
            },
            {
                "name": "concurrent_access_test", 
                "description": "Test concurrent access to browser",
                "user_input": "Go to example.com",
                "concurrent": 3,
                "success_criteria": ["no conflicts"]
            },
            {
                "name": "error_recovery_test",
                "description": "Test error recovery after failures",
                "user_input": "Go to invalid-url.com then go to google.com",
                "success_criteria": ["recovered from error"]
            }
        ]
        
        for scenario in bug_scenarios:
            result = await self.run_bug_test(scenario)
            self.print_test_result(scenario["name"], result)
    
    async def run_bug_test(self, scenario: dict) -> dict:
        """Run a specific bug detection test."""
        test_result = {
            "test_name": scenario["name"],
            "status": "running",
            "errors": [],
            "details": []
        }
        
        try:
            if scenario.get("repeat"):
                # Test for memory leaks
                for i in range(scenario["repeat"]):
                    history = [{"role": "user", "content": scenario["user_input"]}]
                    output = ""
                    async for token in self.framework.run_agent(history):
                        output += token
                    test_result["details"].append(f"Iteration {i+1} completed")
                
            elif scenario.get("concurrent"):
                # Test concurrent access
                tasks = []
                for i in range(scenario["concurrent"]):
                    history = [{"role": "user", "content": scenario["user_input"]}]
                    task = asyncio.create_task(self.collect_agent_output(history))
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        test_result["errors"].append(f"Concurrent task {i+1} failed: {result}")
                    else:
                        test_result["details"].append(f"Concurrent task {i+1} completed")
            
            else:
                # Regular test
                history = [{"role": "user", "content": scenario["user_input"]}]
                output = ""
                async for token in self.framework.run_agent(history):
                    output += token
                test_result["details"].append("Test completed")
            
            test_result["status"] = "passed" if not test_result["errors"] else "failed"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(str(e))
        
        return test_result
    
    async def collect_agent_output(self, history):
        """Helper to collect agent output."""
        output = ""
        async for token in self.framework.run_agent(history):
            output += token
        return output


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run E2E tests for AI Browser Agent")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    parser.add_argument("--scenarios", type=str, help="Comma-separated list of scenario types to run")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--bug-detection", action="store_true", help="Run bug detection tests")
    
    args = parser.parse_args()
    
    scenario_types = None
    if args.scenarios:
        scenario_types = [s.strip() for s in args.scenarios.split(",")]
    
    runner = E2ETestRunner(headless=args.headless, verbose=args.verbose)
    
    try:
        await runner.run_all_tests(scenario_types)
        
        if args.bug_detection:
            await runner.run_bug_detection()
            
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test runner failed: {e}")
        logger.error(f"Test runner error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
