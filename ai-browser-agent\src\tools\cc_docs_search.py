"""Enhanced CartonCloud documentation search tool for RAG queries."""

import os
import logging
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.schema import Document

from .cc_docs_embedder import CartonCloudDocumentEmbedder

logger = logging.getLogger(__name__)


async def search_cc_docs(
    query: str,
    k: int = 5,
    vectorstore_path: Optional[str] = None,
    embedding_model: str = "text-embedding-3-small",
    embedding_provider: str = "openai",
    filter_by_content_type: Optional[str] = None,
    include_score_threshold: float = 0.0
) -> str:
    """Search CartonCloud documentation using vector similarity.
    
    Args:
        query: Search query for CartonCloud concepts
        k: Number of top results to return
        vectorstore_path: Path to Chroma vector store (defaults to env var)
        embedding_model: Embedding model to use
        embedding_provider: Provider for embeddings ('openai' only for now)
        filter_by_content_type: Filter results by content type (optional)
        include_score_threshold: Minimum relevance score (0.0 to 1.0)
        
    Returns:
        Formatted string with top search results
    """
    try:
        # Get vector store path
        if vectorstore_path is None:
            vectorstore_path = os.getenv("CC_DOCSTORE_PATH", ".cache/cc_vectorstore")
        
        vectorstore_dir = Path(vectorstore_path)
        if not vectorstore_dir.exists():
            logger.warning(f"Vector store not found at {vectorstore_path}")
            return "CartonCloud documentation not available. Please run the 'Rebuild CC Docstore' task first."
        
        # Initialize embeddings based on provider
        if embedding_provider == "openai":
            embeddings = OpenAIEmbeddings(model=embedding_model)
        else:
            logger.warning(f"Unsupported embedding provider: {embedding_provider}, falling back to OpenAI")
            embeddings = OpenAIEmbeddings(model=embedding_model)
        
        vectorstore = Chroma(
            persist_directory=str(vectorstore_dir),
            embedding_function=embeddings,
            collection_name="cartoncloud_docs"
        )
        
        # Perform similarity search
        logger.info(f"Searching CartonCloud docs for: {query}")
        
        # Get more results initially for filtering
        search_k = k * 2 if filter_by_content_type else k
        results = vectorstore.similarity_search_with_score(query, k=search_k)
        
        if not results:
            return f"No relevant documentation found for query: {query}"
        
        # Filter results if needed
        filtered_results = []
        for doc, score in results:
            # Apply score threshold
            relevance_score = 1 - score
            if relevance_score < include_score_threshold:
                continue
            
            # Apply content type filter
            if filter_by_content_type:
                doc_content_type = doc.metadata.get("content_type", "").lower()
                if filter_by_content_type.lower() not in doc_content_type:
                    continue
            
            filtered_results.append((doc, score))
            
            # Stop when we have enough results
            if len(filtered_results) >= k:
                break
        
        if not filtered_results:
            filter_msg = f" matching content type '{filter_by_content_type}'" if filter_by_content_type else ""
            threshold_msg = f" above relevance threshold {include_score_threshold}" if include_score_threshold > 0 else ""
            return f"No relevant documentation found for query: {query}{filter_msg}{threshold_msg}"
        
        # Format results
        return _format_search_results(query, filtered_results, filter_by_content_type)
        
    except Exception as e:
        logger.error(f"Error searching CartonCloud docs: {e}")
        return f"Error searching documentation: {str(e)}"


def _format_search_results(
    query: str, 
    results: List[Tuple[Document, float]], 
    content_filter: Optional[str] = None
) -> str:
    """Format search results for display.
    
    Args:
        query: Original search query
        results: List of (document, score) tuples
        content_filter: Content type filter applied
        
    Returns:
        Formatted results string
    """
    formatted_results = []
    formatted_results.append(f"# CartonCloud Documentation Search Results")
    formatted_results.append(f"**Query:** {query}")
    
    if content_filter:
        formatted_results.append(f"**Content Filter:** {content_filter}")
    
    formatted_results.append(f"**Found:** {len(results)} relevant documents")
    formatted_results.append("")
    
    for i, (doc, score) in enumerate(results, 1):
        relevance_score = 1 - score
        
        # Get source information
        source_info = _get_source_info(doc)
        
        # Get content type and loader type
        content_type = doc.metadata.get("content_type", "unknown")
        loader_type = doc.metadata.get("loader_type", "unknown")
        
        # Chunk information
        chunk_info = ""
        if "chunk_index" in doc.metadata:
            chunk_info = f" (chunk {doc.metadata['chunk_index'] + 1}/{doc.metadata['total_chunks']})"
        
        formatted_results.append(f"## Result {i} (relevance: {relevance_score:.3f})")
        formatted_results.append(f"**Source:** {source_info}{chunk_info}")
        formatted_results.append(f"**Type:** {content_type} ({loader_type})")
        formatted_results.append("")
        formatted_results.append("```md")
        formatted_results.append(doc.page_content.strip())
        formatted_results.append("```")
        formatted_results.append("")
    
    return "\n".join(formatted_results)


def _get_source_info(doc: Document) -> str:
    """Extract source information from document metadata.
    
    Args:
        doc: Document to extract source from
        
    Returns:
        Formatted source string
    """
    metadata = doc.metadata
    
    # Check for web source
    if "source_url" in metadata:
        return metadata["source_url"]
    
    # Check for local file source
    if "source_file" in metadata:
        file_path = Path(metadata["source_file"])
        return f"Local file: {file_path.name}"
    
    # Fallback to generic source
    return metadata.get("source", "Unknown source")


async def search_cc_docs_by_type(
    query: str,
    content_type: str,
    k: int = 3
) -> str:
    """Search CartonCloud docs filtered by content type.
    
    Args:
        query: Search query
        content_type: Content type to filter by (api, rate_card, order, user_guide, etc.)
        k: Number of results to return
        
    Returns:
        Formatted search results
    """
    return await search_cc_docs(
        query=query,
        k=k,
        filter_by_content_type=content_type
    )


async def search_cc_api_docs(query: str, k: int = 3) -> str:
    """Search specifically for API-related CartonCloud documentation.
    
    Args:
        query: Search query for API concepts
        k: Number of results to return
        
    Returns:
        Formatted API documentation results
    """
    return await search_cc_docs_by_type(query, "api", k)


async def search_cc_rate_card_docs(query: str, k: int = 3) -> str:
    """Search specifically for rate card-related CartonCloud documentation.
    
    Args:
        query: Search query for rate card concepts
        k: Number of results to return
        
    Returns:
        Formatted rate card documentation results
    """
    return await search_cc_docs_by_type(query, "rate_card", k)


async def rebuild_cc_docstore() -> str:
    """Rebuild the CartonCloud documentation vector store.
    
    Returns:
        Status message about the rebuild operation
    """
    try:
        logger.info("Starting CartonCloud documentation rebuild")
        
        embedder = CartonCloudDocumentEmbedder(
            include_local_docs=True,
            include_web_docs=True
        )
        
        success = embedder.rebuild_vectorstore()
        
        if success:
            stats = embedder.get_stats()
            doc_count = stats.get("document_count", 0)
            
            return f"""
# CartonCloud Documentation Rebuild Complete

**Status:** ✅ Successful
**Total Documents:** {doc_count}
**Vector Store Path:** {stats.get('vectorstore_path')}

## Sources Included:
- **Local Documents:** {'✅' if stats.get('includes_local_docs') else '❌'}
- **Web Documents:** {'✅' if stats.get('includes_web_docs') else '❌'}

The documentation is now ready for searching.
"""
        else:
            return """
# CartonCloud Documentation Rebuild Failed

**Status:** ❌ Failed

Please check the logs for more details. Ensure that:
1. Documentation files are available in the configured directories
2. OpenAI API key is properly configured
3. Required dependencies are installed
"""
            
    except Exception as e:
        logger.error(f"Error rebuilding docstore: {e}")
        return f"""
# CartonCloud Documentation Rebuild Error

**Status:** ❌ Error
**Error:** {str(e)}

Please check the configuration and try again.
"""


def get_vectorstore_stats(vectorstore_path: Optional[str] = None) -> dict:
    """Get comprehensive statistics about the CartonCloud vector store.
    
    Args:
        vectorstore_path: Path to Chroma vector store
        
    Returns:
        Dictionary with vector store statistics
    """
    try:
        embedder = CartonCloudDocumentEmbedder(vectorstore_path=vectorstore_path)
        return embedder.get_stats()
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "path": vectorstore_path or os.getenv("CC_DOCSTORE_PATH", ".cache/cc_vectorstore")
        }
