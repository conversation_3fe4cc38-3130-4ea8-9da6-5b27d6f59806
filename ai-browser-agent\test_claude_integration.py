#!/usr/bin/env python3
"""
Test script demonstrating Claude API integration with CartonCloud RAG system.

This script shows how to:
1. Use <PERSON> as the LLM provider for CartonCloud agent tasks
2. Use OpenAI embeddings for the RAG documentation system  
3. Combine both for intelligent CartonCloud assistance

Usage:
    python test_claude_integration.py
"""

import os
import asyncio
import logging
from pathlib import Path

# Set up environment
import sys
sys.path.append(str(Path(__file__).parent))

from src.tools.cc_docs_search import search_cc_docs
from src.tools.cc_docs_embedder import CartonCloudDocumentEmbedder
from src.utils.utils import get_llm_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_claude_llm():
    """Test Claude API as LLM provider."""
    print("🤖 Testing Claude LLM Integration")
    print("=" * 50)
    
    try:
        # Initialize Claude LLM
        claude_llm = get_llm_model(
            provider="anthropic",
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1
        )
        
        # Test Claude with a simple CartonCloud query
        from langchain_core.messages import HumanMessage
        
        message = HumanMessage(content="""
        You are a CartonCloud expert. Explain what rate cards are and their importance 
        in logistics pricing. Keep it concise and professional.
        """)
        
        print("📤 Sending query to Claude...")
        response = claude_llm.invoke([message])
        
        print("📥 Claude Response:")
        print("-" * 30)
        print(response.content)
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Claude LLM test failed: {e}")
        return False


async def test_rag_system():
    """Test the RAG documentation search system."""
    print("📚 Testing RAG Documentation System")
    print("=" * 50)
    
    try:
        # First, ensure we have a vector store
        embedder = CartonCloudDocumentEmbedder(
            include_local_docs=True,
            include_web_docs=False
        )
        
        # Build the vector store
        print("🔧 Building vector store...")
        await embedder.build_vectorstore()
        
        # Test queries
        test_queries = [
            "What are rate cards in CartonCloud?",
            "How do I authenticate with the CartonCloud API?",
            "What units of measure are supported?",
            "How do charge codes work?"
        ]
        
        print("🔍 Testing RAG searches...")
        for query in test_queries:
            print(f"\nQuery: {query}")
            print("-" * 30)
            
            result = await search_cc_docs(query, k=2)
            if "No relevant documentation found" not in result:
                print("✅ Found relevant content:")
                print(result[:200] + "..." if len(result) > 200 else result)
            else:
                print("⚠️  No content found")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG system test failed: {e}")
        return False


async def test_combined_claude_rag():
    """Test Claude LLM with RAG context for intelligent responses."""
    print("\n🧠 Testing Combined Claude + RAG Integration")
    print("=" * 50)
    
    try:
        # Initialize Claude
        claude_llm = get_llm_model(
            provider="anthropic",
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1
        )
        
        # Get RAG context
        user_question = "How do I create a new rate card using the API?"
        print(f"User Question: {user_question}")
        print()
        
        # Search documentation
        print("🔍 Searching documentation...")
        rag_context = await search_cc_docs(user_question, k=3)
        
        # Create enhanced prompt with RAG context
        if "No relevant documentation found" not in rag_context:
            enhanced_prompt = f"""
You are a CartonCloud expert assistant. A user has asked: "{user_question}"

Here is relevant documentation context:
{rag_context}

Based on this documentation, provide a helpful and accurate answer to the user's question.
Include specific API endpoints, required fields, and example code where applicable.
"""
        else:
            enhanced_prompt = f"""
You are a CartonCloud expert assistant. A user has asked: "{user_question}"

No specific documentation was found, but based on your knowledge of logistics systems 
and APIs, provide a helpful general answer about rate card creation via APIs.
"""
        
        from langchain_core.messages import HumanMessage
        
        print("📤 Sending enhanced query to Claude...")
        response = claude_llm.invoke([HumanMessage(content=enhanced_prompt)])
        
        print("📥 Claude + RAG Response:")
        print("-" * 30)
        print(response.content)
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Combined Claude + RAG test failed: {e}")
        return False


def check_environment():
    """Check if required environment variables are set."""
    print("🔧 Checking Environment Configuration")
    print("=" * 50)
    
    required_vars = {
        "ANTHROPIC_API_KEY": "Claude API access",
        "OPENAI_API_KEY": "OpenAI embeddings (for RAG)"
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}{value[-4:]} ({description})")
        else:
            print(f"❌ {var}: Not set ({description})")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please add them to your .env file.")
        return False
    
    print("\n✅ Environment configured correctly!")
    return True


async def main():
    """Run all tests."""
    print("🚀 CartonCloud Claude API Integration Test")
    print("=" * 60)
    print()
    
    # Check environment
    if not check_environment():
        return
    
    print()
    
    # Test individual components
    claude_success = await test_claude_llm()
    print()
    
    rag_success = await test_rag_system()
    print()
    
    if claude_success and rag_success:
        await test_combined_claude_rag()
    
    print()
    print("🎯 Test Summary")
    print("=" * 30)
    print(f"Claude LLM: {'✅ Pass' if claude_success else '❌ Fail'}")
    print(f"RAG System: {'✅ Pass' if rag_success else '❌ Fail'}")
    print(f"Integration: {'✅ Ready' if claude_success and rag_success else '❌ Not Ready'}")
    
    if claude_success and rag_success:
        print("\n🎉 Your CartonCloud agent is ready to use Claude API!")
        print("\nNext steps:")
        print("1. Configure your browser agent to use provider='anthropic'")
        print("2. Set model_name='claude-3-5-sonnet-20241022'")
        print("3. The RAG system will provide context from your documentation")
        print("4. Claude will generate intelligent responses for CartonCloud tasks")


if __name__ == "__main__":
    asyncio.run(main())
