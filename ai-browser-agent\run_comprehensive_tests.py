"""
Comprehensive Test Suite for AI Browser Agent

This runs all available tests and provides a complete assessment of the application.
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ComprehensiveTestRunner:
    """Runs all available tests and generates comprehensive report."""
    
    def __init__(self):
        self.test_results = []
        self.start_time = time.time()
        
    def run_command_test(self, name: str, command: list, timeout: int = 30) -> dict:
        """Run a command-based test."""
        test_result = {
            "test_name": name,
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "command": " ".join(command),
            "errors": [],
            "output": ""
        }
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd="."
            )
            
            test_result["output"] = result.stdout
            test_result["return_code"] = result.returncode
            
            if result.returncode == 0:
                test_result["status"] = "passed"
            else:
                test_result["status"] = "failed"
                test_result["errors"].append(f"Command failed with code {result.returncode}")
                if result.stderr:
                    test_result["errors"].append(f"Stderr: {result.stderr}")
                    
        except subprocess.TimeoutExpired:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Command timed out after {timeout} seconds")
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Command execution error: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_component_imports(self) -> dict:
        """Test that key components can be imported."""
        test_result = {
            "test_name": "component_imports",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "components_tested": []
        }
        
        components = [
            ("Gradio", "import gradio as gr; print(f'Gradio {gr.__version__}')"),
            ("FastAPI", "from fastapi import FastAPI; print('FastAPI OK')"),
            ("Playwright", "from playwright.async_api import async_playwright; print('Playwright OK')"),
            ("SQLModel", "from sqlmodel import SQLModel; print('SQLModel OK')"),
            ("Gradio UI", "from gradio_ui import GRADIO_AVAILABLE, AGENT_AVAILABLE; print(f'Gradio: {GRADIO_AVAILABLE}, Agent: {AGENT_AVAILABLE}')"),
        ]
        
        for component_name, import_code in components:
            try:
                result = subprocess.run(
                    [sys.executable, "-c", import_code],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    test_result["components_tested"].append(f"✅ {component_name}: {result.stdout.strip()}")
                else:
                    test_result["components_tested"].append(f"❌ {component_name}: {result.stderr.strip()}")
                    test_result["errors"].append(f"{component_name} import failed")
                    
            except Exception as e:
                test_result["components_tested"].append(f"❌ {component_name}: {str(e)}")
                test_result["errors"].append(f"{component_name} import error: {str(e)}")
        
        test_result["status"] = "passed" if len(test_result["errors"]) == 0 else "partial"
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_mock_agent_functionality(self) -> dict:
        """Test the mock agent functionality."""
        test_result = {
            "test_name": "mock_agent_functionality",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "scenarios_tested": []
        }
        
        scenarios = [
            "Look up ALS rate card",
            "Check storage charges for customer XYZ",
            "Update rate card pricing - need approval first",
            "Simulate invoice for last month",
            "Find all rate cards with storage charges"
        ]
        
        try:
            # Import the mock agent
            from gradio_ui import run_agent, AGENT_AVAILABLE
            
            if not AGENT_AVAILABLE:
                test_result["scenarios_tested"].append("✅ Using mock agent (expected)")
            
            for i, scenario in enumerate(scenarios):
                try:
                    history = [{"role": "user", "content": scenario}]
                    response = ""
                    
                    async for token in run_agent(history):
                        response += token
                    
                    # Check response quality
                    if len(response) > 50:
                        test_result["scenarios_tested"].append(f"✅ Scenario {i+1}: Response generated ({len(response)} chars)")
                        
                        # Check for CartonCloud concepts
                        cartoncloud_keywords = ["rate card", "customer", "charges", "invoice", "cartoncloud"]
                        if any(keyword in response.lower() for keyword in cartoncloud_keywords):
                            test_result["scenarios_tested"].append(f"   ✅ Contains CartonCloud concepts")
                        
                        # Check for approval workflow
                        if "approval" in scenario.lower() and any(word in response.lower() for word in ["approval", "confirm", "permission"]):
                            test_result["scenarios_tested"].append(f"   ✅ Mentions approval workflow")
                    else:
                        test_result["errors"].append(f"Scenario {i+1}: Response too short")
                        
                except Exception as e:
                    test_result["errors"].append(f"Scenario {i+1} error: {str(e)}")
            
            test_result["status"] = "passed" if len(test_result["errors"]) == 0 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Mock agent test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_edge_cases(self) -> dict:
        """Test edge case handling."""
        test_result = {
            "test_name": "edge_cases",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "cases_tested": []
        }
        
        edge_cases = [
            ("Empty input", ""),
            ("Very long input", "a" * 1000),
            ("Emoji input", "🤖🚀💻🔥⚡"),
            ("SQL injection attempt", "SELECT * FROM users; DROP TABLE users;"),
            ("Dangerous request", "Delete all rate cards permanently"),
            ("Unicode input", "Héllo Wörld 测试"),
        ]
        
        try:
            from gradio_ui import run_agent
            
            for case_name, case_input in edge_cases:
                try:
                    history = [{"role": "user", "content": case_input}]
                    response = ""
                    
                    async for token in run_agent(history):
                        response += token
                    
                    # Any response without crashing is good for edge cases
                    if isinstance(response, str):
                        test_result["cases_tested"].append(f"✅ {case_name}: Handled gracefully")
                    else:
                        test_result["errors"].append(f"{case_name}: Invalid response type")
                        
                except Exception as e:
                    # Some edge cases might cause exceptions, which is acceptable
                    test_result["cases_tested"].append(f"⚠️ {case_name}: Exception handled ({str(e)[:50]}...)")
            
            test_result["status"] = "passed"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Edge cases test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def run_all_tests(self):
        """Run all available tests."""
        print("🚀 Starting Comprehensive Test Suite for AI Browser Agent")
        print("=" * 70)
        
        # Code quality tests
        print("\n📋 Running Code Quality Tests...")
        
        ruff_result = self.run_command_test(
            "Ruff Linting",
            [sys.executable, "-m", "ruff", "check", "src", "tests", "--statistics"],
            timeout=60
        )
        self.print_test_result("Ruff Linting", ruff_result)
        
        # Component tests
        print("\n🧩 Running Component Tests...")
        
        asyncio.run(self._run_async_tests())
        
        # Generate final report
        self.generate_final_report()
    
    async def _run_async_tests(self):
        """Run async tests."""
        async_tests = [
            ("Component Imports", self.test_component_imports),
            ("Mock Agent Functionality", self.test_mock_agent_functionality),
            ("Edge Cases", self.test_edge_cases),
        ]
        
        for test_name, test_func in async_tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func()
            self.print_test_result(test_name, result)
    
    def print_test_result(self, test_name: str, result: dict):
        """Print formatted test result."""
        status = result["status"]
        
        if status == "passed":
            icon = "✅"
        elif status == "partial":
            icon = "⚠️"
        elif status == "failed":
            icon = "❌"
        else:
            icon = "⏳"
        
        print(f"  {icon} {test_name}: {status.upper()}")
        
        # Show specific details
        if "components_tested" in result:
            for component in result["components_tested"][:3]:
                print(f"    {component}")
        
        if "scenarios_tested" in result:
            for scenario in result["scenarios_tested"][:3]:
                print(f"    {scenario}")
        
        if "cases_tested" in result:
            for case in result["cases_tested"][:3]:
                print(f"    {case}")
        
        if result.get("errors"):
            for error in result["errors"][:2]:
                print(f"    ⚠️ {error}")
    
    def generate_final_report(self):
        """Generate comprehensive final report."""
        passed = len([r for r in self.test_results if r["status"] == "passed"])
        partial = len([r for r in self.test_results if r["status"] == "partial"])
        failed = len([r for r in self.test_results if r["status"] == "failed"])
        total = len(self.test_results)
        
        print(f"\n{'='*70}")
        print("COMPREHENSIVE TEST REPORT")
        print(f"{'='*70}")
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Partial: {partial}")
        print(f"Failed: {failed}")
        
        if total > 0:
            success_rate = (passed + partial * 0.5) / total * 100
            print(f"Success Rate: {success_rate:.1f}%")
        
        print(f"Duration: {time.time() - self.start_time:.2f} seconds")
        
        # Assessment
        if passed == total:
            print("\n🎉 EXCELLENT: All tests passed!")
            assessment = "excellent"
        elif passed + partial >= total * 0.8:
            print("\n✅ GOOD: Most functionality working with minor issues")
            assessment = "good"
        elif passed + partial >= total * 0.5:
            print("\n⚠️ FAIR: Core functionality working but needs improvement")
            assessment = "fair"
        else:
            print("\n❌ POOR: Significant issues need attention")
            assessment = "poor"
        
        # Save detailed report
        report_path = Path("test_reports") / f"comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        report_data = {
            "summary": {
                "total": total,
                "passed": passed,
                "partial": partial,
                "failed": failed,
                "success_rate": success_rate if total > 0 else 0,
                "assessment": assessment,
                "duration": time.time() - self.start_time
            },
            "results": self.test_results,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_path}")
        print(f"{'='*70}")
        
        return assessment


def main():
    """Main entry point."""
    runner = ComprehensiveTestRunner()
    assessment = runner.run_all_tests()
    
    # Exit with appropriate code
    if assessment in ["excellent", "good"]:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
