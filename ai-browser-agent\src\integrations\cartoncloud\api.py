"""Enhanced CartonCloud API client with auto-generated endpoints."""

import importlib.util
import logging
import os
from collections.abc import Iterator
from datetime import datetime
from datetime import timed<PERSON>ta
from pathlib import Path
from typing import Any

from tenacity import retry
from tenacity import stop_after_attempt
from tenacity import wait_exponential

from .auth import get_token

logger = logging.getLogger(__name__)


class CartonCloudAPIError(Exception):
    """CartonCloud API error with structured error information."""

    def __init__(self, message: str, code: str | None = None, status_code: int | None = None):
        super().__init__(message)
        self.message = message
        self.code = code
        self.status_code = status_code

    def __str__(self):
        if self.code:
            return f"CartonCloud API Error [{self.code}]: {self.message}"
        return f"CartonCloud API Error: {self.message}"


class CartonCloudAPI:
    """
    Enhanced CartonCloud API client with auto-generated endpoints.

    This class provides:
    - OAuth2 token management with automatic refresh
    - Access to all auto-generated API endpoints
    - Error handling and pagination helpers
    - Required Accept-Version header management
    """

    def __init__(
        self,
        client_id: str | None = None,
        client_secret: str | None = None,
        base_url: str | None = None,
        tenant_id: str | None = None
    ):
        """
        Initialize CartonCloud API client.

        Args:
            client_id: OAuth2 client ID (defaults to CARTONCLOUD_CLIENT_ID env var)
            client_secret: OAuth2 client secret (defaults to CARTONCLOUD_CLIENT_SECRET env var)
            base_url: API base URL (defaults to CARTONCLOUD_BASE_URL env var)
            tenant_id: Tenant ID for multi-tenant deployments (optional)
        """
        self.client_id = client_id or os.getenv("CARTONCLOUD_CLIENT_ID")
        self.client_secret = client_secret or os.getenv("CARTONCLOUD_CLIENT_SECRET")
        self.base_url = base_url or os.getenv("CARTONCLOUD_BASE_URL", "https://api.cartoncloud.com")
        self.tenant_id = tenant_id

        if not self.client_id or not self.client_secret:
            raise ValueError("CartonCloud client credentials are required")

        self._token = None
        self._token_expires_at = None
        self._generated_client = None

        # Load generated client if available
        self._load_generated_client()

    def _load_generated_client(self):
        """Load the auto-generated API client if available."""
        try:
            client_path = Path(__file__).parent / "cartoncloud_client"
            if client_path.exists():
                # Dynamically import the generated client
                spec = importlib.util.spec_from_file_location(
                    "cartoncloud_client",
                    client_path / "__init__.py"
                )
                if spec and spec.loader:
                    client_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(client_module)

                    # Create client instance with our configuration
                    self._generated_client = client_module.Client(
                        base_url=self.base_url,
                        headers={"Accept-Version": "1"}
                    )
                    logger.info("✅ Loaded auto-generated CartonCloud API client")
                else:
                    logger.warning("⚠️ Could not load auto-generated client spec")
            else:
                logger.warning(f"⚠️ Auto-generated client not found at {client_path}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to load auto-generated client: {e}")

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=2, min=2, max=8))
    def _refresh_token_if_needed(self) -> str:
        """
        Get a valid access token, refreshing if necessary.

        Returns:
            Valid access token

        Raises:
            CartonCloudAPIError: If token refresh fails
        """
        now = datetime.now()

        # Check if we need to refresh the token
        if (self._token is None or
            self._token_expires_at is None or
            now >= self._token_expires_at - timedelta(minutes=5)):

            try:
                logger.debug("Refreshing CartonCloud access token...")
                self._token = get_token(
                    client_id=self.client_id,
                    client_secret=self.client_secret,
                    base_url=self.base_url
                )
                # Assume 1-hour token lifetime (typical for OAuth2)
                self._token_expires_at = now + timedelta(hours=1)
                logger.debug("✅ Access token refreshed successfully")
            except Exception as e:
                raise CartonCloudAPIError(f"Failed to refresh access token: {e}") from e

        return self._token

    @property
    def client(self):
        """
        Get the auto-generated API client with authentication.

        Returns:
            Configured auto-generated client instance

        Raises:
            CartonCloudAPIError: If client is not available or authentication fails
        """
        if not self._generated_client:
            raise CartonCloudAPIError(
                "Auto-generated client not available. Run 'python scripts/fetch_cc_spec.py' first."
            )

        # Ensure we have a valid token
        token = self._refresh_token_if_needed()

        # Update client authentication headers
        self._generated_client.headers.update({
            "Authorization": f"Bearer {token}",
            "Accept-Version": "1"
        })

        return self._generated_client

    def make_request(
        self,
        method: str,
        endpoint: str,
        data: dict[str, Any] | None = None,
        params: dict[str, Any] | None = None,
        **kwargs
    ) -> dict[str, Any]:
        """
        Make authenticated request to CartonCloud API with proper error handling.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint path
            data: Request body data
            params: URL parameters
            **kwargs: Additional request options

        Returns:
            Response JSON data

        Raises:
            CartonCloudAPIError: If request fails with structured error info
        """
        import requests

        token = self._refresh_token_if_needed()
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Accept-Version": "1",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        try:
            response = requests.request(
                method=method.upper(),
                url=url,
                json=data,
                params=params,
                headers=headers,
                timeout=30,
                **kwargs
            )

            # Handle HTTP errors with CartonCloud-specific error parsing
            if not response.ok:
                error_data = {}
                try:
                    error_data = response.json()
                except Exception:
                    pass

                error_message = error_data.get('message', f'HTTP {response.status_code}')
                error_code = error_data.get('code') or error_data.get('error')

                raise CartonCloudAPIError(
                    message=error_message,
                    code=error_code,
                    status_code=response.status_code
                )

            return response.json()

        except requests.RequestException as e:
            raise CartonCloudAPIError(f"Request failed: {e}") from e

    def paginate(
        self,
        endpoint: str,
        method: str = "GET",
        page_param: str = "page",
        size_param: str = "size",
        page_size: int = 100,
        max_pages: int | None = None,
        **request_kwargs
    ) -> Iterator[dict[str, Any]]:
        """
        Auto-paginate through API responses.

        Args:
            endpoint: API endpoint to paginate
            method: HTTP method (usually GET)
            page_param: Query parameter name for page number
            size_param: Query parameter name for page size
            page_size: Number of items per page
            max_pages: Maximum number of pages to fetch (None for all)
            **request_kwargs: Additional request parameters

        Yields:
            Individual items from paginated responses
        """
        page = 1
        pages_fetched = 0

        while True:
            # Check max pages limit
            if max_pages and pages_fetched >= max_pages:
                break

            # Add pagination parameters
            params = request_kwargs.get('params', {}).copy()
            params[page_param] = page
            params[size_param] = page_size
            request_kwargs['params'] = params

            try:
                response = self.make_request(method, endpoint, **request_kwargs)

                # Extract data array (common patterns)
                data = response.get('data', [])
                if not data:
                    data = response.get('items', [])
                if not data:
                    data = response.get('results', [])

                # Yield individual items
                yield from data

                # Check if we should continue
                if len(data) < page_size:
                    # Less than full page returned, we're at the end
                    break

                # Check for explicit pagination info in headers or response
                total_pages = None
                if 'Total-Pages' in getattr(response, 'headers', {}):
                    total_pages = int(response.headers['Total-Pages'])
                elif 'total_pages' in response:
                    total_pages = response['total_pages']

                if total_pages and page >= total_pages:
                    break

                page += 1
                pages_fetched += 1

            except CartonCloudAPIError as e:
                if e.status_code == 404:
                    # No more pages
                    break
                raise

    # Convenience methods for common operations
    def get_rate_cards(self, **params) -> Iterator[dict[str, Any]]:
        """Get all rate cards with pagination."""
        return self.paginate("/v1/rate-cards", params=params)

    def get_purchase_orders(self, **params) -> Iterator[dict[str, Any]]:
        """Get all purchase orders with pagination."""
        return self.paginate("/v1/purchase-orders", params=params)

    def get_sales_orders(self, **params) -> Iterator[dict[str, Any]]:
        """Get all sales orders with pagination."""
        return self.paginate("/v1/sales-orders", params=params)

    def create_purchase_order(self, order_data: dict[str, Any]) -> dict[str, Any]:
        """Create a new purchase order."""
        return self.make_request("POST", "/v1/purchase-orders", data=order_data)

    def create_sales_order(self, order_data: dict[str, Any]) -> dict[str, Any]:
        """Create a new sales order."""
        return self.make_request("POST", "/v1/sales-orders", data=order_data)

    def update_rate_card(self, rate_card_id: str, updates: dict[str, Any]) -> dict[str, Any]:
        """Update an existing rate card."""
        return self.make_request("PUT", f"/v1/rate-cards/{rate_card_id}", data=updates)
