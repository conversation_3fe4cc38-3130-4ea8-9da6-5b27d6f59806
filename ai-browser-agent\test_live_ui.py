"""
Live UI Testing with Browser Automation

This tests the actual running Gradio UI with real browser interactions.
"""

import asyncio
import logging
import time
from datetime import datetime

from playwright.async_api import async_playwright

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LiveUITester:
    """Test the live Gradio UI with browser automation."""
    
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.base_url = "http://127.0.0.1:7860"
        self.test_results = []
        
    async def test_ui_loads_and_responds(self):
        """Test that UI loads and can handle messages."""
        test_result = {
            "test_name": "ui_loads_and_responds",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": []
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            # Navigate to UI
            await page.goto(self.base_url, timeout=30000)
            test_result["steps"].append("✅ Navigated to Gradio UI")
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle", timeout=30000)
            test_result["steps"].append("✅ Page loaded completely")
            
            # Check for main title
            title = await page.locator("h1").first.text_content()
            if "AI Browser Agent" in title:
                test_result["steps"].append("✅ Found main title")
            else:
                test_result["errors"].append("Main title not found")
            
            # Find message input and send a test message
            message_input = page.locator("textarea").first
            test_message = "Look up the rate card for ALS customer"
            await message_input.fill(test_message)
            test_result["steps"].append(f"✅ Filled message: {test_message}")
            
            # Click send button
            send_button = page.locator("button:has-text('Send')")
            await send_button.click()
            test_result["steps"].append("✅ Clicked send button")
            
            # Wait for response
            await asyncio.sleep(5)
            
            # Check for response in the page
            page_content = await page.content()
            if test_message in page_content:
                test_result["steps"].append("✅ User message appeared in chat")
            else:
                test_result["errors"].append("User message not visible")
            
            if "Mock Agent Response" in page_content or "rate card" in page_content.lower():
                test_result["steps"].append("✅ Agent response received")
            else:
                test_result["errors"].append("No agent response detected")
            
            test_result["status"] = "passed" if not test_result["errors"] else "failed"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"UI test failed: {str(e)}")
            logger.error(f"UI test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_cartoncloud_rate_card_scenarios(self):
        """Test CartonCloud rate card specific scenarios."""
        test_result = {
            "test_name": "cartoncloud_rate_card_scenarios",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "scenarios_tested": []
        }
        
        scenarios = [
            {
                "message": "Look up the rate card for ALS customer",
                "expected_concepts": ["rate card", "ALS", "customer"],
                "should_ask_approval": False
            },
            {
                "message": "Show me storage charges for customer XYZ",
                "expected_concepts": ["storage", "charges", "customer"],
                "should_ask_approval": False
            },
            {
                "message": "Update the rate card pricing for customer ABC - increase storage to $50",
                "expected_concepts": ["update", "rate card", "pricing"],
                "should_ask_approval": True
            },
            {
                "message": "Delete the old rate card for customer DEF",
                "expected_concepts": ["delete", "rate card", "customer"],
                "should_ask_approval": True
            },
            {
                "message": "Simulate creating an invoice for last month's charges",
                "expected_concepts": ["simulate", "invoice", "charges"],
                "should_ask_approval": False
            }
        ]
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            for i, scenario in enumerate(scenarios):
                try:
                    # Create new chat for each scenario
                    new_chat_btn = page.locator("button:has-text('+ New Chat')")
                    if await new_chat_btn.count() > 0:
                        await new_chat_btn.click()
                        await asyncio.sleep(1)
                    
                    # Send scenario message
                    message_input = page.locator("textarea").first
                    await message_input.fill(scenario["message"])
                    
                    send_button = page.locator("button:has-text('Send')")
                    await send_button.click()
                    
                    # Wait for response
                    await asyncio.sleep(4)
                    
                    # Get page content to analyze response
                    page_content = await page.content()
                    response_text = page_content.lower()
                    
                    # Check for expected concepts
                    concepts_found = 0
                    for concept in scenario["expected_concepts"]:
                        if concept.lower() in response_text:
                            concepts_found += 1
                    
                    if concepts_found >= len(scenario["expected_concepts"]) * 0.5:  # At least 50% of concepts
                        test_result["scenarios_tested"].append(f"✅ Scenario {i+1}: Concepts understood ({concepts_found}/{len(scenario['expected_concepts'])})")
                    else:
                        test_result["errors"].append(f"Scenario {i+1}: Missing key concepts")
                    
                    # Check approval workflow
                    if scenario["should_ask_approval"]:
                        approval_keywords = ["approval", "confirm", "permission", "authorize", "proceed", "sure"]
                        if any(keyword in response_text for keyword in approval_keywords):
                            test_result["scenarios_tested"].append(f"✅ Scenario {i+1}: Correctly asks for approval")
                        else:
                            test_result["errors"].append(f"Scenario {i+1}: Should ask for approval but doesn't")
                    else:
                        # For non-approval scenarios, check that it doesn't unnecessarily ask for approval
                        test_result["scenarios_tested"].append(f"✅ Scenario {i+1}: Appropriate response level")
                    
                except Exception as e:
                    test_result["errors"].append(f"Scenario {i+1} error: {str(e)}")
            
            test_result["status"] = "passed" if len(test_result["errors"]) <= 1 else "failed"  # Allow 1 minor error
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"CartonCloud scenarios test failed: {str(e)}")
            logger.error(f"CartonCloud scenarios test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_example_buttons(self):
        """Test the example buttons functionality."""
        test_result = {
            "test_name": "example_buttons",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "buttons_tested": []
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            await page.goto(self.base_url, timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            
            # Test example buttons
            example_buttons = [
                "Look up ALS rate card",
                "Check storage charges for customer XYZ",
                "Simulate invoice for last month",
                "Update rate card pricing"
            ]
            
            for button_text in example_buttons:
                try:
                    # Find and click the example button
                    button = page.locator(f"button:has-text('{button_text}')")
                    if await button.count() > 0:
                        await button.click()
                        await asyncio.sleep(1)
                        
                        # Check if text appears in message input
                        message_input = page.locator("textarea").first
                        input_value = await message_input.input_value()
                        
                        if button_text.lower() in input_value.lower():
                            test_result["buttons_tested"].append(f"✅ {button_text}: Text populated correctly")
                        else:
                            test_result["errors"].append(f"{button_text}: Text not populated")
                    else:
                        test_result["errors"].append(f"{button_text}: Button not found")
                        
                except Exception as e:
                    test_result["errors"].append(f"{button_text}: Error - {str(e)}")
            
            test_result["status"] = "passed" if len(test_result["errors"]) == 0 else "partial"
            
            await browser.close()
            await playwright.stop()
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Example buttons test failed: {str(e)}")
            logger.error(f"Example buttons test error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def run_all_tests(self):
        """Run all live UI tests."""
        print("🚀 Starting Live UI Tests")
        print("=" * 50)
        
        # Wait a moment for server to be ready
        print("⏳ Waiting for Gradio server to be ready...")
        await asyncio.sleep(3)
        
        tests = [
            ("UI Loads and Responds", self.test_ui_loads_and_responds),
            ("CartonCloud Rate Card Scenarios", self.test_cartoncloud_rate_card_scenarios),
            ("Example Buttons", self.test_example_buttons),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func()
            self.print_test_result(test_name, result)
        
        self.generate_report()
    
    def print_test_result(self, test_name: str, result: dict):
        """Print test result."""
        status = result["status"]
        icon = "✅" if status == "passed" else "⚠️" if status == "partial" else "❌"
        
        print(f"  {icon} {test_name}: {status.upper()}")
        
        if result.get("steps"):
            for step in result["steps"][:3]:
                print(f"    {step}")
        
        if result.get("scenarios_tested"):
            for scenario in result["scenarios_tested"][:3]:
                print(f"    {scenario}")
        
        if result.get("buttons_tested"):
            for button in result["buttons_tested"][:3]:
                print(f"    {button}")
        
        if result.get("errors"):
            for error in result["errors"][:2]:
                print(f"    ⚠️ {error}")
    
    def generate_report(self):
        """Generate test report."""
        passed = len([r for r in self.test_results if r["status"] == "passed"])
        partial = len([r for r in self.test_results if r["status"] == "partial"])
        failed = len([r for r in self.test_results if r["status"] == "failed"])
        total = len(self.test_results)
        
        print(f"\n{'='*50}")
        print("LIVE UI TEST REPORT")
        print(f"{'='*50}")
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Partial: {partial}")
        print(f"Failed: {failed}")
        
        if total > 0:
            success_rate = (passed + partial * 0.5) / total * 100
            print(f"Success Rate: {success_rate:.1f}%")
        
        if passed == total:
            print("🎉 All live UI tests PASSED!")
        elif passed + partial >= total * 0.8:
            print("✅ Most live UI tests PASSED!")
        else:
            print("⚠️ Some live UI tests need attention")
        
        print(f"{'='*50}")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run live UI tests")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    args = parser.parse_args()
    
    tester = LiveUITester(headless=args.headless)
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
