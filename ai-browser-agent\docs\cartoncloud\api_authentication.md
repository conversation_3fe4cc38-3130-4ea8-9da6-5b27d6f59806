---
title: "CartonCloud API Authentication"
content_type: "api_documentation"
topic: "authentication"
---

# CartonCloud API Authentication

## OAuth 2.0 Client Credentials Flow

CartonCloud uses OAuth 2.0 Client Credentials flow for API authentication. This is a server-to-server authentication method suitable for applications that need to access CartonCloud resources without user interaction.

### Required Credentials

To authenticate with the CartonCloud API, you need:

- **Client ID**: Unique identifier for your application
- **Client Secret**: Secret key for your application
- **Base URL**: CartonCloud API base URL (typically https://api.cartoncloud.com)

### Authentication Process

1. **Request Access Token**
   ```http
   POST /oauth/token
   Content-Type: application/x-www-form-urlencoded
   
   grant_type=client_credentials&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET
   ```

2. **Response**
   ```json
   {
     "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
     "token_type": "Bearer",
     "expires_in": 3600,
     "scope": "api"
   }
   ```

3. **Use Token in API Requests**
   ```http
   GET /api/v2/customers
   Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
   ```

### Token Management

- **Expiration**: Access tokens typically expire after 1 hour (3600 seconds)
- **Refresh**: Request a new token before the current one expires
- **Caching**: Cache tokens to avoid unnecessary authentication requests
- **Security**: Never expose client secrets in client-side code

### Error Handling

Common authentication errors:

- **401 Unauthorized**: Invalid or expired token
- **403 Forbidden**: Insufficient permissions for the requested resource
- **400 Bad Request**: Invalid client credentials or malformed request

### Rate Limiting

The authentication endpoint has rate limits:
- Maximum 100 requests per minute per client
- Use exponential backoff for retry logic
- Cache tokens to minimize authentication requests

### API Scopes

CartonCloud API supports different scopes for fine-grained access control:

- **api**: Full API access (default)
- **read**: Read-only access to resources
- **write**: Create and update permissions
- **delete**: Delete permissions for applicable resources

### Security Best Practices

1. **Secure Storage**: Store client secrets securely using environment variables or secure vaults
2. **HTTPS Only**: Always use HTTPS for API communications
3. **Token Rotation**: Implement automatic token refresh
4. **Monitoring**: Log authentication events for security monitoring
5. **Principle of Least Privilege**: Use the minimum required scopes

### SDK Integration

When using the Python SDK:

```python
from cartoncloud_api import CartonCloudAPI

# Initialize with credentials
api = CartonCloudAPI(
    client_id="your_client_id",
    client_secret="your_client_secret", 
    base_url="https://api.cartoncloud.com"
)

# SDK handles token management automatically
customers = api.get_customers()
```
