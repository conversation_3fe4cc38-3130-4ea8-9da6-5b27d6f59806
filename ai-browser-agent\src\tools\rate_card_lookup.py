"""Rate card lookup tool for CartonCloud price validation."""

import os
from dataclasses import dataclass
from decimal import Decimal
from pathlib import Path
from typing import Any

import yaml

RATECARD_FILE = os.getenv("RATECARD_FILE") or str(
    Path(__file__).parent.parent.parent / "data" / "ratecards" / "als_2024-11-28.yml"
)

@dataclass
class RateCardItem:
    id: str
    service: str
    price_ex_gst: Decimal
    unit: str
    min_charge: Decimal | None = None
    category: str = "general"
    effective_from: str = "2024-11-28"
    notes: str | None = None

@dataclass
class LookupResult:
    service_id: str
    service_name: str
    unit: str
    price_ex_gst: Decimal
    min_charge: Decimal | None
    quantity: Decimal
    extended_price_ex_gst: Decimal
    min_charge_applied: bool
    notes: str | None = None

class RateCardLookup:
    def __init__(self, rate_card_file: str | None = None):
        self.rate_card_file = rate_card_file or RATECARD_FILE
        self._rate_cards: dict[str, RateCardItem] = {}
        self._loaded = False

    def _load_rate_cards(self) -> None:
        if self._loaded:
            return
        path = Path(self.rate_card_file)
        if not path.exists():
            self._loaded = True
            return
        with open(path, encoding="utf-8") as f:
            data = yaml.safe_load(f)
        for item in data.get("rate_cards", []):
            self._rate_cards[item["id"]] = RateCardItem(
                id=item["id"],
                service=item["service"],
                price_ex_gst=Decimal(str(item["price_ex_gst"])),
                unit=item["unit"],
                min_charge=Decimal(str(item["min_charge"])) if item.get("min_charge") else None,
                category=item.get("category", "general"),
                effective_from=item.get("effective_from", "2024-11-28"),
                notes=item.get("notes"),
            )
        self._loaded = True

    def lookup(self, service_id: str, qty: float = 1.0) -> LookupResult | None:
        self._load_rate_cards()
        if service_id not in self._rate_cards:
            return None
        item = self._rate_cards[service_id]
        quantity = Decimal(str(qty))
        extended = item.price_ex_gst * quantity
        min_applied = False
        if item.min_charge and extended < item.min_charge:
            extended = item.min_charge
            min_applied = True
        return LookupResult(
            service_id=item.id,
            service_name=item.service,
            unit=item.unit,
            price_ex_gst=item.price_ex_gst,
            min_charge=item.min_charge,
            quantity=quantity,
            extended_price_ex_gst=extended,
            min_charge_applied=min_applied,
            notes=item.notes,
        )

    def search_by_service_name(self, service_name: str) -> list[RateCardItem]:
        self._load_rate_cards()
        s = service_name.lower()
        matches = [item for item in self._rate_cards.values() if s in item.service.lower()]
        matches.sort(key=lambda x: (s != x.service.lower(), len(x.service), x.service.lower()))
        return matches

    def get_all_services(self) -> list[RateCardItem]:
        self._load_rate_cards()
        return list(self._rate_cards.values())

    def get_categories(self) -> dict[str, list[RateCardItem]]:
        self._load_rate_cards()
        cats: dict[str, list[RateCardItem]] = {}
        for item in self._rate_cards.values():
            cats.setdefault(item.category, []).append(item)
        for cat in cats:
            cats[cat].sort(key=lambda x: x.service)
        return cats

_rate_card_lookup = None

def get_rate_card_lookup() -> RateCardLookup:
    global _rate_card_lookup
    if _rate_card_lookup is None:
        _rate_card_lookup = RateCardLookup()
    return _rate_card_lookup

def lookup(service_id: str, qty: float = 1.0) -> dict[str, Any] | None:
    svc = get_rate_card_lookup().lookup(service_id, qty)
    if not svc:
        return None
    return {
        "service_id": svc.service_id,
        "service_name": svc.service_name,
        "unit": svc.unit,
        "price_ex_gst": float(svc.price_ex_gst),
        "min_charge": float(svc.min_charge) if svc.min_charge else None,
        "quantity": float(svc.quantity),
        "extended_price_ex_gst": float(svc.extended_price_ex_gst),
        "min_charge_applied": svc.min_charge_applied,
        "notes": svc.notes,
    }
