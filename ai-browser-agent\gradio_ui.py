"""
Gradio UI for AI Browser Agent

This provides a working Gradio interface that can function with or without
the full agent dependencies, allowing for testing and development.
"""

import asyncio
import logging
import os
import time
from datetime import datetime
from typing import AsyncIterator, Generator

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try to import Gradio with error handling
try:
    import gradio as gr
    GRADIO_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Gradio import failed: {e}")
    GRADIO_AVAILABLE = False
    # Create a mock gr object for testing
    class MockGradio:
        def __getattr__(self, name):
            return lambda *args, **kwargs: None
    gr = MockGradio()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import the full agent, fall back to mock if not available
try:
    from src.agent.run_agent import run_agent, cleanup_browser
    AGENT_AVAILABLE = True
    logger.info("✅ Full agent functionality available")
except ImportError as e:
    logger.warning(f"⚠️ Agent import failed: {e}")
    logger.info("🔄 Using mock agent for testing")
    AGENT_AVAILABLE = False

    async def run_agent(history):
        """Enhanced agent with comprehensive CartonCloud knowledge."""
        from src.knowledge.cartoncloud_knowledge import cartoncloud_kb

        user_message = history[-1]["content"] if history else "No message"

        # Analyze the request using CartonCloud knowledge
        analysis = cartoncloud_kb.analyze_request(user_message)

        responses = []

        # Header
        responses.append(f"🤖 **AI Browser Agent - CartonCloud Expert**\n\n")
        responses.append(f"📝 **Your Request:** {user_message}\n\n")

        # Analysis
        if analysis["matching_concepts"]:
            responses.append(f"🎯 **Analysis:** I understand you're working with:\n")
            for concept in analysis["matching_concepts"][:3]:  # Show top 3 matches
                responses.append(f"- **{concept.name}** ({concept.module.value}): {concept.description}\n")
            responses.append(f"\n")

        # Operation type
        responses.append(f"🔧 **Operation Type:** {analysis['operation_type'].value.title()}\n\n")

        # Approval workflow
        if analysis["approval_needed"]:
            responses.append(f"⚠️ **APPROVAL REQUIRED**\n")
            responses.append(f"**Level:** {analysis['approval_level'].value.title()}\n")
            responses.append(f"**Reason:** This operation modifies CartonCloud data and requires authorization.\n\n")
            responses.append(f"❓ **Do you want me to proceed with this operation?** (Please confirm)\n\n")
        else:
            responses.append(f"✅ **No Approval Required** - This is a read-only operation.\n\n")

        # Specific guidance based on concepts
        if analysis["matching_concepts"]:
            concept = analysis["matching_concepts"][0]  # Primary concept

            responses.append(f"📋 **Detailed Guidance for {concept.name}:**\n")
            responses.append(f"**Available Operations:** {', '.join([op.value for op in concept.operations])}\n")
            responses.append(f"**API Endpoints:** {', '.join(concept.api_endpoints)}\n")
            responses.append(f"**UI Locations:** {', '.join(concept.ui_locations)}\n\n")

            if concept.business_rules:
                responses.append(f"📏 **Business Rules:**\n")
                for rule in concept.business_rules[:3]:
                    responses.append(f"- {rule}\n")
                responses.append(f"\n")

            if concept.examples:
                responses.append(f"💡 **Examples:**\n")
                for example in concept.examples[:2]:
                    responses.append(f"- {example}\n")
                responses.append(f"\n")

        # Related concepts
        if analysis["matching_concepts"]:
            related = cartoncloud_kb.get_related_concepts(analysis["matching_concepts"][0].name)
            if related:
                responses.append(f"🔗 **Related Concepts:** {', '.join([r.name for r in related[:3]])}\n\n")

        # Workflow if applicable
        if "update" in user_message.lower() and "rate card" in user_message.lower():
            workflow = cartoncloud_kb.get_workflow("rate_card_update")
            if workflow:
                responses.append(f"📋 **Recommended Workflow:**\n")
                for step in workflow[:4]:  # Show first 4 steps
                    responses.append(f"{step}\n")
                responses.append(f"\n")

        # Status and next steps
        responses.append(f"🕒 **Time:** {datetime.now().strftime('%H:%M:%S')}\n")
        responses.append(f"📊 **Confidence:** {analysis['confidence']:.1%}\n")

        if AGENT_AVAILABLE:
            responses.append(f"🚀 **Status:** Full agent available - ready to execute operations\n")
        else:
            responses.append(f"⚠️ **Status:** Knowledge-enhanced mock mode\n")

        responses.append(f"\n✅ **Analysis Complete** - How would you like to proceed?")

        # Stream the response
        for response in responses:
            yield response
            await asyncio.sleep(0.2)

    async def cleanup_browser():
        """Mock cleanup function."""
        pass


# Global state for chat history
chat_sessions = {}
current_session_id = None


def create_new_chat():
    """Create a new chat session."""
    global current_session_id
    session_id = f"chat_{int(time.time())}"
    chat_sessions[session_id] = {
        "id": session_id,
        "title": "New Chat",
        "messages": [],
        "created_at": datetime.now()
    }
    current_session_id = session_id

    # Update chat list
    chat_list = [f"{session['title']} ({session['id']})" for session in chat_sessions.values()]

    return (
        "",  # Clear chat display
        "",  # Clear message input
        gr.Dropdown(choices=chat_list, value=chat_list[-1] if chat_list else None),  # Update chat selector
        f"Created new chat: {session_id}"
    )


def select_chat(chat_selection):
    """Select an existing chat session."""
    global current_session_id

    if not chat_selection:
        return "", "No chat selected"

    # Extract session ID from selection
    session_id = chat_selection.split("(")[-1].rstrip(")")

    if session_id in chat_sessions:
        current_session_id = session_id
        session = chat_sessions[session_id]

        # Rebuild chat display
        chat_display = ""
        for msg in session["messages"]:
            role = msg["role"]
            content = msg["content"]
            timestamp = msg.get("timestamp", "")

            if role == "user":
                chat_display += f"**👤 You** ({timestamp}):\n{content}\n\n"
            else:
                chat_display += f"**🤖 Agent** ({timestamp}):\n{content}\n\n"

        return chat_display, f"Selected chat: {session_id}"

    return "", f"Chat not found: {session_id}"


async def send_message_async(message: str, chat_display: str) -> AsyncIterator[tuple[str, str]]:
    """Send message to agent and stream response."""
    global current_session_id

    if not message.strip():
        yield chat_display, "Please enter a message"
        return

    # Create new chat if none exists
    if not current_session_id or current_session_id not in chat_sessions:
        create_new_chat()

    session = chat_sessions[current_session_id]
    timestamp = datetime.now().strftime("%H:%M:%S")

    # Add user message
    user_msg = {
        "role": "user",
        "content": message,
        "timestamp": timestamp
    }
    session["messages"].append(user_msg)

    # Update chat display with user message
    chat_display += f"**👤 You** ({timestamp}):\n{message}\n\n"
    yield chat_display, "Agent is thinking..."

    # Get agent response
    history = session["messages"]
    agent_response = ""

    try:
        async for token in run_agent(history):
            agent_response += token
            # Update display with streaming response
            current_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{agent_response}\n\n"
            yield current_display, "Agent is responding..."

        # Add complete agent response to session
        agent_msg = {
            "role": "assistant",
            "content": agent_response,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        # Final update
        final_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{agent_response}\n\n"
        yield final_display, "Response completed"

    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        agent_msg = {
            "role": "assistant",
            "content": error_msg,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        error_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{error_msg}\n\n"
        yield error_display, f"Error occurred: {str(e)}"


def send_message_sync(message: str, chat_display: str) -> tuple[str, str]:
    """Synchronous wrapper with enhanced CartonCloud knowledge."""
    if not message.strip():
        return chat_display, "Please enter a message"

    # Create new chat if none exists
    global current_session_id
    if not current_session_id or current_session_id not in chat_sessions:
        create_new_chat()

    session = chat_sessions[current_session_id]
    timestamp = datetime.now().strftime("%H:%M:%S")

    # Add user message
    user_msg = {
        "role": "user",
        "content": message,
        "timestamp": timestamp
    }
    session["messages"].append(user_msg)

    # Update chat display with user message
    chat_display += f"**👤 You** ({timestamp}):\n{message}\n\n"

    # Get enhanced agent response
    try:
        from src.knowledge.cartoncloud_knowledge import cartoncloud_kb

        # Analyze the request
        analysis = cartoncloud_kb.analyze_request(message)

        # Build comprehensive response
        response_parts = []
        response_parts.append("🤖 **AI Browser Agent - CartonCloud Expert**\n\n")
        response_parts.append(f"📝 **Your Request:** {message}\n\n")

        if analysis["matching_concepts"]:
            response_parts.append("🎯 **Analysis:** I understand you're working with:\n")
            for concept in analysis["matching_concepts"][:2]:
                response_parts.append(f"- **{concept.name}**: {concept.description}\n")
            response_parts.append("\n")

        response_parts.append(f"🔧 **Operation Type:** {analysis['operation_type'].value.title()}\n\n")

        if analysis["approval_needed"]:
            response_parts.append("⚠️ **APPROVAL REQUIRED**\n")
            response_parts.append(f"**Level:** {analysis['approval_level'].value.title()}\n")
            response_parts.append("**Reason:** This operation modifies CartonCloud data.\n\n")
            response_parts.append("❓ **Do you want me to proceed?** (Please confirm)\n\n")
        else:
            response_parts.append("✅ **No Approval Required** - Read-only operation.\n\n")

        if analysis["matching_concepts"]:
            concept = analysis["matching_concepts"][0]
            response_parts.append(f"📋 **{concept.name} Operations:**\n")
            response_parts.append(f"- Available: {', '.join([op.value for op in concept.operations])}\n")
            response_parts.append(f"- API: {concept.api_endpoints[0] if concept.api_endpoints else 'N/A'}\n\n")

        response_parts.append(f"📊 **Confidence:** {analysis['confidence']:.1%}\n")
        response_parts.append(f"🕒 **Time:** {timestamp}\n")
        response_parts.append(f"🚀 **Status:** {'Full agent ready' if AGENT_AVAILABLE else 'Enhanced knowledge mode'}\n\n")
        response_parts.append("✅ **Analysis Complete** - How would you like to proceed?")

        agent_response = "".join(response_parts)

        # Add agent response to session
        agent_msg = {
            "role": "assistant",
            "content": agent_response,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        # Update display
        final_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{agent_response}\n\n"
        return final_display, "Enhanced response completed"

    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        agent_msg = {
            "role": "assistant",
            "content": error_msg,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        error_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{error_msg}\n\n"
        return error_display, f"Error occurred: {str(e)}"


def get_system_status():
    """Get current system status."""
    status_info = {
        "Agent Available": "✅ Yes" if AGENT_AVAILABLE else "❌ No (Mock Mode)",
        "Chat Sessions": len(chat_sessions),
        "Current Session": current_session_id or "None",
        "Environment": "Development" if os.getenv("DEBUG") else "Production",
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    status_text = "\n".join([f"**{k}:** {v}" for k, v in status_info.items()])
    return status_text


# Create Gradio interface
def create_interface():
    """Create the Gradio interface."""

    with gr.Blocks(
        title="AI Browser Agent",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .chat-display {
            height: 500px;
            overflow-y: auto;
        }
        """
    ) as interface:

        gr.Markdown("# 🤖 AI Browser Agent")
        gr.Markdown("*Intelligent browser automation for CartonCloud operations*")

        with gr.Row():
            with gr.Column(scale=3):
                # Chat interface
                with gr.Group():
                    gr.Markdown("## 💬 Chat Interface")

                    chat_display = gr.Textbox(
                        label="Conversation",
                        lines=20,
                        max_lines=30,
                        interactive=False,
                        elem_classes=["chat-display"]
                    )

                    with gr.Row():
                        message_input = gr.Textbox(
                            label="Your message",
                            placeholder="Ask me to help with CartonCloud operations...",
                            lines=2,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)

                    status_display = gr.Textbox(
                        label="Status",
                        value="Ready",
                        interactive=False
                    )

            with gr.Column(scale=1):
                # Chat management
                with gr.Group():
                    gr.Markdown("## 📋 Chat Management")

                    new_chat_btn = gr.Button("+ New Chat", variant="secondary")

                    chat_selector = gr.Dropdown(
                        label="Select Chat",
                        choices=[],
                        interactive=True
                    )

                    chat_status = gr.Textbox(
                        label="Chat Status",
                        value="No active chat",
                        interactive=False
                    )

                # System info
                with gr.Group():
                    gr.Markdown("## ⚙️ System Status")

                    system_status = gr.Textbox(
                        label="System Info",
                        value=get_system_status(),
                        interactive=False,
                        lines=6
                    )

                    refresh_btn = gr.Button("Refresh Status", variant="secondary")

        # Example prompts
        with gr.Row():
            gr.Markdown("## 💡 Example Prompts")

        with gr.Row():
            example_btns = [
                gr.Button("Look up ALS rate card", variant="secondary"),
                gr.Button("Check storage charges for customer XYZ", variant="secondary"),
                gr.Button("Simulate invoice for last month", variant="secondary"),
                gr.Button("Update rate card pricing", variant="secondary")
            ]

        # Event handlers
        def set_example(example_text):
            return example_text

        # Wire up the interface
        def handle_send(message, chat_display):
            result_display, status = send_message_sync(message, chat_display)
            return result_display, status, ""  # Clear message input

        send_btn.click(
            fn=handle_send,
            inputs=[message_input, chat_display],
            outputs=[chat_display, status_display, message_input],
            show_progress=True
        )

        message_input.submit(
            fn=handle_send,
            inputs=[message_input, chat_display],
            outputs=[chat_display, status_display, message_input],
            show_progress=True
        )

        new_chat_btn.click(
            fn=create_new_chat,
            outputs=[chat_display, message_input, chat_selector, chat_status]
        )

        chat_selector.change(
            fn=select_chat,
            inputs=[chat_selector],
            outputs=[chat_display, chat_status]
        )

        refresh_btn.click(
            fn=get_system_status,
            outputs=[system_status]
        )

        # Example button handlers
        for i, btn in enumerate(example_btns):
            examples = [
                "Look up the rate card for ALS customer",
                "Check what storage charges apply for customer XYZ",
                "Simulate creating an invoice for last month's charges",
                "I need to update rate card pricing - what's the process?"
            ]
            btn.click(
                fn=lambda x=examples[i]: x,
                outputs=[message_input]
            )

    return interface


if __name__ == "__main__":
    # Create and launch the interface
    interface = create_interface()

    print("🚀 Starting AI Browser Agent Gradio UI...")
    print(f"📊 Agent Status: {'Available' if AGENT_AVAILABLE else 'Mock Mode'}")

    interface.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )
