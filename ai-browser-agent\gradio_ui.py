"""
Gradio UI for AI Browser Agent

This provides a working Gradio interface that can function with or without
the full agent dependencies, allowing for testing and development.
"""

import asyncio
import logging
import os
import time
from datetime import datetime
from typing import AsyncIterator, Generator

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try to import Gradio with error handling
try:
    import gradio as gr
    GRADIO_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Gradio import failed: {e}")
    GRADIO_AVAILABLE = False
    # Create a mock gr object for testing
    class MockGradio:
        def __getattr__(self, name):
            return lambda *args, **kwargs: None
    gr = MockGradio()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import the full agent, fall back to mock if not available
try:
    from src.agent.run_agent import run_agent, cleanup_browser
    AGENT_AVAILABLE = True
    logger.info("✅ Full agent functionality available")
except ImportError as e:
    logger.warning(f"⚠️ Agent import failed: {e}")
    logger.info("🔄 Using mock agent for testing")
    AGENT_AVAILABLE = False

    async def run_agent(history):
        """Mock agent for testing when full agent is not available."""
        user_message = history[-1]["content"] if history else "No message"

        responses = [
            f"🤖 **Mock Agent Response**\n\n",
            f"📝 **Your message:** {user_message}\n\n",
            f"⚠️ **Note:** This is a mock response. The full agent is not available due to dependency issues.\n\n",
            f"🔧 **What the real agent would do:**\n",
            f"- Analyze your request for CartonCloud operations\n",
            f"- Check if rate card updates are needed\n",
            f"- Ask for approval before making changes\n",
            f"- Execute browser automation tasks\n",
            f"- Provide detailed results\n\n",
            f"🕒 **Time:** {datetime.now().strftime('%H:%M:%S')}\n\n",
            f"✅ **Status:** Mock response completed"
        ]

        for response in responses:
            yield response
            await asyncio.sleep(0.3)

    async def cleanup_browser():
        """Mock cleanup function."""
        pass


# Global state for chat history
chat_sessions = {}
current_session_id = None


def create_new_chat():
    """Create a new chat session."""
    global current_session_id
    session_id = f"chat_{int(time.time())}"
    chat_sessions[session_id] = {
        "id": session_id,
        "title": "New Chat",
        "messages": [],
        "created_at": datetime.now()
    }
    current_session_id = session_id

    # Update chat list
    chat_list = [f"{session['title']} ({session['id']})" for session in chat_sessions.values()]

    return (
        "",  # Clear chat display
        "",  # Clear message input
        gr.Dropdown(choices=chat_list, value=chat_list[-1] if chat_list else None),  # Update chat selector
        f"Created new chat: {session_id}"
    )


def select_chat(chat_selection):
    """Select an existing chat session."""
    global current_session_id

    if not chat_selection:
        return "", "No chat selected"

    # Extract session ID from selection
    session_id = chat_selection.split("(")[-1].rstrip(")")

    if session_id in chat_sessions:
        current_session_id = session_id
        session = chat_sessions[session_id]

        # Rebuild chat display
        chat_display = ""
        for msg in session["messages"]:
            role = msg["role"]
            content = msg["content"]
            timestamp = msg.get("timestamp", "")

            if role == "user":
                chat_display += f"**👤 You** ({timestamp}):\n{content}\n\n"
            else:
                chat_display += f"**🤖 Agent** ({timestamp}):\n{content}\n\n"

        return chat_display, f"Selected chat: {session_id}"

    return "", f"Chat not found: {session_id}"


async def send_message_async(message: str, chat_display: str) -> AsyncIterator[tuple[str, str]]:
    """Send message to agent and stream response."""
    global current_session_id

    if not message.strip():
        yield chat_display, "Please enter a message"
        return

    # Create new chat if none exists
    if not current_session_id or current_session_id not in chat_sessions:
        create_new_chat()

    session = chat_sessions[current_session_id]
    timestamp = datetime.now().strftime("%H:%M:%S")

    # Add user message
    user_msg = {
        "role": "user",
        "content": message,
        "timestamp": timestamp
    }
    session["messages"].append(user_msg)

    # Update chat display with user message
    chat_display += f"**👤 You** ({timestamp}):\n{message}\n\n"
    yield chat_display, "Agent is thinking..."

    # Get agent response
    history = session["messages"]
    agent_response = ""

    try:
        async for token in run_agent(history):
            agent_response += token
            # Update display with streaming response
            current_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{agent_response}\n\n"
            yield current_display, "Agent is responding..."

        # Add complete agent response to session
        agent_msg = {
            "role": "assistant",
            "content": agent_response,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        # Final update
        final_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{agent_response}\n\n"
        yield final_display, "Response completed"

    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        agent_msg = {
            "role": "assistant",
            "content": error_msg,
            "timestamp": timestamp
        }
        session["messages"].append(agent_msg)

        error_display = chat_display + f"**🤖 Agent** ({timestamp}):\n{error_msg}\n\n"
        yield error_display, f"Error occurred: {str(e)}"


def send_message_sync(message: str, chat_display: str) -> Generator[tuple[str, str], None, None]:
    """Synchronous wrapper for async message sending."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        async_gen = send_message_async(message, chat_display)
        while True:
            try:
                result = loop.run_until_complete(async_gen.__anext__())
                yield result
            except StopAsyncIteration:
                break
    finally:
        loop.close()


def get_system_status():
    """Get current system status."""
    status_info = {
        "Agent Available": "✅ Yes" if AGENT_AVAILABLE else "❌ No (Mock Mode)",
        "Chat Sessions": len(chat_sessions),
        "Current Session": current_session_id or "None",
        "Environment": "Development" if os.getenv("DEBUG") else "Production",
        "Timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    status_text = "\n".join([f"**{k}:** {v}" for k, v in status_info.items()])
    return status_text


# Create Gradio interface
def create_interface():
    """Create the Gradio interface."""

    with gr.Blocks(
        title="AI Browser Agent",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .chat-display {
            height: 500px;
            overflow-y: auto;
        }
        """
    ) as interface:

        gr.Markdown("# 🤖 AI Browser Agent")
        gr.Markdown("*Intelligent browser automation for CartonCloud operations*")

        with gr.Row():
            with gr.Column(scale=3):
                # Chat interface
                with gr.Group():
                    gr.Markdown("## 💬 Chat Interface")

                    chat_display = gr.Textbox(
                        label="Conversation",
                        lines=20,
                        max_lines=30,
                        interactive=False,
                        elem_classes=["chat-display"]
                    )

                    with gr.Row():
                        message_input = gr.Textbox(
                            label="Your message",
                            placeholder="Ask me to help with CartonCloud operations...",
                            lines=2,
                            scale=4
                        )
                        send_btn = gr.Button("Send", variant="primary", scale=1)

                    status_display = gr.Textbox(
                        label="Status",
                        value="Ready",
                        interactive=False
                    )

            with gr.Column(scale=1):
                # Chat management
                with gr.Group():
                    gr.Markdown("## 📋 Chat Management")

                    new_chat_btn = gr.Button("+ New Chat", variant="secondary")

                    chat_selector = gr.Dropdown(
                        label="Select Chat",
                        choices=[],
                        interactive=True
                    )

                    chat_status = gr.Textbox(
                        label="Chat Status",
                        value="No active chat",
                        interactive=False
                    )

                # System info
                with gr.Group():
                    gr.Markdown("## ⚙️ System Status")

                    system_status = gr.Textbox(
                        label="System Info",
                        value=get_system_status(),
                        interactive=False,
                        lines=6
                    )

                    refresh_btn = gr.Button("Refresh Status", variant="secondary")

        # Example prompts
        with gr.Row():
            gr.Markdown("## 💡 Example Prompts")

        with gr.Row():
            example_btns = [
                gr.Button("Look up ALS rate card", variant="secondary"),
                gr.Button("Check storage charges for customer XYZ", variant="secondary"),
                gr.Button("Simulate invoice for last month", variant="secondary"),
                gr.Button("Update rate card pricing", variant="secondary")
            ]

        # Event handlers
        def set_example(example_text):
            return example_text

        # Wire up the interface
        send_btn.click(
            fn=send_message_sync,
            inputs=[message_input, chat_display],
            outputs=[chat_display, status_display],
            show_progress=True
        )

        message_input.submit(
            fn=send_message_sync,
            inputs=[message_input, chat_display],
            outputs=[chat_display, status_display],
            show_progress=True
        )

        new_chat_btn.click(
            fn=create_new_chat,
            outputs=[chat_display, message_input, chat_selector, chat_status]
        )

        chat_selector.change(
            fn=select_chat,
            inputs=[chat_selector],
            outputs=[chat_display, chat_status]
        )

        refresh_btn.click(
            fn=get_system_status,
            outputs=[system_status]
        )

        # Example button handlers
        for i, btn in enumerate(example_btns):
            examples = [
                "Look up the rate card for ALS customer",
                "Check what storage charges apply for customer XYZ",
                "Simulate creating an invoice for last month's charges",
                "I need to update rate card pricing - what's the process?"
            ]
            btn.click(
                fn=lambda x=examples[i]: x,
                outputs=[message_input]
            )

    return interface


if __name__ == "__main__":
    # Create and launch the interface
    interface = create_interface()

    print("🚀 Starting AI Browser Agent Gradio UI...")
    print(f"📊 Agent Status: {'Available' if AGENT_AVAILABLE else 'Mock Mode'}")

    interface.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )
