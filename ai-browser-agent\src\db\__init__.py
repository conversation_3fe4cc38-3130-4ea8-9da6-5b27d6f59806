"""Database initialization and session management."""

from pathlib import Path

from sqlmodel import Session
from sqlmodel import SQLModel
from sqlmodel import create_engine

from .models import Chat as Chat
from .models import Message as Message

# Ensure data directory exists
data_dir = Path(__file__).parent.parent.parent / "data"
data_dir.mkdir(exist_ok=True)

# Create SQLite engine
engine = create_engine(f"sqlite:///{data_dir}/chat.db", echo=False)

# Create all tables
SQLModel.metadata.create_all(engine)


def get_session():
    """Get database session."""
    with Session(engine) as session:
        yield session
