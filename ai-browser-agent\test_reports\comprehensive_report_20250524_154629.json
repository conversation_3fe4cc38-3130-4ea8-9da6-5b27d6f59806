{"summary": {"total": 4, "passed": 4, "partial": 0, "failed": 0, "success_rate": 100.0, "assessment": "excellent", "duration": 53.38571882247925}, "results": [{"test_name": "<PERSON><PERSON>", "status": "passed", "start_time": "2025-05-24T15:45:36.280569", "command": "c:\\Users\\<USER>\\Desktop\\cartoncloud agent\\.venv\\Scripts\\python.exe -m ruff check src tests --statistics", "errors": [], "output": "", "return_code": 0, "end_time": "2025-05-24T15:45:36.463044"}, {"test_name": "component_imports", "status": "passed", "start_time": "2025-05-24T15:45:36.474509", "errors": [], "components_tested": ["✅ Gradio: Gradio 5.31.0", "✅ FastAPI: FastAPI OK", "✅ Playwright: Playwright OK", "✅ SQLModel: SQLModel OK", "✅ Gradio UI: Gradio: True, Agent: <PERSON><PERSON><PERSON>"], "end_time": "2025-05-24T15:45:47.807918"}, {"test_name": "mock_agent_functionality", "status": "passed", "start_time": "2025-05-24T15:45:47.809438", "errors": [], "scenarios_tested": ["✅ Using mock agent (expected)", "✅ Scenario 1: Response generated (456 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 2: Response generated (473 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 3: Response generated (481 chars)", "   ✅ Contains CartonCloud concepts", "   ✅ Mentions approval workflow", "✅ Scenario 4: Response generated (466 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 5: Response generated (475 chars)", "   ✅ Contains CartonCloud concepts"], "end_time": "2025-05-24T15:46:09.346005"}, {"test_name": "edge_cases", "status": "passed", "start_time": "2025-05-24T15:46:09.347886", "errors": [], "cases_tested": ["✅ Empty input: Handled gracefully", "✅ Very long input: Handled gracefully", "✅ Emoji input: Handled gracefully", "✅ SQL injection attempt: Handled gracefully", "✅ Dangerous request: Handled gracefully", "✅ Unicode input: Handled gracefully"], "end_time": "2025-05-24T15:46:29.650354"}], "timestamp": "2025-05-24T15:46:29.666288"}