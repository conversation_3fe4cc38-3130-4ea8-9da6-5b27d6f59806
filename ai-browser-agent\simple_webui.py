"""
Simplified WebUI for testing without complex browser_use dependencies.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import AsyncIterator

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sse_starlette.sse import EventSourceResponse

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="AI Browser Agent - Simple Test UI")

# Setup templates
templates = Jinja2Templates(directory="templates")

# In-memory storage for testing
chats = {}


async def simple_agent_response(message: str) -> AsyncIterator[str]:
    """Simple mock agent response for testing."""
    responses = [
        f"🤖 Received your message: {message}",
        "\n\n📝 Processing your request...",
        "\n\n✅ This is a test response from the simplified agent.",
        f"\n\n🕒 Current time: {datetime.now().strftime('%H:%M:%S')}",
        "\n\n💡 The full agent functionality will be available once all dependencies are resolved."
    ]
    
    for response in responses:
        yield response
        await asyncio.sleep(0.5)  # Simulate processing time


@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Serve the main chat interface."""
    return templates.TemplateResponse("index.html", {"request": request})


@app.post("/api/chats")
async def create_chat():
    """Create a new chat session."""
    chat_id = str(uuid.uuid4())
    chats[chat_id] = {
        "id": chat_id,
        "title": "New Chat",
        "created_at": datetime.now().isoformat(),
        "messages": []
    }
    return {"chat_id": chat_id}


@app.get("/api/chats")
async def list_chats():
    """List all chat sessions."""
    return {"chats": list(chats.values())}


@app.get("/api/chats/{chat_id}")
async def get_chat(chat_id: str):
    """Get a specific chat session."""
    if chat_id not in chats:
        return {"error": "Chat not found"}, 404
    return chats[chat_id]


@app.post("/api/chats/{chat_id}/messages")
async def send_message(chat_id: str, request: Request):
    """Send a message and get streaming response."""
    if chat_id not in chats:
        return {"error": "Chat not found"}, 404
    
    data = await request.json()
    message = data.get("message", "")
    
    # Add user message to chat
    user_msg = {
        "role": "user",
        "content": message,
        "timestamp": datetime.now().isoformat()
    }
    chats[chat_id]["messages"].append(user_msg)
    
    async def generate_response():
        """Generate streaming response."""
        response_content = ""
        
        async for token in simple_agent_response(message):
            response_content += token
            yield f"data: {json.dumps({'token': token})}\n\n"
        
        # Add assistant message to chat
        assistant_msg = {
            "role": "assistant", 
            "content": response_content,
            "timestamp": datetime.now().isoformat()
        }
        chats[chat_id]["messages"].append(assistant_msg)
        
        yield f"data: {json.dumps({'done': True})}\n\n"
    
    return EventSourceResponse(generate_response())


@app.delete("/api/chats/{chat_id}")
async def delete_chat(chat_id: str):
    """Delete a chat session."""
    if chat_id in chats:
        del chats[chat_id]
        return {"success": True}
    return {"error": "Chat not found"}, 404


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "simple-test",
        "chats_count": len(chats)
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=7861, log_level="info")
