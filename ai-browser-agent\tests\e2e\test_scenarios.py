"""
End-to-End Test Scenarios for AI Browser Agent

This module defines comprehensive test scenarios covering:
1. Basic functionality
2. User workflows
3. Edge cases
4. Error handling
5. Performance scenarios
"""

from typing import Any


# Test Scenarios Definition
class TestScenarios:
    """Comprehensive test scenarios for the AI Browser Agent."""

    @staticmethod
    def get_basic_scenarios() -> list[dict[str, Any]]:
        """Basic functionality test scenarios."""
        return [
            {
                "name": "simple_web_search",
                "description": "Search for information on Google",
                "user_input": "Search Google for 'OpenAI GPT-4' and tell me the first result",
                "expected_actions": ["navigate", "search", "extract_text"],
                "success_criteria": ["contains OpenAI", "mentions GPT-4"],
                "max_steps": 5,
                "timeout": 30
            },
            {
                "name": "website_navigation",
                "description": "Navigate to a specific website and extract information",
                "user_input": "Go to wikipedia.org and find information about artificial intelligence",
                "expected_actions": ["navigate", "click", "extract_text"],
                "success_criteria": ["contains artificial intelligence", "from wikipedia"],
                "max_steps": 8,
                "timeout": 45
            },
            {
                "name": "form_interaction",
                "description": "Fill out a simple form",
                "user_input": "Go to httpbin.org/forms/post and fill out the form with test data",
                "expected_actions": ["navigate", "type", "click"],
                "success_criteria": ["form submitted", "success response"],
                "max_steps": 10,
                "timeout": 60
            }
        ]

    @staticmethod
    def get_user_workflow_scenarios() -> list[dict[str, Any]]:
        """Real user workflow scenarios."""
        return [
            {
                "name": "research_workflow",
                "description": "Multi-step research task",
                "user_input": "Research the latest developments in AI safety, check at least 3 sources",
                "expected_actions": ["navigate", "search", "extract_text", "navigate"],
                "success_criteria": ["multiple sources", "AI safety mentioned", "recent information"],
                "max_steps": 20,
                "timeout": 120
            },
            {
                "name": "comparison_shopping",
                "description": "Compare products across websites",
                "user_input": "Compare laptop prices between Amazon and Best Buy for MacBook Pro",
                "expected_actions": ["navigate", "search", "extract_text", "navigate", "search"],
                "success_criteria": ["price information", "multiple sites", "MacBook Pro"],
                "max_steps": 25,
                "timeout": 150
            },
            {
                "name": "news_aggregation",
                "description": "Gather news from multiple sources",
                "user_input": "Get the latest tech news from TechCrunch and The Verge",
                "expected_actions": ["navigate", "extract_text", "navigate", "extract_text"],
                "success_criteria": ["tech news", "multiple sources", "recent articles"],
                "max_steps": 15,
                "timeout": 90
            }
        ]

    @staticmethod
    def get_edge_case_scenarios() -> list[dict[str, Any]]:
        """Edge cases and error scenarios."""
        return [
            {
                "name": "invalid_url",
                "description": "Handle invalid URL gracefully",
                "user_input": "Go to invalid-website-that-does-not-exist.com",
                "expected_actions": ["navigate"],
                "success_criteria": ["error handled", "graceful failure"],
                "max_steps": 3,
                "timeout": 15,
                "expect_failure": True
            },
            {
                "name": "slow_loading_page",
                "description": "Handle slow loading pages",
                "user_input": "Go to httpbin.org/delay/10 and get the response",
                "expected_actions": ["navigate", "wait"],
                "success_criteria": ["timeout handled", "appropriate response"],
                "max_steps": 5,
                "timeout": 20
            },
            {
                "name": "javascript_heavy_site",
                "description": "Handle JavaScript-heavy single page applications",
                "user_input": "Go to react.dev and find information about React hooks",
                "expected_actions": ["navigate", "wait", "extract_text"],
                "success_criteria": ["React hooks", "SPA handled"],
                "max_steps": 10,
                "timeout": 60
            },
            {
                "name": "popup_handling",
                "description": "Handle popups and modals",
                "user_input": "Go to a site with popups and dismiss them to access content",
                "expected_actions": ["navigate", "click", "dismiss"],
                "success_criteria": ["popup dismissed", "content accessed"],
                "max_steps": 8,
                "timeout": 45
            }
        ]

    @staticmethod
    def get_cartoncloud_scenarios() -> list[dict[str, Any]]:
        """CartonCloud-specific test scenarios."""
        return [
            {
                "name": "rate_card_lookup",
                "description": "Look up rate card information",
                "user_input": "Find rate card information for ALS customer",
                "expected_actions": ["search_rate_cards", "extract_data"],
                "success_criteria": ["rate card found", "ALS mentioned"],
                "max_steps": 5,
                "timeout": 30
            },
            {
                "name": "invoice_simulation",
                "description": "Simulate invoice creation",
                "user_input": "Simulate creating an invoice for last month's charges",
                "expected_actions": ["navigate_cartoncloud", "simulate_invoice"],
                "success_criteria": ["invoice simulated", "charges calculated"],
                "max_steps": 15,
                "timeout": 90
            }
        ]

    @staticmethod
    def get_performance_scenarios() -> list[dict[str, Any]]:
        """Performance and stress test scenarios."""
        return [
            {
                "name": "concurrent_requests",
                "description": "Handle multiple concurrent chat sessions",
                "user_input": "Simple search task",
                "concurrent_users": 3,
                "expected_actions": ["navigate", "search"],
                "success_criteria": ["all requests handled", "no conflicts"],
                "max_steps": 5,
                "timeout": 60
            },
            {
                "name": "long_running_task",
                "description": "Handle long-running complex tasks",
                "user_input": "Research and summarize the top 10 AI companies, their products, and recent news",
                "expected_actions": ["navigate", "search", "extract_text"],
                "success_criteria": ["comprehensive research", "10 companies", "summaries"],
                "max_steps": 50,
                "timeout": 300
            }
        ]

    @staticmethod
    def get_all_scenarios() -> dict[str, list[dict[str, Any]]]:
        """Get all test scenarios organized by category."""
        return {
            "basic": TestScenarios.get_basic_scenarios(),
            "workflows": TestScenarios.get_user_workflow_scenarios(),
            "edge_cases": TestScenarios.get_edge_case_scenarios(),
            "cartoncloud": TestScenarios.get_cartoncloud_scenarios(),
            "performance": TestScenarios.get_performance_scenarios()
        }


# User Questions for Testing
class UserQuestions:
    """Real user questions to test the agent with."""

    SIMPLE_QUESTIONS = [
        "What's the weather like today?",
        "Find me the latest news about AI",
        "Search for Python tutorials",
        "What is the capital of France?",
        "Show me the top 5 programming languages"
    ]

    COMPLEX_QUESTIONS = [
        "Research the latest developments in quantum computing and summarize the key breakthroughs",
        "Compare the features and pricing of the top 3 cloud providers",
        "Find and analyze customer reviews for the iPhone 15 Pro from multiple sources",
        "Research sustainable energy solutions and their current adoption rates",
        "Investigate the current state of autonomous vehicle technology"
    ]

    CARTONCLOUD_QUESTIONS = [
        "Look up the rate card for ALS customer",
        "Find all rate cards with storage charges",
        "Simulate an invoice for customer XYZ for last month",
        "Update the rate card for customer ABC with new pricing",
        "Generate a bulk charges report for the last week"
    ]

    EDGE_CASE_QUESTIONS = [
        "",  # Empty input
        "a" * 1000,  # Very long input
        "Go to https://invalid-url-12345.com",  # Invalid URL
        "Click on the button that doesn't exist",  # Impossible task
        "🤖🚀💻🔥⚡",  # Emoji only
        "SELECT * FROM users; DROP TABLE users;",  # SQL injection attempt
    ]
