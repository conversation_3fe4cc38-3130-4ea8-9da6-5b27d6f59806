"""Auto-generated CartonCloud API client."""

from typing import Optional, Dict, Any, List
import httpx
from datetime import datetime


class CartonCloudClientError(Exception):
    """CartonCloud client error."""
    pass


class Client:
    """Auto-generated CartonCloud API client."""
    
    def __init__(self, base_url: str, headers: Optional[Dict[str, str]] = None):
        """Initialize the client.
        
        Args:
            base_url: API base URL
            headers: Default headers to include in all requests
        """
        self.base_url = base_url.rstrip("/")
        self.headers = headers or {}
        self._client = httpx.Client(base_url=self.base_url, headers=self.headers)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self._client.close()
    
    def close(self):
        """Close the HTTP client."""
        self._client.close()
    
    # Rate Cards endpoints
    def list_rate_cards(
        self, 
        page: Optional[int] = None,
        size: Optional[int] = None,
        search: Optional[str] = None,
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """List rate cards with pagination."""
        params = {}
        if page is not None:
            params["page"] = page
        if size is not None:
            params["size"] = size
        if search is not None:
            params["search"] = search
        if status is not None:
            params["status"] = status
        
        response = self._client.get("/v1/rate-cards", params=params)
        response.raise_for_status()
        return response.json()
    
    def get_rate_card(self, rate_card_id: str) -> Dict[str, Any]:
        """Get a specific rate card by ID."""
        response = self._client.get(f"/v1/rate-cards/{rate_card_id}")
        response.raise_for_status()
        return response.json()
    
    def create_rate_card(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new rate card."""
        response = self._client.post("/v1/rate-cards", json=data)
        response.raise_for_status()
        return response.json()
    
    def update_rate_card(self, rate_card_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing rate card."""
        response = self._client.put(f"/v1/rate-cards/{rate_card_id}", json=data)
        response.raise_for_status()
        return response.json()
    
    def delete_rate_card(self, rate_card_id: str) -> None:
        """Delete a rate card."""
        response = self._client.delete(f"/v1/rate-cards/{rate_card_id}")
        response.raise_for_status()
    
    # Purchase Orders endpoints
    def list_purchase_orders(
        self,
        page: Optional[int] = None,
        size: Optional[int] = None,
        status: Optional[str] = None,
        vendor_id: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> Dict[str, Any]:
        """List purchase orders with pagination."""
        params = {}
        if page is not None:
            params["page"] = page
        if size is not None:
            params["size"] = size
        if status is not None:
            params["status"] = status
        if vendor_id is not None:
            params["vendor_id"] = vendor_id
        if date_from is not None:
            params["date_from"] = date_from
        if date_to is not None:
            params["date_to"] = date_to
        
        response = self._client.get("/v1/purchase-orders", params=params)
        response.raise_for_status()
        return response.json()
    
    def get_purchase_order(self, purchase_order_id: str) -> Dict[str, Any]:
        """Get a specific purchase order by ID."""
        response = self._client.get(f"/v1/purchase-orders/{purchase_order_id}")
        response.raise_for_status()
        return response.json()
    
    def create_purchase_order(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new purchase order."""
        response = self._client.post("/v1/purchase-orders", json=data)
        response.raise_for_status()
        return response.json()
    
    def update_purchase_order(self, purchase_order_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing purchase order."""
        response = self._client.put(f"/v1/purchase-orders/{purchase_order_id}", json=data)
        response.raise_for_status()
        return response.json()
    
    # Sales Orders endpoints
    def list_sales_orders(
        self,
        page: Optional[int] = None,
        size: Optional[int] = None,
        status: Optional[str] = None,
        customer_id: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> Dict[str, Any]:
        """List sales orders with pagination."""
        params = {}
        if page is not None:
            params["page"] = page
        if size is not None:
            params["size"] = size
        if status is not None:
            params["status"] = status
        if customer_id is not None:
            params["customer_id"] = customer_id
        if date_from is not None:
            params["date_from"] = date_from
        if date_to is not None:
            params["date_to"] = date_to
        
        response = self._client.get("/v1/sales-orders", params=params)
        response.raise_for_status()
        return response.json()
    
    def get_sales_order(self, sales_order_id: str) -> Dict[str, Any]:
        """Get a specific sales order by ID."""
        response = self._client.get(f"/v1/sales-orders/{sales_order_id}")
        response.raise_for_status()
        return response.json()
    
    def create_sales_order(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new sales order."""
        response = self._client.post("/v1/sales-orders", json=data)
        response.raise_for_status()
        return response.json()
    
    def update_sales_order(self, sales_order_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing sales order."""
        response = self._client.put(f"/v1/sales-orders/{sales_order_id}", json=data)
        response.raise_for_status()
        return response.json()
