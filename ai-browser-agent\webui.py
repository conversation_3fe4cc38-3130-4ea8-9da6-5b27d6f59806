"""FastAPI Chat UI for AI Browser Agent."""

import uuid
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any

from fastapi import Fast<PERSON><PERSON>, Request, Depends, HTTPException, Form
from fastapi.responses import HTMLResponse, StreamingResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from sqlmodel import Session, select
from sse_starlette import EventSourceResponse

from dotenv import load_dotenv
load_dotenv()

from src.db import get_session
from src.db.models import Chat, Message
from src.agent.run_agent import run_agent

logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="AI Browser Agent Chat")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Setup static files (if needed)
from pathlib import Path
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")


# FastAPI Routes

@app.get("/", response_class=HTMLResponse)
async def index(request: Request, session: Session = Depends(get_session)):
    """Main chat interface."""
    # Get all chats ordered by creation date
    chats = session.exec(select(Chat).order_by(Chat.created_at.desc())).all()
    return templates.TemplateResponse("index.html", {
        "request": request,
        "chats": chats
    })


@app.post("/api/chat")
async def create_chat(title: str = Form(...), session: Session = Depends(get_session)):
    """Create a new chat session."""
    chat_id = str(uuid.uuid4())
    
    # Truncate title if it's too long
    if len(title) > 50:
        title = title[:47] + "..."
    
    chat = Chat(id=chat_id, title=title)
    session.add(chat)
    session.commit()
    session.refresh(chat)
    
    # Return the new chat item HTML
    return f"""
    <div class="chat-item p-3 mb-2 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
         hx-get="/api/chat/{chat.id}"
         hx-target="#chat-body"
         hx-swap="innerHTML"
         onclick="setActiveChat('{chat.id}', '{chat.title}')">
        <div class="font-medium text-gray-800 truncate">{chat.title}</div>
        <div class="text-sm text-gray-500">{chat.created_at.strftime('%m/%d %H:%M')}</div>
    </div>
    """


@app.get("/api/chat/{chat_id}")
async def get_chat_messages(chat_id: str, session: Session = Depends(get_session)):
    """Get all messages for a chat."""
    messages = session.exec(
        select(Message)
        .where(Message.chat_id == chat_id)
        .order_by(Message.created_at)
    ).all()
    
    # Return HTML for the messages
    html_parts = []
    for message in messages:
        if message.role == "user":
            html_parts.append(f"""
            <div class="chat-message mb-4">
                <div class="flex justify-end">
                    <div class="bg-blue-600 text-white px-4 py-2 rounded-lg max-w-xs lg:max-w-md">
                        {message.content}
                    </div>
                </div>
            </div>
            """)
        else:  # assistant
            html_parts.append(f"""
            <div class="chat-message mb-4">
                <div class="flex justify-start">
                    <div class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg max-w-xs lg:max-w-md">
                        {message.content}
                    </div>
                </div>
            </div>
            """)
    
    return "".join(html_parts)


@app.post("/api/chat/{chat_id}")
async def send_message(
    chat_id: str, 
    content: str = Form(...), 
    session: Session = Depends(get_session)
):
    """Send a message and get agent response."""
    # Save user message
    user_msg = Message(
        id=str(uuid.uuid4()),
        chat_id=chat_id,
        role="user",
        content=content
    )
    session.add(user_msg)
    session.commit()
    
    # Update chat title if it's the first message
    chat = session.get(Chat, chat_id)
    if chat and chat.title == "New Chat":
        chat.title = content[:50] + ("..." if len(content) > 50 else "")
        session.add(chat)
        session.commit()
    
    # Return user message HTML immediately
    user_html = f"""
    <div class="chat-message mb-4">
        <div class="flex justify-end">
            <div class="bg-blue-600 text-white px-4 py-2 rounded-lg max-w-xs lg:max-w-md">
                {content}
            </div>
        </div>
    </div>
    <div id="assistant-response" class="chat-message mb-4 streaming">
        <div class="flex justify-start">
            <div class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg max-w-xs lg:max-w-md">
                <div class="typing-indicator">🤖 Thinking...</div>
            </div>
        </div>
    </div>
    """
    
    return user_html


@app.get("/api/stream/{chat_id}")
async def stream_response(chat_id: str, session: Session = Depends(get_session)):
    """Stream agent response via Server-Sent Events."""
    
    async def generate():
        try:
            # Get chat history
            messages = session.exec(
                select(Message)
                .where(Message.chat_id == chat_id)
                .order_by(Message.created_at)
            ).all()
            
            # Convert to format expected by agent
            history = [{"role": msg.role, "content": msg.content} for msg in messages]
            
            # Stream agent response
            response_content = ""
            async for token in run_agent(history):
                response_content += token
                yield f"data: {token}\n\n"
            
            # Save assistant response
            assistant_msg = Message(
                id=str(uuid.uuid4()),
                chat_id=chat_id,
                role="assistant",
                content=response_content
            )
            session.add(assistant_msg)
            session.commit()
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"Error in stream_response: {e}")
            yield f"data: Error: {str(e)}\n\n"
            yield "data: [DONE]\n\n"
    
    return EventSourceResponse(generate())


@app.delete("/api/chat/{chat_id}/undo")
async def undo_last_message(chat_id: str, session: Session = Depends(get_session)):
    """Undo the last user message and assistant response."""
    # Get last two messages
    messages = session.exec(
        select(Message)
        .where(Message.chat_id == chat_id)
        .order_by(Message.created_at.desc())
        .limit(2)
    ).all()
    
    if len(messages) >= 2 and messages[0].role == "assistant" and messages[1].role == "user":
        # Delete both messages
        for msg in messages:
            session.delete(msg)
        session.commit()
        
        # Return updated chat messages
        return await get_chat_messages(chat_id, session)
    
    # If can't undo, return current messages
    return await get_chat_messages(chat_id, session)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=7860)
