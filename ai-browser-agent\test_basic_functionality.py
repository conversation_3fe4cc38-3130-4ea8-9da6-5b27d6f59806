"""
Basic functionality test to verify the application works.

This is a simple test to check if the core components are working
before running the full E2E test suite.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.agent.run_agent import run_agent
from src.utils import utils

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_basic_agent():
    """Test basic agent functionality."""
    print("🧪 Testing Basic Agent Functionality...")
    
    try:
        # Test simple task
        history = [{"role": "user", "content": "What is 2 + 2?"}]
        
        print("📤 Sending test message: 'What is 2 + 2?'")
        
        response = ""
        async for token in run_agent(history):
            response += token
            print(token, end="", flush=True)
        
        print(f"\n\n📥 Complete response received ({len(response)} characters)")
        
        if response and len(response) > 10:
            print("✅ Basic agent test PASSED")
            return True
        else:
            print("❌ Basic agent test FAILED - No meaningful response")
            return False
            
    except Exception as e:
        print(f"❌ Basic agent test FAILED - Error: {e}")
        logger.error(f"Agent test error: {e}")
        return False


async def test_llm_connection():
    """Test LLM connection."""
    print("\n🔗 Testing LLM Connection...")
    
    try:
        llm = utils.get_llm_model(
            provider="anthropic",
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1,
        )
        
        print("✅ LLM connection test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ LLM connection test FAILED - Error: {e}")
        logger.error(f"LLM connection error: {e}")
        return False


def test_imports():
    """Test that all required imports work."""
    print("📦 Testing Imports...")
    
    try:
        # Test core imports
        from src.agent.custom_agent import CustomAgent
        from src.browser.custom_browser import CustomBrowser
        from src.controller.custom_controller import CustomController
        from fastapi.testclient import TestClient
        from webui import app
        
        print("✅ Import test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Import test FAILED - Error: {e}")
        logger.error(f"Import error: {e}")
        return False


async def test_webui_basic():
    """Test basic WebUI functionality."""
    print("\n🌐 Testing WebUI Basic Functionality...")
    
    try:
        from fastapi.testclient import TestClient
        from webui import app
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        
        if response.status_code == 200:
            print("✅ WebUI basic test PASSED")
            return True
        else:
            print(f"❌ WebUI basic test FAILED - Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ WebUI basic test FAILED - Error: {e}")
        logger.error(f"WebUI test error: {e}")
        return False


async def main():
    """Run all basic tests."""
    print("🚀 Running Basic Functionality Tests")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("LLM Connection", test_llm_connection),
        ("WebUI Basic", test_webui_basic),
        ("Agent Functionality", test_basic_agent),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
            
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        icon = "✅" if result else "❌"
        print(f"{icon} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All basic tests PASSED! Ready for E2E testing.")
        return True
    else:
        print("⚠️ Some basic tests FAILED. Fix issues before E2E testing.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
