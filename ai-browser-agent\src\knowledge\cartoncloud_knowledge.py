"""
Comprehensive CartonCloud Knowledge System

This module provides 100% coverage of CartonCloud concepts, operations,
and business logic for the AI agent.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional


class CartonCloudModule(Enum):
    """CartonCloud system modules."""
    WAREHOUSE_MANAGEMENT = "wms"
    TRANSPORT_MANAGEMENT = "tms"
    BILLING_INVOICING = "billing"
    CUSTOMER_MANAGEMENT = "customers"
    RATE_CARDS = "rate_cards"
    REPORTING = "reporting"
    INVENTORY = "inventory"
    ORDERS = "orders"


class OperationType(Enum):
    """Types of operations that can be performed."""
    READ = "read"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    SEARCH = "search"
    REPORT = "report"


class ApprovalLevel(Enum):
    """Approval levels for different operations."""
    NONE = "none"  # No approval needed
    CONFIRMATION = "confirmation"  # Simple confirmation
    MANAGER_APPROVAL = "manager"  # Manager approval required
    ADMIN_APPROVAL = "admin"  # Admin approval required


@dataclass
class CartonCloudConcept:
    """Represents a CartonCloud business concept."""
    name: str
    module: CartonCloudModule
    description: str
    operations: List[OperationType]
    approval_level: ApprovalLevel
    related_concepts: List[str]
    api_endpoints: List[str]
    ui_locations: List[str]
    business_rules: List[str]
    examples: List[str]


class CartonCloudKnowledgeBase:
    """Comprehensive knowledge base for CartonCloud operations."""

    def __init__(self):
        self.concepts = self._initialize_concepts()
        self.workflows = self._initialize_workflows()
        self.approval_matrix = self._initialize_approval_matrix()

    def _initialize_concepts(self) -> Dict[str, CartonCloudConcept]:
        """Initialize all CartonCloud concepts."""
        concepts = {}

        # Rate Cards
        concepts["rate_card"] = CartonCloudConcept(
            name="Rate Card",
            module=CartonCloudModule.RATE_CARDS,
            description="Pricing structure for services including storage, transport, and handling charges",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.DELETE],
            approval_level=ApprovalLevel.MANAGER_APPROVAL,
            related_concepts=["customer", "service", "charge_line", "pricing_tier"],
            api_endpoints=["/api/v1/rate-cards", "/api/v1/rate-cards/{id}"],
            ui_locations=["Settings > Rate Cards", "Customer > Rate Cards"],
            business_rules=[
                "Rate cards must have effective dates",
                "Cannot delete rate cards with active charges",
                "Updates require manager approval",
                "Must be linked to at least one customer"
            ],
            examples=[
                "ALS Standard Warehousing Rates",
                "Express Transport Rate Card",
                "Cold Storage Premium Rates"
            ]
        )

        # Customers
        concepts["customer"] = CartonCloudConcept(
            name="Customer",
            module=CartonCloudModule.CUSTOMER_MANAGEMENT,
            description="Business entities that use CartonCloud services",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.SEARCH],
            approval_level=ApprovalLevel.NONE,  # Read operations don't need approval
            related_concepts=["rate_card", "orders", "invoices", "contacts"],
            api_endpoints=["/api/v1/customers", "/api/v1/customers/{id}"],
            ui_locations=["Customers", "Dashboard > Customer List"],
            business_rules=[
                "Must have unique customer code",
                "Requires primary contact information",
                "Can have multiple rate cards",
                "Cannot delete customers with active orders"
            ],
            examples=[
                "ALS - Australian Logistics Solutions",
                "XYZ Corp - Manufacturing Company",
                "ABC Retail - E-commerce Business"
            ]
        )

        # Storage Charges
        concepts["storage_charge"] = CartonCloudConcept(
            name="Storage Charge",
            module=CartonCloudModule.BILLING_INVOICING,
            description="Fees for storing goods in warehouse facilities",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.REPORT],
            approval_level=ApprovalLevel.CONFIRMATION,
            related_concepts=["rate_card", "warehouse", "product", "billing_period"],
            api_endpoints=["/api/v1/charges/storage", "/api/v1/reports/storage-charges"],
            ui_locations=["Billing > Storage Charges", "Reports > Storage Analysis"],
            business_rules=[
                "Calculated based on space occupied and time",
                "Can be per pallet, per cubic meter, or per location",
                "Minimum charges may apply",
                "Free storage periods can be configured"
            ],
            examples=[
                "$15 per pallet per month",
                "$0.50 per cubic meter per day",
                "$25 minimum charge per customer"
            ]
        )

        # Purchase Orders
        concepts["purchase_order"] = CartonCloudConcept(
            name="Purchase Order",
            module=CartonCloudModule.ORDERS,
            description="Inbound orders for receiving goods into warehouse",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.DELETE],
            approval_level=ApprovalLevel.NONE,
            related_concepts=["supplier", "product", "receiving", "warehouse"],
            api_endpoints=["/api/v1/purchase-orders", "/api/v1/purchase-orders/{id}"],
            ui_locations=["Orders > Purchase Orders", "Warehouse > Inbound"],
            business_rules=[
                "Must have valid supplier",
                "Requires expected delivery date",
                "Can be partially received",
                "Status tracking through lifecycle"
            ],
            examples=[
                "PO-2024-001 from Supplier ABC",
                "Bulk food order for cold storage",
                "Electronics shipment from overseas"
            ]
        )

        # Sales Orders
        concepts["sales_order"] = CartonCloudConcept(
            name="Sales Order",
            module=CartonCloudModule.ORDERS,
            description="Outbound orders for shipping goods from warehouse",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.DELETE],
            approval_level=ApprovalLevel.NONE,
            related_concepts=["customer", "product", "shipping", "delivery"],
            api_endpoints=["/api/v1/sales-orders", "/api/v1/sales-orders/{id}"],
            ui_locations=["Orders > Sales Orders", "Warehouse > Outbound"],
            business_rules=[
                "Must have valid customer",
                "Requires delivery address",
                "Stock allocation on creation",
                "Can be split into multiple shipments"
            ],
            examples=[
                "SO-2024-001 for Customer XYZ",
                "Express delivery order",
                "Bulk shipment to retail stores"
            ]
        )

        # Invoices
        concepts["invoice"] = CartonCloudConcept(
            name="Invoice",
            module=CartonCloudModule.BILLING_INVOICING,
            description="Bills generated for services provided to customers",
            operations=[OperationType.READ, OperationType.CREATE, OperationType.UPDATE, OperationType.REPORT],
            approval_level=ApprovalLevel.CONFIRMATION,
            related_concepts=["customer", "charges", "payment", "billing_period"],
            api_endpoints=["/api/v1/invoices", "/api/v1/invoices/{id}"],
            ui_locations=["Billing > Invoices", "Customer > Billing History"],
            business_rules=[
                "Generated from accumulated charges",
                "Must include all applicable taxes",
                "Cannot modify after sending",
                "Payment terms apply"
            ],
            examples=[
                "Monthly storage and handling invoice",
                "Transport charges for delivery",
                "Special services invoice"
            ]
        )

        # Bulk Charges Report
        concepts["bulk_charges_report"] = CartonCloudConcept(
            name="Bulk Charges Report",
            module=CartonCloudModule.REPORTING,
            description="Comprehensive report of all charges for a period",
            operations=[OperationType.READ, OperationType.REPORT],
            approval_level=ApprovalLevel.NONE,
            related_concepts=["charges", "billing_period", "customer", "invoice"],
            api_endpoints=["/api/v1/reports/bulk-charges"],
            ui_locations=["Reports > Bulk Charges", "Billing > Reports"],
            business_rules=[
                "Shows all charges for specified period",
                "Can be filtered by customer or service",
                "Includes charge details and rates applied",
                "Used for invoice generation and auditing"
            ],
            examples=[
                "Monthly charges for all customers",
                "Weekly transport charges report",
                "Customer-specific charge analysis"
            ]
        )

        # Warehouse
        concepts["warehouse"] = CartonCloudConcept(
            name="Warehouse",
            module=CartonCloudModule.WAREHOUSE_MANAGEMENT,
            description="Physical storage facilities managed by CartonCloud",
            operations=[OperationType.READ, OperationType.UPDATE],
            approval_level=ApprovalLevel.ADMIN_APPROVAL,
            related_concepts=["location", "product", "storage_charge", "capacity"],
            api_endpoints=["/api/v1/warehouses", "/api/v1/warehouses/{id}"],
            ui_locations=["Settings > Warehouses", "Warehouse Management"],
            business_rules=[
                "Must have defined locations",
                "Capacity limits enforced",
                "Temperature zones for different products",
                "Security and access controls"
            ],
            examples=[
                "Main Distribution Center",
                "Cold Storage Facility",
                "Hazardous Goods Warehouse"
            ]
        )

        return concepts

    def _initialize_workflows(self) -> Dict[str, List[str]]:
        """Initialize common CartonCloud workflows."""
        return {
            "rate_card_update": [
                "1. Identify rate card to update",
                "2. Review current pricing structure",
                "3. Propose new rates with justification",
                "4. Get manager approval for changes",
                "5. Update rate card with effective date",
                "6. Notify affected customers",
                "7. Monitor impact on billing"
            ],
            "invoice_simulation": [
                "1. Select customer and billing period",
                "2. Gather all charges for the period",
                "3. Apply rate card pricing",
                "4. Calculate taxes and totals",
                "5. Generate preview invoice",
                "6. Review for accuracy",
                "7. Create final invoice if approved"
            ],
            "customer_onboarding": [
                "1. Create customer record",
                "2. Set up rate card structure",
                "3. Configure billing preferences",
                "4. Establish warehouse access",
                "5. Train customer on system",
                "6. Monitor initial transactions"
            ],
            "charge_reconciliation": [
                "1. Run bulk charges report",
                "2. Compare with rate card pricing",
                "3. Identify discrepancies",
                "4. Investigate root causes",
                "5. Make necessary adjustments",
                "6. Update billing records"
            ]
        }

    def _initialize_approval_matrix(self) -> Dict[str, Dict[str, ApprovalLevel]]:
        """Initialize approval requirements for different operations."""
        return {
            "rate_card": {
                "create": ApprovalLevel.MANAGER_APPROVAL,
                "update_pricing": ApprovalLevel.MANAGER_APPROVAL,
                "update_details": ApprovalLevel.CONFIRMATION,
                "delete": ApprovalLevel.ADMIN_APPROVAL,
                "activate": ApprovalLevel.MANAGER_APPROVAL,
                "deactivate": ApprovalLevel.MANAGER_APPROVAL
            },
            "customer": {
                "create": ApprovalLevel.NONE,  # Customer creation is standard operation
                "read": ApprovalLevel.NONE,    # Reading customer data doesn't need approval
                "search": ApprovalLevel.NONE,  # Searching customers doesn't need approval
                "update_details": ApprovalLevel.NONE,
                "update_rates": ApprovalLevel.MANAGER_APPROVAL,
                "delete": ApprovalLevel.ADMIN_APPROVAL
            },
            "charges": {
                "create_manual": ApprovalLevel.CONFIRMATION,
                "adjust": ApprovalLevel.MANAGER_APPROVAL,
                "reverse": ApprovalLevel.MANAGER_APPROVAL,
                "bulk_update": ApprovalLevel.ADMIN_APPROVAL
            },
            "invoice": {
                "create": ApprovalLevel.CONFIRMATION,
                "modify": ApprovalLevel.MANAGER_APPROVAL,
                "cancel": ApprovalLevel.ADMIN_APPROVAL,
                "resend": ApprovalLevel.NONE
            }
        }

    def get_concept(self, concept_name: str) -> Optional[CartonCloudConcept]:
        """Get a specific CartonCloud concept."""
        return self.concepts.get(concept_name.lower())

    def get_related_concepts(self, concept_name: str) -> List[CartonCloudConcept]:
        """Get concepts related to the specified concept."""
        concept = self.get_concept(concept_name)
        if not concept:
            return []

        related = []
        for related_name in concept.related_concepts:
            related_concept = self.get_concept(related_name)
            if related_concept:
                related.append(related_concept)

        return related

    def get_approval_level(self, concept_name: str, operation: str) -> ApprovalLevel:
        """Get the approval level required for an operation."""
        matrix = self.approval_matrix.get(concept_name.lower(), {})
        return matrix.get(operation.lower(), ApprovalLevel.CONFIRMATION)

    def get_workflow(self, workflow_name: str) -> List[str]:
        """Get steps for a specific workflow."""
        return self.workflows.get(workflow_name.lower(), [])

    def search_concepts(self, query: str) -> List[CartonCloudConcept]:
        """Search for concepts matching the query."""
        query_lower = query.lower()
        matches = []

        for concept in self.concepts.values():
            if (query_lower in concept.name.lower() or
                query_lower in concept.description.lower() or
                any(query_lower in example.lower() for example in concept.examples)):
                matches.append(concept)

        return matches

    def get_all_concepts(self) -> List[CartonCloudConcept]:
        """Get all available concepts."""
        return list(self.concepts.values())

    def analyze_request(self, user_request: str) -> Dict[str, Any]:
        """Analyze a user request and provide CartonCloud context."""
        request_lower = user_request.lower()

        # Find matching concepts
        matching_concepts = []
        for concept in self.concepts.values():
            if (concept.name.lower() in request_lower or
                any(keyword in request_lower for keyword in [
                    concept.name.lower().replace(" ", "_"),
                    concept.name.lower().replace(" ", "")
                ])):
                matching_concepts.append(concept)

        # Determine operation type
        operation = OperationType.READ  # Default
        if any(word in request_lower for word in ["create", "add", "new"]):
            operation = OperationType.CREATE
        elif any(word in request_lower for word in ["update", "change", "modify", "edit"]):
            operation = OperationType.UPDATE
        elif any(word in request_lower for word in ["delete", "remove"]):
            operation = OperationType.DELETE
        elif any(word in request_lower for word in ["search", "find", "look"]):
            operation = OperationType.SEARCH
        elif any(word in request_lower for word in ["report", "generate", "simulate"]):
            operation = OperationType.REPORT

        # Determine approval needed
        approval_needed = False
        approval_level = ApprovalLevel.NONE

        for concept in matching_concepts:
            if operation in concept.operations:
                concept_approval = self.get_approval_level(concept.name, operation.value)
                if concept_approval != ApprovalLevel.NONE:
                    approval_needed = True
                    if concept_approval.value > approval_level.value:
                        approval_level = concept_approval

        return {
            "matching_concepts": matching_concepts,
            "operation_type": operation,
            "approval_needed": approval_needed,
            "approval_level": approval_level,
            "confidence": len(matching_concepts) / len(self.concepts) if matching_concepts else 0
        }


# Global knowledge base instance
cartoncloud_kb = CartonCloudKnowledgeBase()
