"""Tests for CartonCloud API client signatures and endpoint parity.

This module tests that the new CartonCloudAPI class provides 100% parity
with the official CartonCloud API specification.
"""

import pytest
import responses
from datetime import datetime
from decimal import Decimal
from unittest.mock import patch, MagicMock

from src.integrations.cartoncloud.api import CartonCloudAPI, CartonCloudAPIError
from src.integrations.cartoncloud.models import RateCard, PurchaseOrder, SalesOrder


class TestCartonCloudAPISignature:
    """Test CartonCloudAPI method signatures and endpoint coverage."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Provide mock credentials for testing
        self.api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    def test_api_initialization(self):
        """Test API client initialization."""
        assert self.api.base_url == "https://api.cartoncloud.com"
        assert self.api.client_id == "test_client_id"
        assert self.api.client_secret == "test_client_secret"
    
    @responses.activate
    def test_make_request_includes_version_header(self):
        """Test that all requests include Accept-Version: 1 header."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/test",
            json={"data": []},
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            self.api.make_request("GET", "/v1/test")
        
        # Check that the request included the required header
        assert len(responses.calls) == 1
        request_headers = responses.calls[0].request.headers
        assert request_headers["Accept-Version"] == "1"
    
    def test_error_handling_structure(self):
        """Test CartonCloudAPIError structure."""
        error = CartonCloudAPIError("Test error", "E001", 400)
        assert error.message == "Test error"
        assert error.code == "E001" 
        assert error.status_code == 400
        assert str(error) == "CartonCloud API Error [E001]: Test error"


@pytest.mark.parametrize("endpoint,method,params,expected_url", [
    # Rate Cards endpoints
    ("/v1/rate-cards", "GET", {"customer_id": "123"}, "https://api.cartoncloud.com/v1/rate-cards"),
    ("/v1/rate-cards/rc_001", "GET", {}, "https://api.cartoncloud.com/v1/rate-cards/rc_001"),
    ("/v1/rate-cards", "POST", {}, "https://api.cartoncloud.com/v1/rate-cards"),
    ("/v1/rate-cards/rc_001", "PUT", {}, "https://api.cartoncloud.com/v1/rate-cards/rc_001"),
    ("/v1/rate-cards/rc_001", "DELETE", {}, "https://api.cartoncloud.com/v1/rate-cards/rc_001"),
    
    # Purchase Orders endpoints  
    ("/v1/purchase-orders", "GET", {"status": "active"}, "https://api.cartoncloud.com/v1/purchase-orders"),
    ("/v1/purchase-orders/po_001", "GET", {}, "https://api.cartoncloud.com/v1/purchase-orders/po_001"),
    ("/v1/purchase-orders", "POST", {}, "https://api.cartoncloud.com/v1/purchase-orders"),
    ("/v1/purchase-orders/po_001", "PUT", {}, "https://api.cartoncloud.com/v1/purchase-orders/po_001"),
    
    # Sales Orders endpoints
    ("/v1/sales-orders", "GET", {"customer_id": "456"}, "https://api.cartoncloud.com/v1/sales-orders"),
    ("/v1/sales-orders/so_001", "GET", {}, "https://api.cartoncloud.com/v1/sales-orders/so_001"),
    ("/v1/sales-orders", "POST", {}, "https://api.cartoncloud.com/v1/sales-orders"),
    ("/v1/sales-orders/so_001", "PUT", {}, "https://api.cartoncloud.com/v1/sales-orders/so_001"),
    
    # Reports endpoints
    ("/v1/reports/bulk-charges", "GET", {"start_date": "2024-01-01"}, "https://api.cartoncloud.com/v1/reports/bulk-charges"),
])
class TestEndpointCoverage:
    """Test that all CartonCloud API endpoints are properly covered."""
    
    @responses.activate
    def test_endpoint_signature(self, endpoint, method, params, expected_url):
        """Test that each endpoint can be called with proper signature."""
        # Provide mock credentials for testing
        api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
        
        # Mock the response
        responses.add(
            getattr(responses, method),
            expected_url,
            json={"data": [], "pagination": {"page": 1, "total_pages": 1}},
            status=200 if method != "POST" else 201
        )
        
        with patch.object(api, '_refresh_token_if_needed', return_value="test_token"):
            if method == "GET":
                result = api.make_request(method, endpoint, params=params)
            else:
                result = api.make_request(method, endpoint, data={"test": "data"})
            
            assert "data" in result
        
        # Verify the request was made to the correct URL
        assert len(responses.calls) == 1
        assert responses.calls[0].request.url.startswith(expected_url)


class TestPaginationHandling:
    """Test pagination handling in CartonCloudAPI."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Provide mock credentials for testing
        self.api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    @responses.activate
    def test_paginate_with_total_pages_header(self):
        """Test pagination using Total-Pages header."""
        # Mock first page
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={"data": [{"id": "rc_001", "name": "Rate Card 1"}]},
            headers={"Total-Pages": "2"},
            status=200
        )
        
        # Mock second page
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={"data": [{"id": "rc_002", "name": "Rate Card 2"}]},
            headers={"Total-Pages": "2"},
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            results = list(self.api.paginate("/v1/rate-cards", page_size=1))
        
        assert len(results) == 2
        assert results[0]["id"] == "rc_001"
        assert results[1]["id"] == "rc_002"
        assert len(responses.calls) == 2
    
    @responses.activate
    def test_paginate_with_response_metadata(self):
        """Test pagination using response metadata."""
        # Mock first page
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/purchase-orders",
            json={
                "data": [{"id": "po_001", "po_number": "PO-001"}],
                "pagination": {"page": 1, "total_pages": 2, "has_more": True}
            },
            status=200
        )
        
        # Mock second page
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/purchase-orders",
            json={
                "data": [{"id": "po_002", "po_number": "PO-002"}],
                "pagination": {"page": 2, "total_pages": 2, "has_more": False}
            },
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            results = list(self.api.paginate("/v1/purchase-orders", page_size=1))
        
        assert len(results) == 2
        assert results[0]["id"] == "po_001"
        assert results[1]["id"] == "po_002"


class TestConvenienceMethods:
    """Test convenience methods that wrap common operations."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Provide mock credentials for testing
        self.api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    @responses.activate
    def test_get_rate_cards_method(self):
        """Test get_rate_cards convenience method."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={
                "data": [
                    {
                        "id": "rc_001",
                        "name": "Standard Rates",
                        "customer_id": "cust_001",
                        "effective_date": "2024-01-01T00:00:00Z",
                        "status": "active",
                        "charge_lines": []
                    }
                ]
            },
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            rate_cards = list(self.api.get_rate_cards(customer_id="cust_001"))
        
        assert len(rate_cards) == 1
        assert isinstance(rate_cards[0], RateCard)
        assert rate_cards[0].id == "rc_001"
        assert rate_cards[0].customer_id == "cust_001"
    
    @responses.activate
    def test_get_purchase_orders_method(self):
        """Test get_purchase_orders convenience method."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/purchase-orders",
            json={
                "data": [
                    {
                        "id": "po_001",
                        "po_number": "PO-2024-001",
                        "supplier_id": "supp_001",
                        "order_date": "2024-01-01T00:00:00Z",
                        "status": "draft",
                        "subtotal": "1000.00",
                        "total_amount": "1100.00",
                        "lines": []
                    }
                ]
            },
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            pos = list(self.api.get_purchase_orders(status="draft"))
        
        assert len(pos) == 1
        assert isinstance(pos[0], PurchaseOrder)
        assert pos[0].id == "po_001"
        assert pos[0].status == "draft"
    
    @responses.activate
    def test_get_sales_orders_method(self):
        """Test get_sales_orders convenience method."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/sales-orders",
            json={
                "data": [
                    {
                        "id": "so_001",
                        "so_number": "SO-2024-001",
                        "customer_id": "cust_001",
                        "order_date": "2024-01-01T00:00:00Z",
                        "status": "confirmed",
                        "subtotal": "500.00",
                        "total_amount": "550.00",
                        "lines": []
                    }
                ]
            },
            status=200
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            sos = list(self.api.get_sales_orders(customer_id="cust_001"))
        
        assert len(sos) == 1
        assert isinstance(sos[0], SalesOrder)
        assert sos[0].id == "so_001"
        assert sos[0].customer_id == "cust_001"


class TestErrorHandling:
    """Test error handling for various API scenarios."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Provide mock credentials for testing
        self.api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    @responses.activate
    def test_http_error_handling(self):
        """Test handling of HTTP errors."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards/invalid",
            json={"error": {"code": "NOT_FOUND", "message": "Rate card not found"}},
            status=404
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            with pytest.raises(CartonCloudAPIError) as exc_info:
                self.api.make_request("GET", "/v1/rate-cards/invalid")
            
            assert exc_info.value.status_code == 404
            assert exc_info.value.code == "NOT_FOUND"
            assert "Rate card not found" in exc_info.value.message
    
    @responses.activate  
    def test_validation_error_handling(self):
        """Test handling of validation errors."""
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={
                "error": {
                    "code": "VALIDATION_ERROR",
                    "message": "Validation failed",
                    "details": {"name": ["This field is required"]}
                }
            },
            status=422
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            with pytest.raises(CartonCloudAPIError) as exc_info:
                self.api.make_request("POST", "/v1/rate-cards", data={})
            
            assert exc_info.value.status_code == 422
            assert exc_info.value.code == "VALIDATION_ERROR"
            assert "Validation failed" in exc_info.value.message
    
    @responses.activate
    def test_server_error_handling(self):
        """Test handling of server errors."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={"error": {"code": "INTERNAL_ERROR", "message": "Internal server error"}},
            status=500
        )
        
        with patch.object(self.api, '_refresh_token_if_needed', return_value="test_token"):
            with pytest.raises(CartonCloudAPIError) as exc_info:
                self.api.make_request("GET", "/v1/rate-cards")
            
            assert exc_info.value.status_code == 500
            assert exc_info.value.code == "INTERNAL_ERROR"


class TestTokenManagement:
    """Test OAuth token management and refresh."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Provide mock credentials for testing
        self.api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
    
    def test_token_refresh_on_expiry(self):
        """Test that tokens are refreshed when expired."""
        with patch('src.integrations.cartoncloud.auth.get_token') as mock_get_token:
            mock_get_token.side_effect = ["expired_token", "new_token"]
            
            # First call should get the expired token
            token1 = self.api._refresh_token_if_needed()
            assert token1 == "expired_token"
            
            # Force token refresh by clearing the token
            self.api._token = None
            
            # Second call should get the new token
            token2 = self.api._refresh_token_if_needed()
            assert token2 == "new_token"
            
            assert mock_get_token.call_count == 2
