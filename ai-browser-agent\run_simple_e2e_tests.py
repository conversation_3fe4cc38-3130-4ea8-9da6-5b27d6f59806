"""
Simplified E2E Test Runner for AI Browser Agent

This script tests the basic UI functionality without complex dependencies.
"""

import asyncio
import json
import logging
import threading
import time
from datetime import datetime
from pathlib import Path

import httpx
import uvicorn
from playwright.async_api import async_playwright

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleE2ETestRunner:
    """Simplified E2E test runner."""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.server_thread = None
        self.base_url = "http://127.0.0.1:7861"
        self.test_results = []
        
    def start_server(self):
        """Start the FastAPI server in a background thread."""
        def run_server():
            from simple_webui import app
            uvicorn.run(app, host="127.0.0.1", port=7861, log_level="error")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
    async def test_server_health(self):
        """Test if the server is running."""
        test_result = {
            "test_name": "server_health",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": []
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    data = response.json()
                    test_result["status"] = "passed"
                    test_result["details"] = f"Server healthy: {data['status']}"
                else:
                    test_result["status"] = "failed"
                    test_result["errors"].append(f"Health check failed: {response.status_code}")
                    
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Server health check error: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_ui_functionality(self):
        """Test the UI with browser automation."""
        test_result = {
            "test_name": "ui_functionality",
            "status": "running", 
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": []
        }
        
        try:
            playwright = await async_playwright().start()
            browser = await playwright.chromium.launch(headless=self.headless)
            context = await browser.new_context()
            page = await context.new_page()
            
            # Navigate to the application
            await page.goto(self.base_url)
            test_result["steps"].append("Navigated to application")
            
            # Check if page loads
            title = await page.title()
            test_result["steps"].append(f"Page title: {title}")
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle")
            
            # Check for main elements
            try:
                # Look for chat interface elements
                chat_elements = await page.locator("#chat-title, .chat-title, h1").count()
                if chat_elements > 0:
                    test_result["steps"].append("Found chat interface elements")
                else:
                    test_result["errors"].append("No chat interface elements found")
                
                # Look for input elements
                input_elements = await page.locator("input, textarea").count()
                if input_elements > 0:
                    test_result["steps"].append(f"Found {input_elements} input elements")
                else:
                    test_result["errors"].append("No input elements found")
                
                # Look for buttons
                button_elements = await page.locator("button").count()
                if button_elements > 0:
                    test_result["steps"].append(f"Found {button_elements} button elements")
                else:
                    test_result["errors"].append("No button elements found")
                    
            except Exception as e:
                test_result["errors"].append(f"Element check error: {e}")
            
            # Test basic interaction if possible
            try:
                # Try to find and click new chat button
                new_chat_btn = page.locator('button:has-text("New Chat"), button:has-text("+ New Chat")')
                if await new_chat_btn.count() > 0:
                    await new_chat_btn.first.click()
                    test_result["steps"].append("Clicked new chat button")
                    await asyncio.sleep(1)
                
                # Try to find message input
                message_input = page.locator("#message-input, input[type='text'], textarea")
                if await message_input.count() > 0:
                    test_message = "Hello, this is a test message"
                    await message_input.first.fill(test_message)
                    test_result["steps"].append("Filled message input")
                    
                    # Try to send message
                    send_btn = page.locator('button[type="submit"], button:has-text("Send")')
                    if await send_btn.count() > 0:
                        await send_btn.first.click()
                        test_result["steps"].append("Clicked send button")
                        
                        # Wait for response
                        await asyncio.sleep(3)
                        
                        # Check if response appears
                        page_content = await page.content()
                        if "test response" in page_content.lower() or "processing" in page_content.lower():
                            test_result["steps"].append("Response received")
                        else:
                            test_result["errors"].append("No response detected")
                
            except Exception as e:
                test_result["errors"].append(f"Interaction test error: {e}")
            
            await browser.close()
            await playwright.stop()
            
            test_result["status"] = "passed" if not test_result["errors"] else "failed"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"UI test failed: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def test_api_endpoints(self):
        """Test the API endpoints."""
        test_result = {
            "test_name": "api_endpoints",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "steps": []
        }
        
        try:
            async with httpx.AsyncClient() as client:
                # Test create chat
                response = await client.post(f"{self.base_url}/api/chats")
                if response.status_code == 200:
                    chat_data = response.json()
                    chat_id = chat_data["chat_id"]
                    test_result["steps"].append(f"Created chat: {chat_id}")
                else:
                    test_result["errors"].append(f"Failed to create chat: {response.status_code}")
                    return test_result
                
                # Test list chats
                response = await client.get(f"{self.base_url}/api/chats")
                if response.status_code == 200:
                    chats = response.json()
                    test_result["steps"].append(f"Listed chats: {len(chats['chats'])} found")
                else:
                    test_result["errors"].append(f"Failed to list chats: {response.status_code}")
                
                # Test get specific chat
                response = await client.get(f"{self.base_url}/api/chats/{chat_id}")
                if response.status_code == 200:
                    test_result["steps"].append("Retrieved specific chat")
                else:
                    test_result["errors"].append(f"Failed to get chat: {response.status_code}")
                
                # Test send message
                message_data = {"message": "Test API message"}
                response = await client.post(
                    f"{self.base_url}/api/chats/{chat_id}/messages",
                    json=message_data
                )
                if response.status_code == 200:
                    test_result["steps"].append("Sent message via API")
                else:
                    test_result["errors"].append(f"Failed to send message: {response.status_code}")
                
                # Test delete chat
                response = await client.delete(f"{self.base_url}/api/chats/{chat_id}")
                if response.status_code == 200:
                    test_result["steps"].append("Deleted chat")
                else:
                    test_result["errors"].append(f"Failed to delete chat: {response.status_code}")
            
            test_result["status"] = "passed" if not test_result["errors"] else "failed"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"API test failed: {e}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    async def run_all_tests(self):
        """Run all tests."""
        print("🚀 Starting Simplified E2E Tests for AI Browser Agent")
        print("=" * 60)
        
        # Start server
        print("🔧 Starting test server...")
        self.start_server()
        
        # Run tests
        tests = [
            ("Server Health", self.test_server_health),
            ("API Endpoints", self.test_api_endpoints),
            ("UI Functionality", self.test_ui_functionality),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = await test_func()
            self.print_test_result(test_name, result)
        
        # Generate report
        self.generate_report()
    
    def print_test_result(self, test_name: str, result: dict):
        """Print test result."""
        status = result["status"]
        icon = "✅" if status == "passed" else "❌" if status == "failed" else "⏳"
        
        print(f"  {icon} {test_name}: {status.upper()}")
        
        if result.get("steps"):
            for step in result["steps"][:3]:  # Show first 3 steps
                print(f"    ✓ {step}")
        
        if result.get("errors"):
            for error in result["errors"][:2]:  # Show first 2 errors
                print(f"    ⚠️ {error}")
    
    def generate_report(self):
        """Generate test report."""
        passed = len([r for r in self.test_results if r["status"] == "passed"])
        failed = len([r for r in self.test_results if r["status"] == "failed"])
        total = len(self.test_results)
        
        print(f"\n{'='*60}")
        print("SIMPLIFIED E2E TEST REPORT")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {passed/total*100:.1f}%" if total > 0 else "No tests run")
        
        # Save detailed report
        report_path = Path("test_reports") / f"simple_e2e_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump({
                "summary": {"passed": passed, "failed": failed, "total": total},
                "results": self.test_results
            }, f, indent=2)
        
        print(f"Detailed report saved to: {report_path}")
        print(f"{'='*60}")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run simplified E2E tests")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    args = parser.parse_args()
    
    runner = SimpleE2ETestRunner(headless=args.headless)
    await runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
