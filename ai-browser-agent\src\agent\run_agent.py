"""Simple agent runner for chat interface."""

import logging
from collections.abc import <PERSON><PERSON><PERSON><PERSON><PERSON>

from browser_use.browser.browser import BrowserConfig
from browser_use.browser.context import Browser<PERSON>ontextWindowSize

from src.agent.custom_agent import CustomAgent
from src.agent.custom_prompts import Custom<PERSON>gentMessagePrompt
from src.agent.custom_prompts import CustomSystemPrompt
from src.browser.custom_browser import CustomBrowser
from src.browser.custom_context import BrowserContextConfig
from src.controller.custom_controller import CustomController
from src.utils import utils

logger = logging.getLogger(__name__)

# Global browser instances for persistence
_global_browser = None
_global_browser_context = None


async def run_agent(history: list[dict[str, str]]) -> AsyncIterator[str]:
    """
    Run the agent with chat history and yield streaming tokens.
    
    Args:
        history: List of messages with 'role' and 'content' keys
        
    Yields:
        str: Streaming tokens from the agent
    """
    global _global_browser, _global_browser_context

    try:
        # Extract the latest user message as the task
        if not history or history[-1]["role"] != "user":
            yield "Error: No user message found"
            return

        task = history[-1]["content"]

        # Get LLM configuration from environment
        llm = utils.get_llm_model(
            provider="anthropic",  # Default to anthropic
            model_name="claude-3-5-sonnet-20241022",
            temperature=0.1,
        )

        # Initialize browser if needed
        if _global_browser is None:
            _global_browser = CustomBrowser(
                config=BrowserConfig(
                    headless=True,
                    disable_security=True,
                )
            )

        if _global_browser_context is None:
            _global_browser_context = await _global_browser.new_context(
                config=BrowserContextConfig(
                    no_viewport=False,
                    browser_window_size=BrowserContextWindowSize(
                        width=1280, height=720
                    ),
                )
            )

        # Create controller and agent
        controller = CustomController()
        agent = CustomAgent(
            task=task,
            add_infos="",
            use_vision=True,
            llm=llm,
            browser=_global_browser,
            browser_context=_global_browser_context,
            controller=controller,
            system_prompt_class=CustomSystemPrompt,
            agent_prompt_class=CustomAgentMessagePrompt,
            max_actions_per_step=5,
            tool_calling_method="auto",
            max_input_tokens=128000,
            generate_gif=False
        )

        # Run agent and stream results
        yield f"🤖 Starting task: {task}\n\n"

        # For now, run the agent and return the final result
        # In a real implementation, you'd want to stream the actual agent steps
        agent_history = await agent.run(max_steps=10)

        final_result = agent_history.final_result()
        if final_result:
            yield f"✅ Task completed: {final_result}"
        else:
            yield "❌ Task completed but no result was returned"

        # Add any errors
        errors = agent_history.errors()
        if errors:
            yield f"\n\n⚠️ Errors encountered:\n{errors}"

    except Exception as e:
        logger.error(f"Error running agent: {e}")
        yield f"❌ Error: {str(e)}"


async def cleanup_browser():
    """Clean up global browser instances."""
    global _global_browser, _global_browser_context

    if _global_browser_context:
        await _global_browser_context.close()
        _global_browser_context = None

    if _global_browser:
        await _global_browser.close()
        _global_browser = None
