# Core dependencies
browser-use
playwright
langchain-core
langchain-openai
langchain-anthropic
langchain-google-genai
langchain-mistralai
langchain-ollama
langchain-community
fastapi
uvicorn[standard]
jinja2
sqlmodel
sse-starlette
ruff
mypy
pytest
pytest-asyncio
pytest-playwright
pytest-cov
responses
playwright
httpx
python-dotenv
click

# CartonCloud integration
pydantic>=2.0.0
requests
tenacity
PyYAML
openapi-python-client
openpyxl

# RAG and document processing
chromadb>=0.4.0
beautifulsoup4
html2text
unstructured[local-inference]
pdfplumber
python-docx

# Testing
pytest
pytest-asyncio
responses

# Development
ruff
mypy
pre-commit

# Image processing (existing dependency)
Pillow

# JSON repair (existing dependency)
json-repair
