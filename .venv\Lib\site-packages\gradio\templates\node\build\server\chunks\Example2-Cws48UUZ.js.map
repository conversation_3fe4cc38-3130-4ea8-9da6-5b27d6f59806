{"version": 3, "file": "Example2-Cws48UUZ.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example2.js"], "sourcesContent": ["import { create_ssr_component, escape } from \"svelte/internal\";\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { title } = $$props;\n  let { x } = $$props;\n  let { y } = $$props;\n  if ($$props.title === void 0 && $$bindings.title && title !== void 0)\n    $$bindings.title(title);\n  if ($$props.x === void 0 && $$bindings.x && x !== void 0)\n    $$bindings.x(x);\n  if ($$props.y === void 0 && $$bindings.y && y !== void 0)\n    $$bindings.y(y);\n  return `${title ? `${escape(title)}` : `${escape(x)} x ${escape(y)}`}`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;AACK,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;AAC1D,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;;;;"}