["test_manual.py::test_api_initialization", "test_manual.py::test_error_handling_structure", "test_simple.py::test_api_initialization", "test_simple.py::test_error_class", "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudAuth::test_get_token_failure", "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudAuth::test_get_token_success", "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudEndpoints::test_create_purchase_order", "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudEndpoints::test_list_rate_cards", "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudIntegration::test_nightly_audit_workflow", "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_api_initialization", "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_error_handling_structure", "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_make_request_includes_version_header", "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_purchase_orders_method", "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_rate_cards_method", "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_sales_orders_method", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-GET-params5-https://api.cartoncloud.com/v1/purchase-orders]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-POST-params7-https://api.cartoncloud.com/v1/purchase-orders]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-GET-params6-https://api.cartoncloud.com/v1/purchase-orders/po_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-PUT-params8-https://api.cartoncloud.com/v1/purchase-orders/po_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-GET-params0-https://api.cartoncloud.com/v1/rate-cards]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-POST-params2-https://api.cartoncloud.com/v1/rate-cards]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-DELETE-params4-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-GET-params1-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-PUT-params3-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/reports/bulk-charges-GET-params13-https://api.cartoncloud.com/v1/reports/bulk-charges]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-GET-params9-https://api.cartoncloud.com/v1/sales-orders]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-POST-params11-https://api.cartoncloud.com/v1/sales-orders]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-GET-params10-https://api.cartoncloud.com/v1/sales-orders/so_001]", "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-PUT-params12-https://api.cartoncloud.com/v1/sales-orders/so_001]", "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_http_error_handling", "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_server_error_handling", "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_validation_error_handling", "tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_response_metadata", "tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_total_pages_header", "tests/cartoncloud/test_client_signature.py::TestTokenManagement::test_token_refresh_on_expiry", "tests/cartoncloud/test_client_signature_fixed.py::TestCartonCloudAPISignature::test_api_initialization", "tests/cartoncloud/test_client_signature_fixed.py::TestCartonCloudAPISignature::test_error_handling_structure", "tests/cartoncloud/test_client_signature_fixed.py::TestCartonCloudAPISignature::test_make_request_includes_version_header", "tests/cartoncloud/test_client_signature_fixed.py::TestConvenienceMethods::test_get_purchase_orders_method", "tests/cartoncloud/test_client_signature_fixed.py::TestConvenienceMethods::test_get_rate_cards_method", "tests/cartoncloud/test_client_signature_fixed.py::TestConvenienceMethods::test_get_sales_orders_method", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-GET-params5-https://api.cartoncloud.com/v1/purchase-orders]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-POST-params7-https://api.cartoncloud.com/v1/purchase-orders]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-GET-params6-https://api.cartoncloud.com/v1/purchase-orders/po_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-PUT-params8-https://api.cartoncloud.com/v1/purchase-orders/po_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-GET-params0-https://api.cartoncloud.com/v1/rate-cards]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-POST-params2-https://api.cartoncloud.com/v1/rate-cards]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-DELETE-params4-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-GET-params1-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-PUT-params3-https://api.cartoncloud.com/v1/rate-cards/rc_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/reports/bulk-charges-GET-params13-https://api.cartoncloud.com/v1/reports/bulk-charges]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-GET-params9-https://api.cartoncloud.com/v1/sales-orders]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-POST-params11-https://api.cartoncloud.com/v1/sales-orders]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-GET-params10-https://api.cartoncloud.com/v1/sales-orders/so_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-PUT-params12-https://api.cartoncloud.com/v1/sales-orders/so_001]", "tests/cartoncloud/test_client_signature_fixed.py::TestErrorHandling::test_http_error_handling", "tests/cartoncloud/test_client_signature_fixed.py::TestErrorHandling::test_server_error_handling", "tests/cartoncloud/test_client_signature_fixed.py::TestErrorHandling::test_validation_error_handling", "tests/cartoncloud/test_client_signature_fixed.py::TestPaginationHandling::test_paginate_with_response_metadata", "tests/cartoncloud/test_client_signature_fixed.py::TestPaginationHandling::test_paginate_with_total_pages_header", "tests/cartoncloud/test_client_signature_fixed.py::TestTokenManagement::test_token_refresh_on_expiry", "tests/cartoncloud/test_endpoints.py::test_create_purchase_order", "tests/cartoncloud/test_endpoints.py::test_create_sales_order", "tests/cartoncloud/test_endpoints.py::test_deprecation_warning", "tests/cartoncloud/test_endpoints.py::test_get_bulk_charges_report", "tests/cartoncloud/test_endpoints.py::test_get_purchase_order_found", "tests/cartoncloud/test_endpoints.py::test_get_purchase_order_not_found", "tests/cartoncloud/test_endpoints.py::test_get_rate_card_found", "tests/cartoncloud/test_endpoints.py::test_get_rate_card_not_found", "tests/cartoncloud/test_endpoints.py::test_get_sales_order_found", "tests/cartoncloud/test_endpoints.py::test_get_sales_order_not_found", "tests/cartoncloud/test_endpoints.py::test_get_yesterday_bulk_charges", "tests/cartoncloud/test_endpoints.py::test_list_rate_cards", "tests/cartoncloud/test_endpoints.py::test_request_delegates_and_warns", "tests/cartoncloud/test_endpoints.py::test_request_raises_on_api_error", "tests/cartoncloud/test_rate_card_conversion.py::test_normalize_service_id", "tests/cartoncloud/test_rate_card_conversion.py::test_parse_price_string_basic", "tests/cartoncloud/test_rate_card_conversion.py::test_rate_card_lookup_quote_logic", "tests/cartoncloud/test_rate_card_conversion.py::test_write_and_read_yaml", "tests/test_browser_use.py::test_browser_use_org", "tests/test_deep_research.py::test_deep_research", "tests/test_llm_api.py::test_azure_openai_model", "tests/test_llm_api.py::test_deepseek_model", "tests/test_llm_api.py::test_deepseek_r1_model", "tests/test_llm_api.py::test_deepseek_r1_ollama_model", "tests/test_llm_api.py::test_google_model", "tests/test_llm_api.py::test_llm", "tests/test_llm_api.py::test_mistral_model", "tests/test_llm_api.py::test_moonshot_model", "tests/test_llm_api.py::test_ollama_model", "tests/test_llm_api.py::test_openai_model", "tests/test_playwright.py::test_connect_browser"]