[mypy]
python_version = 3.11
strict = True
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

# Ignore missing imports for third-party libraries
ignore_missing_imports = True

# Specific module configurations
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-browser_use.*]
ignore_missing_imports = True

[mypy-playwright.*]
ignore_missing_imports = True

[mypy-langchain.*]
ignore_missing_imports = True

[mypy-main_content_extractor]
ignore_missing_imports = True

[mypy-json_repair]
ignore_missing_imports = True
