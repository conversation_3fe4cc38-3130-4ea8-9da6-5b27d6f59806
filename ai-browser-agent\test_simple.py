#!/usr/bin/env python3
"""Simple test to verify API functionality."""

import sys
import os
sys.path.append('.')

from src.integrations.cartoncloud.api import CartonCloudAPI, CartonCloudAPIError

def test_api_initialization():
    """Test that the API can be initialized."""
    try:
        api = CartonCloudAPI(
            client_id="test_client_id",
            client_secret="test_client_secret"
        )
        assert api.base_url == "https://api.cartoncloud.com"
        assert api.client_id == "test_client_id"
        assert api.client_secret == "test_client_secret"
        print("✅ API initialization test passed!")
        return True
    except Exception as e:
        print(f"❌ API initialization test failed: {e}")
        return False

def test_error_class():
    """Test that the error class works correctly."""
    try:
        error = CartonCloudAPIError("Test error", "E001", 400)
        assert error.message == "Test error"
        assert error.code == "E001"
        assert error.status_code == 400
        print("✅ Error class test passed!")
        return True
    except Exception as e:
        print(f"❌ Error class test failed: {e}")
        return False

def main():
    """Run all simple tests."""
    print("Running simple CartonCloud API tests...")
    
    tests = [test_api_initialization, test_error_class]
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
