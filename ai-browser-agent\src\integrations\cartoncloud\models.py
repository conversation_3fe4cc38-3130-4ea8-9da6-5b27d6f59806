"""Pydantic models for CartonCloud API data structures."""

from datetime import datetime
from decimal import Decimal
from typing import Any

from pydantic import BaseModel
from pydantic import Field


class ChargeLine(BaseModel):
    """Represents a charge line in a rate card."""

    id: str | None = None
    charge_code: str = Field(..., description="Charge code identifier")
    description: str = Field(..., description="Charge description")
    unit_of_measure: str = Field(..., description="Unit of measure (m³, m², pallet/day, etc.)")
    rate: Decimal = Field(..., description="Rate amount")
    minimum_charge: Decimal | None = Field(None, description="Minimum charge amount")
    currency: str = Field(default="AUD", description="Currency code")
    created_at: datetime | None = None
    updated_at: datetime | None = None


class RateCard(BaseModel):
    """Represents a CartonCloud rate card."""

    id: str | None = None
    name: str = Field(..., description="Rate card name")
    customer_id: str = Field(..., description="Associated customer ID")
    effective_date: datetime = Field(..., description="Effective date")
    expiry_date: datetime | None = Field(None, description="Expiry date")
    charge_lines: list[ChargeLine] = Field(default_factory=list, description="Charge lines")
    status: str = Field(default="active", description="Rate card status")
    created_at: datetime | None = None
    updated_at: datetime | None = None


class PurchaseOrderLine(BaseModel):
    """Represents a line item in a purchase order."""

    id: str | None = None
    product_code: str = Field(..., description="Product code")
    description: str = Field(..., description="Line item description")
    quantity: Decimal = Field(..., description="Quantity")
    unit_price: Decimal = Field(..., description="Unit price")
    total_price: Decimal = Field(..., description="Total line price")
    unit_of_measure: str = Field(..., description="Unit of measure")


class PurchaseOrder(BaseModel):
    """Represents a CartonCloud purchase order."""

    id: str | None = None
    po_number: str = Field(..., description="Purchase order number")
    supplier_id: str = Field(..., description="Supplier ID")
    order_date: datetime = Field(..., description="Order date")
    delivery_date: datetime | None = Field(None, description="Expected delivery date")
    status: str = Field(default="draft", description="Order status")
    lines: list[PurchaseOrderLine] = Field(default_factory=list, description="Order lines")
    subtotal: Decimal = Field(..., description="Subtotal amount")
    tax_amount: Decimal = Field(default=Decimal("0"), description="Tax amount")
    total_amount: Decimal = Field(..., description="Total amount")
    currency: str = Field(default="AUD", description="Currency code")
    notes: str | None = Field(None, description="Order notes")
    created_at: datetime | None = None
    updated_at: datetime | None = None


class SalesOrderLine(BaseModel):
    """Represents a line item in a sales order."""

    id: str | None = None
    product_code: str = Field(..., description="Product code")
    description: str = Field(..., description="Line item description")
    quantity: Decimal = Field(..., description="Quantity")
    unit_price: Decimal = Field(..., description="Unit price")
    total_price: Decimal = Field(..., description="Total line price")
    unit_of_measure: str = Field(..., description="Unit of measure")


class SalesOrder(BaseModel):
    """Represents a CartonCloud sales order."""

    id: str | None = None
    so_number: str = Field(..., description="Sales order number")
    customer_id: str = Field(..., description="Customer ID")
    order_date: datetime = Field(..., description="Order date")
    delivery_date: datetime | None = Field(None, description="Expected delivery date")
    status: str = Field(default="draft", description="Order status")
    lines: list[SalesOrderLine] = Field(default_factory=list, description="Order lines")
    subtotal: Decimal = Field(..., description="Subtotal amount")
    tax_amount: Decimal = Field(default=Decimal("0"), description="Tax amount")
    total_amount: Decimal = Field(..., description="Total amount")
    currency: str = Field(default="AUD", description="Currency code")
    notes: str | None = Field(None, description="Order notes")
    created_at: datetime | None = None
    updated_at: datetime | None = None


class BulkChargeReportLine(BaseModel):
    """Represents a line in the bulk charges report."""

    id: str = Field(..., description="Charge line ID")
    customer_id: str = Field(..., description="Customer ID")
    customer_name: str = Field(..., description="Customer name")
    charge_code: str = Field(..., description="Charge code")
    description: str = Field(..., description="Charge description")
    unit_of_measure: str = Field(..., description="Unit of measure")
    quantity: Decimal = Field(..., description="Quantity charged")
    rate: Decimal = Field(..., description="Rate applied")
    amount: Decimal = Field(..., description="Total charge amount")
    date_charged: datetime = Field(..., description="Date the charge was applied")
    invoice_id: str | None = Field(None, description="Associated invoice ID")


class ApiResponse(BaseModel):
    """Generic API response wrapper."""

    success: bool = Field(..., description="Whether the request was successful")
    data: dict[str, Any] | None = Field(None, description="Response data")
    error: str | None = Field(None, description="Error message if any")
    pagination: dict[str, Any] | None = Field(None, description="Pagination info")
