{"version": 3, "file": "Example11-CEeECD4f.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example11.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport { I as Image } from \"./Image.js\";\nconst css = {\n  code: \".container.svelte-a9zvka img{width:100%;height:100%}.container.selected.svelte-a9zvka{border-color:var(--border-color-accent)}.border.table.svelte-a9zvka{border:2px solid var(--border-color-primary)}.container.table.svelte-a9zvka{margin:0 auto;border-radius:var(--radius-lg);overflow:hidden;width:var(--size-20);height:var(--size-20);object-fit:cover}.container.gallery.svelte-a9zvka{width:var(--size-20);max-width:var(--size-20);object-fit:cover}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import Image from \\\\\"./shared/Image.svelte\\\\\";\\\\nexport let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"container\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass:border={value}\\\\n>\\\\n\\\\t{#if value}\\\\n\\\\t\\\\t<Image src={value.url} alt=\\\\\"\\\\\" />\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.selected {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.border.table {\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.container.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\theight: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.gallery {\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tmax-width: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmBC,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,UAAU,uBAAU,CACnB,YAAY,CAAE,IAAI,qBAAqB,CACxC,CACA,OAAO,oBAAO,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,UAAU,oBAAO,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,KACb,CAEA,UAAU,sBAAS,CAClB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,KACb\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<div class=\"${[\n    \"container svelte-a9zvka\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\") + \" \" + (value ? \"border\" : \"\")\n  ].join(\" \").trim()}\">${value ? `${validate_component(Image, \"Image\").$$render($$result, { src: value.url, alt: \"\" }, {}, {})}` : ``} </div>`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,icAAic;AACzc,EAAE,GAAG,EAAE,+jDAA+jD;AACtkD,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,yBAAyB;AAC7B,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAC;AACxJ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC/I,CAAC;;;;"}