{"version": "2.0.0", "tasks": [{"label": "Run Chat UI", "type": "shell", "command": "u<PERSON><PERSON>", "args": ["webui:app", "--reload", "--port", "7860"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Run Nightly Audit Once", "type": "shell", "command": "python", "args": ["tasks/nightly_audit.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Playwright <PERSON> (Chrome)", "type": "shell", "command": "python", "args": ["-m", "playwright", "codegen", "--channel=chrome"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Install Playwright Chrome", "type": "shell", "command": "python", "args": ["-m", "playwright", "install", "chrome"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "python", "args": ["-m", "pytest", "tests/", "-v"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "<PERSON><PERSON> with <PERSON><PERSON>", "type": "shell", "command": "ruff", "args": ["check", "src/", "tests/", "tasks/"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Type Check with MyPy", "type": "shell", "command": "mypy", "args": ["src/", "--ignore-missing-imports"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Rebuild CC Docstore", "type": "shell", "command": "python", "args": ["scripts/build_cc_docstore.py", "--recreate"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Check CC Docstore Status", "type": "shell", "command": "python", "args": ["tasks/cc_docstore_manager.py", "status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Build CC Web Docs Only", "type": "shell", "command": "python", "args": ["scripts/build_cc_docstore.py", "--sources", "web"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Build CC Local Docs Only", "type": "shell", "command": "python", "args": ["scripts/build_cc_docstore.py", "--sources", "local"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Fetch CartonCloud Spec", "type": "shell", "command": "python", "args": ["scripts/fetch_cc_spec.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Convert Rate Card", "type": "shell", "command": "python", "args": ["scripts/convert_ratecard.py"], "group": "build", "presentation": {"reveal": "always"}, "problemMatcher": []}]}