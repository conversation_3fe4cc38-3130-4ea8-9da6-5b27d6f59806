"""Build CartonCloud documentation vector store for RAG with recursive crawling."""

import os
import asyncio
import logging
import click
from pathlib import Path
from typing import List, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse, urlunparse
from urllib.robotparser import RobotFileParser
import requests
from bs4 import BeautifulSoup, Comment
import html2text
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.schema import Document
from datetime import datetime
import shutil
import time

# Import our enhanced tools
import sys
sys.path.append(str(Path(__file__).parent.parent))
from src.tools.cc_docs_embedder import CartonCloudDocumentEmbedder
from src.tools.cc_local_docs_loader import CartonCloudLocalDocsLoader

logger = logging.getLogger(__name__)


class WebCrawler:
    """Recursive web crawler for CartonCloud documentation sites."""
    
    def __init__(self, max_depth: int = 2, max_pages: int = 400, delay: float = 1.0):
        self.max_depth = max_depth
        self.max_pages = max_pages
        self.delay = delay
        self.visited_urls: Set[str] = set()
        self.robots_cache: dict = {}
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def _normalize_url(self, url: str) -> str:
        """Normalize URL by removing fragments and query parameters."""
        parsed = urlparse(url)
        return urlunparse((parsed.scheme, parsed.netloc, parsed.path, '', '', ''))
    
    def _is_valid_url(self, url: str, base_domain: str, base_path: str, current_depth: int) -> bool:
        """Check if URL should be crawled."""
        try:
            parsed = urlparse(url)
            
            # Must be same domain
            if parsed.netloc != base_domain:
                return False
            
            # Must be within depth limit
            if current_depth > self.max_depth:
                return False
            
            # Must start with base path
            if not parsed.path.startswith(base_path):
                return False
            
            # Calculate depth from base path
            relative_path = parsed.path[len(base_path):].strip('/')
            if relative_path:
                depth = len(relative_path.split('/'))
                if depth > self.max_depth:
                    return False
            
            # Must end in valid extension or be directory
            path = parsed.path.lower()
            if not (path.endswith('/') or path.endswith('.html') or path.endswith('.htm') or 
                   '.' not in os.path.basename(path)):
                return False
            
            # Skip common non-content files
            skip_patterns = [
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.zip', '.tar', '.gz',
                '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico', '.css', '.js',
                '.xml', '.json', '.rss', '/search', '/login', '/logout', '/admin'
            ]
            
            for pattern in skip_patterns:
                if pattern in path:
                    return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Invalid URL {url}: {e}")
            return False
    
    def _check_robots_txt(self, base_url: str, url: str) -> bool:
        """Check if URL is allowed by robots.txt."""
        try:
            parsed_base = urlparse(base_url)
            robots_url = f"{parsed_base.scheme}://{parsed_base.netloc}/robots.txt"
            
            if robots_url not in self.robots_cache:
                try:
                    rp = RobotFileParser()
                    rp.set_url(robots_url)
                    rp.read()
                    self.robots_cache[robots_url] = rp
                except Exception:
                    # If robots.txt is not accessible, allow all
                    self.robots_cache[robots_url] = None
            
            robots_parser = self.robots_cache[robots_url]
            if robots_parser is None:
                return True
            
            return robots_parser.can_fetch('*', url)
            
        except Exception:
            return True  # Default to allowing if check fails
    
    def _extract_links(self, html_content: str, base_url: str) -> List[str]:
        """Extract all links from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for link in soup.find_all('a', href=True):
                href = link['href'].strip()
                if href:
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(base_url, href)
                    normalized = self._normalize_url(absolute_url)
                    if normalized not in self.visited_urls:
                        links.append(normalized)
            
            return links
            
        except Exception as e:
            logger.debug(f"Error extracting links: {e}")
            return []
    
    def crawl_site(self, base_url: str) -> List[str]:
        """Crawl a website recursively and return list of URLs."""
        logger.info(f"🕷️ Starting crawl of {base_url} (max_depth={self.max_depth}, max_pages={self.max_pages})")
        
        parsed_base = urlparse(base_url)
        base_domain = parsed_base.netloc
        base_path = parsed_base.path.rstrip('/')
        
        # Initialize crawl queue with base URL
        crawl_queue: List[Tuple[str, int]] = [(base_url, 0)]
        crawled_urls: List[str] = []
        
        while crawl_queue and len(crawled_urls) < self.max_pages:
            current_url, depth = crawl_queue.pop(0)
            
            # Skip if already visited
            if current_url in self.visited_urls:
                continue
            
            # Validate URL
            if not self._is_valid_url(current_url, base_domain, base_path, depth):
                continue
            
            # Check robots.txt
            if not self._check_robots_txt(base_url, current_url):
                logger.debug(f"Robots.txt disallows: {current_url}")
                continue
            
            try:
                logger.info(f"📄 Crawling [{len(crawled_urls)+1}/{self.max_pages}] depth={depth}: {current_url}")
                
                # Fetch page
                response = self.session.get(current_url, timeout=30)
                response.raise_for_status()
                
                # Mark as visited
                self.visited_urls.add(current_url)
                crawled_urls.append(current_url)
                
                # Extract links for next level (if not at max depth)
                if depth < self.max_depth:
                    links = self._extract_links(response.text, current_url)
                    for link in links[:50]:  # Limit links per page to avoid explosion
                        if (link, depth + 1) not in crawl_queue:
                            crawl_queue.append((link, depth + 1))
                
                # Rate limiting
                if self.delay > 0:
                    time.sleep(self.delay)
                    
            except Exception as e:
                logger.warning(f"Error crawling {current_url}: {e}")
                continue
        
        logger.info(f"✅ Crawl complete: {len(crawled_urls)} pages discovered from {base_url}")
        return crawled_urls


def load_url_list(file_path: str = "scripts/cc_doc_urls.txt") -> Tuple[List[str], List[str]]:
    """Load URLs from text file, separating crawl bases from direct URLs.
    
    Args:
        file_path: Path to text file containing URLs
        
    Returns:
        Tuple of (crawl_base_urls, direct_urls)
    """
    urls_file = Path(file_path)
    if not urls_file.exists():
        logger.warning(f"URL list file not found: {file_path}")
        return [], []
    
    crawl_bases = []
    direct_urls = []
    
    with open(urls_file, 'r', encoding='utf-8') as f:
        for line in f:
            url = line.strip()
            if url and not url.startswith('#'):  # Skip empty lines and comments
                if url.startswith('* '):
                    # Crawl base URL
                    crawl_bases.append(url[2:].strip())
                else:
                    # Direct URL
                    direct_urls.append(url)
    
    return crawl_bases, direct_urls


def clean_html_content(html_content: str) -> str:
    """Clean HTML content by removing navigation, footers, and converting to markdown."""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.extract()
    
    # Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()
    
    # Remove common navigation and footer elements
    for element in soup.find_all(['nav', 'header', 'footer']):
        element.extract()
    
    # Remove elements by class/id patterns
    selectors_to_remove = [
        '.navigation', '.nav', '.sidebar', '.footer', '.header',
        '#navigation', '#nav', '#sidebar', '#footer', '#header',
        '.breadcrumb', '.menu', '.social', '.advertisement'
    ]
    
    for selector in selectors_to_remove:
        for element in soup.select(selector):
            element.extract()
    
    # Convert to markdown
    h = html2text.HTML2Text()
    h.ignore_links = False
    h.ignore_images = False
    h.body_width = 0  # No line wrapping
    
    markdown_content = h.handle(str(soup))
    
    # Clean up excessive whitespace
    lines = markdown_content.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if not line:
            if not prev_empty:
                cleaned_lines.append('')
                prev_empty = True
        else:
            cleaned_lines.append(line)
            prev_empty = False
    
    return '\n'.join(cleaned_lines)


def download_and_save_page(url: str, output_dir: Path) -> Optional[str]:
    """Download a web page and save as markdown."""
    try:
        logger.debug(f"Downloading: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Clean and convert content
        markdown_content = clean_html_content(response.text)
        
        # Generate filename from URL
        filename = url.replace('https://', '').replace('http://', '')
        filename = filename.replace('/', '_').replace('?', '_').replace('&', '_')
        filename = filename[:100] + '.md'  # Limit filename length
        
        # Add metadata header
        metadata = f"""---
source_url: {url}
downloaded_at: {datetime.now().isoformat()}
---

"""
        
        content_with_metadata = metadata + markdown_content
        
        # Save to file
        file_path = output_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content_with_metadata)
        
        return str(file_path)
        
    except Exception as e:
        logger.error(f"Error downloading {url}: {e}")
        return None


@click.command()
@click.option('--recreate', is_flag=True, help='Recreate vector store from scratch')
@click.option('--max-pages', default=400, help='Maximum number of pages to crawl per domain')
@click.option('--sources', default='both', type=click.Choice(['web', 'local', 'both']),
              help='Which sources to include (default: both)')
@click.option('--vectorstore-path', type=str, help='Custom path for vector store')
@click.option('--local-docs-dirs', multiple=True, help='Custom directories for local documents')
def main(recreate, max_pages, sources, vectorstore_path, local_docs_dirs):
    """Build CartonCloud documentation vector store with recursive crawling."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Get crawl configuration from environment
        crawl_depth = int(os.getenv('CC_DOC_CRAWL_DEPTH', '2'))
        max_pages_env = int(os.getenv('CC_DOC_MAX_PAGES', str(max_pages)))
        
        # Determine which sources to include
        include_web = sources in ["web", "both"]
        include_local = sources in ["local", "both"]
        
        logger.info(f"🚀 Building CartonCloud docstore with sources: {sources}")
        logger.info(f"📄 Web docs: {'✅' if include_web else '❌'}")
        logger.info(f"📁 Local docs: {'✅' if include_local else '❌'}")
        logger.info(f"🕷️ Crawl depth: {crawl_depth}, Max pages: {max_pages_env}")
        
        # If web docs are requested, run crawling
        if include_web:
            logger.info("🌐 Starting web document collection...")
            
            # Load URL configuration
            crawl_bases, direct_urls = load_url_list("scripts/cc_doc_urls.txt")
            
            # Create docs directory
            docs_dir = Path("docs/cartoncloud")
            if recreate and docs_dir.exists():
                logger.info(f"🗑️ Removing existing web docs: {docs_dir}")
                shutil.rmtree(docs_dir)
            docs_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize crawler
            crawler = WebCrawler(max_depth=crawl_depth, max_pages=max_pages_env)
            
            all_urls = []
            
            # Crawl base URLs
            for base_url in crawl_bases:
                logger.info(f"🕷️ Crawling base: {base_url}")
                discovered_urls = crawler.crawl_site(base_url)
                all_urls.extend(discovered_urls)
                logger.info(f"📊 Found {len(discovered_urls)} pages from {base_url}")
            
            # Add direct URLs
            all_urls.extend(direct_urls)
            
            # Remove duplicates
            unique_urls = list(dict.fromkeys(all_urls))
            logger.info(f"📄 Total unique URLs to process: {len(unique_urls)}")
            
            # Download all pages
            downloaded_files = []
            for i, url in enumerate(unique_urls, 1):
                logger.info(f"⬇️ Downloading [{i}/{len(unique_urls)}]: {url}")
                file_path = download_and_save_page(url, docs_dir)
                if file_path:
                    downloaded_files.append(file_path)
            
            logger.info(f"✅ Downloaded {len(downloaded_files)} web pages successfully")
            
            # Log domain statistics
            domain_stats = {}
            for url in unique_urls:
                domain = urlparse(url).netloc
                domain_stats[domain] = domain_stats.get(domain, 0) + 1
            
            logger.info("📊 Pages per domain:")
            for domain, count in domain_stats.items():
                logger.info(f"  {domain}: {count} pages")
        
        # Initialize the enhanced embedder
        embedder = CartonCloudDocumentEmbedder(
            vectorstore_path=vectorstore_path,
            include_local_docs=include_local,
            include_web_docs=include_web,
            local_docs_dirs=list(local_docs_dirs) if local_docs_dirs else None
        )
        
        # Get initial stats
        stats = embedder.get_stats()
        logger.info("📊 Pre-build statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # Build the vector store
        success = embedder.rebuild_vectorstore() if recreate else embedder.build_vectorstore()
        
        if success:
            # Get final stats
            final_stats = embedder.get_stats()
            logger.info("✅ CartonCloud documentation vector store built successfully!")
            logger.info(f"📊 Total documents: {final_stats.get('document_count', 0)}")
            logger.info(f"📂 Vector store path: {final_stats.get('vectorstore_path')}")
            
            # Show breakdown by source type
            if include_local and final_stats.get("local_docs"):
                local_stats = final_stats["local_docs"]
                logger.info(f"📁 Local files: {local_stats.get('total_files', 0)}")
            
            if include_web and final_stats.get("web_docs"):
                web_stats = final_stats["web_docs"]
                logger.info(f"🌐 Web files: {web_stats.get('web_doc_files', 0)}")
        else:
            logger.error("❌ Failed to build vector store")
            
    except Exception as e:
        logger.error(f"❌ Error building vector store: {e}")
        raise


if __name__ == "__main__":
    main()
