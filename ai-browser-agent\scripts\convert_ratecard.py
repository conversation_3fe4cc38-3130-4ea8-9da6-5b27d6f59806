#!/usr/bin/env python3
"""Convert AUL Wholesale Service Costings Excel file to YAML rate card and Markdown.

- Reads /input/AUL Wholesale Service Costings.xlsx
- Extracts rows with valid service and price
- Outputs data/ratecards/als_2024-11-28.yml and docs/cartoncloud/als_ratecard.md
"""

import re
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
import yaml
import openpyxl

RATECARD_DATE = "2024-11-28"
CATEGORY_MAP = {
    "storage": ["storage", "pallet"],
    "handling": ["movement", "handling", "pick", "pack"],
    "transport": ["transport", "delivery", "freight"],
    "admin": ["admin", "management", "setup"],
}

class RateCardEntry:
    def __init__(
        self,
        id: str,
        service: str,
        price_ex_gst: Decimal,
        unit: str,
        min_charge: Optional[Decimal] = None,
        category: str = "general",
        effective_from: str = RATECARD_DATE,
        notes: Optional[str] = None,
    ):
        self.id = id
        self.service = service
        self.price_ex_gst = price_ex_gst
        self.unit = unit
        self.min_charge = min_charge
        self.category = category
        self.effective_from = effective_from
        self.notes = notes

    def to_dict(self) -> Dict[str, Any]:
        d = {
            "id": self.id,
            "service": self.service,
            "price_ex_gst": float(self.price_ex_gst),
            "unit": self.unit,
            "category": self.category,
            "effective_from": self.effective_from,
        }
        if self.min_charge is not None:
            d["min_charge"] = float(self.min_charge)
        if self.notes:
            d["notes"] = self.notes
        return d

def normalize_service_id(service_name: str) -> str:
    s = re.sub(r"[^\w\s-]", "", service_name.lower())
    s = re.sub(r"[\s-]+", "_", s)
    return s.strip("_")

def parse_price_string(price_str: str) -> Tuple[Decimal, str, Optional[Decimal], Optional[str]]:
    if not price_str or price_str.strip() in ["Price…", "Price", ""]:
        raise ValueError("Invalid or placeholder price string")
    price_str = price_str.strip()

    # Handle various invalid/placeholder values
    invalid_values = ["TBC", "TBA", "N/A", "na", "n/a", "-", ""]
    if price_str.lower() in [v.lower() for v in invalid_values]:
        raise ValueError(f"Invalid or placeholder price string: {price_str}")

    pat = r"\$?([\d,.]+)(?:\s+per\s+(.+?))?(?:\s*\(min\s*\$?([\d,.]+)\))?(.*)$"
    m = re.match(pat, price_str, re.IGNORECASE)
    if not m:
        price_match = re.search(r"\$?([\d,.]+)", price_str)
        if price_match:
            price_text = price_match.group(1).replace(",", "")
            # Additional validation for price text
            if not price_text or not re.match(r"^\d+\.?\d*$", price_text):
                raise ValueError(f"Invalid price format: {price_str}")
            price = Decimal(price_text)
            return price, "unit", None, price_str
        raise ValueError(f"Could not parse price from: {price_str}")

    price_text = m.group(1).replace(",", "")
    # Additional validation for price text
    if not price_text or not re.match(r"^\d+\.?\d*$", price_text):
        raise ValueError(f"Invalid price format: {price_str}")

    price = Decimal(price_text)
    unit = (m.group(2) or "unit").strip()
    unit_map = {
        "pallet per week": "pallet/week",
        "pallets per week": "pallet/week",
        "sqm/cbm per week": "sqm/week",
        "square metre per week": "sqm/week",
        "cubic metre per week": "cbm/week",
        "movement": "movement",
        "hour": "hour",
        "kg": "kg",
        "tonne": "tonne",
    }
    for old, new in unit_map.items():
        if old in unit.lower():
            unit = new
            break

    min_charge_text = m.group(3)
    min_charge = None
    if min_charge_text:
        min_charge_text = min_charge_text.replace(",", "")
        if re.match(r"^\d+\.?\d*$", min_charge_text):
            min_charge = Decimal(min_charge_text)

    notes = m.group(4).strip() if m.group(4) and m.group(4).strip() not in ["", "()", "per week"] else None
    return price, unit, min_charge, notes

def categorize_service(service_name: str) -> str:
    s = service_name.lower()
    for cat, keywords in CATEGORY_MAP.items():
        if any(word in s for word in keywords):
            return cat
    return "general"

def read_excel_file(file_path: Path) -> List[RateCardEntry]:
    wb = openpyxl.load_workbook(file_path, data_only=True)
    sheet = wb["ALS Pricebook"] if "ALS Pricebook" in wb.sheetnames else wb[wb.sheetnames[0]]
    entries = []
    header_row, service_col, price_col = None, None, None
    for row_idx, row in enumerate(sheet.iter_rows(min_row=1, max_row=20), 1):
        for col_idx, cell in enumerate(row):
            if cell.value and isinstance(cell.value, str):
                v = cell.value.strip().lower()
                if "service" in v or "description" in v:
                    header_row, service_col = row_idx, col_idx
                elif "price" in v or "cost" in v:
                    price_col = col_idx
        if header_row and service_col is not None and price_col is not None:
            break
    if not header_row:
        header_row, service_col, price_col = 1, 0, 1
    for row in sheet.iter_rows(min_row=header_row + 1):
        service_cell = row[service_col] if service_col < len(row) else None
        service_name = service_cell.value if service_cell else None
        if not service_name or str(service_name).strip() == "":
            continue
        service_name = str(service_name).strip()
        price_value = row[price_col].value if price_col < len(row) else None
        if not price_value:
            continue
        price_str = str(price_value).strip()
        if price_str in ["Price…", "Price", "", "TBC", "TBA"]:
            continue
        try:
            price, unit, min_charge, notes = parse_price_string(price_str)
        except ValueError as e:
            print(f"Skipping row with service '{service_name}' due to price parsing error: {e}")
            continue
        service_id = normalize_service_id(service_name)
        category = categorize_service(service_name)
        entries.append(RateCardEntry(
            id=service_id,
            service=service_name,
            price_ex_gst=price,
            unit=unit,
            min_charge=min_charge,
            category=category,
            notes=notes,
        ))
    return entries

def write_yaml_file(entries: List[RateCardEntry], output_path: Path) -> None:
    data = {
        "version": "1.0",
        "generated_at": datetime.now().isoformat(),
        "source": "AUL Wholesale Service Costings.xlsx",
        "effective_from": RATECARD_DATE,
        "currency": "AUD",
        "rate_cards": [e.to_dict() for e in entries],
    }
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        yaml.dump(data, f, default_flow_style=False, sort_keys=False, allow_unicode=True)

def write_markdown_file(entries: List[RateCardEntry], output_path: Path) -> None:
    output_path.parent.mkdir(parents=True, exist_ok=True)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(f"# ALS Rate Card - {RATECARD_DATE}\n\n")
        f.write("Auto-generated from AUL Wholesale Service Costings.xlsx\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        cats = {}
        for e in entries:
            cats.setdefault(e.category, []).append(e)
        for cat, cat_entries in cats.items():
            f.write(f"## {cat.title()}\n\n")
            f.write("| Service | Price (ex GST) | Unit | Min Charge | Notes |\n")
            f.write("|---------|----------------|------|------------|-------|\n")
            for e in sorted(cat_entries, key=lambda x: x.service):
                min_charge_str = f"${e.min_charge:.2f}" if e.min_charge else "-"
                notes_str = e.notes or "-"
                f.write(f"| {e.service} | ${e.price_ex_gst:.2f} | {e.unit} | {min_charge_str} | {notes_str} |\n")
            f.write("\n")
        f.write("## Service Index\n\n")
        for e in sorted(entries, key=lambda x: x.service):
            f.write(f"- **{e.service}**: ${e.price_ex_gst:.2f} per {e.unit}")
            if e.min_charge:
                f.write(f" (min ${e.min_charge:.2f})")
            f.write(f" - ID: `{e.id}`\n")

def main():
    project_root = Path(__file__).parent.parent
    input_file = project_root / "input" / "AUL Wholesale Service Costings.xlsx"
    yaml_output = project_root / "data" / "ratecards" / f"als_{RATECARD_DATE}.yml"
    markdown_output = project_root / "docs" / "cartoncloud" / "als_ratecard.md"

    if not input_file.exists():
        print(f"Input file not found: {input_file}", file=sys.stderr)
        print("Please place the 'AUL Wholesale Service Costings.xlsx' file in the input directory.", file=sys.stderr)
        return 1

    entries = read_excel_file(input_file)
    if not entries:
        print("No rate card entries extracted from Excel file", file=sys.stderr)
        return 1

    write_yaml_file(entries, yaml_output)
    write_markdown_file(entries, markdown_output)
    print(f"YAML output: {yaml_output}")
    print(f"Markdown output: {markdown_output}")
    print(f"Processed {len(entries)} rate card entries")
    print("\nNext steps:")
    print("1. Review the generated YAML and Markdown files")
    print("2. Use 'python scripts/upload_ratecard.py' to upload to CartonCloud")
    print("3. Use the rate card lookup tool for pricing calculations")
    return 0

if __name__ == "__main__":
    sys.exit(main())
