<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI Browser Agent{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/sse.js"></script>
    
    <style>
        .chat-message {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .sidebar {
            transition: transform 0.3s ease-in-out;
        }
        
        .streaming {
            border-left: 3px solid #3b82f6;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { border-color: #3b82f6; }
            50% { border-color: #60a5fa; }
        }
        
        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 h-screen overflow-hidden">
    {% block content %}{% endblock %}
    
    <script>
        // Auto-scroll chat to bottom
        function scrollToBottom() {
            const chatBody = document.getElementById('chat-body');
            if (chatBody) {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
        }
        
        // Scroll to bottom when new messages arrive
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            if (evt.target.id === 'chat-body' || evt.target.closest('#chat-body')) {
                setTimeout(scrollToBottom, 100);
            }
        });
        
        // Handle form submission
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            if (evt.target.id === 'message-form') {
                const input = document.getElementById('message-input');
                if (input.value.trim() === '') {
                    evt.preventDefault();
                    return;
                }
                // Clear input after sending
                setTimeout(() => input.value = '', 100);
            }
        });
        
        // Handle SSE connection
        document.body.addEventListener('htmx:sseOpen', function(evt) {
            console.log('SSE connection opened');
        });
        
        document.body.addEventListener('htmx:sseError', function(evt) {
            console.log('SSE connection error:', evt.detail);
        });
    </script>
</body>
</html>
