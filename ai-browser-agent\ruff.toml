target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "N",  # pep8-naming
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[per-file-ignores]
"tests/*" = ["E402"]  # Allow imports not at top of file in tests

[isort]
known-first-party = ["src"]
force-single-line = true

[mccabe]
max-complexity = 10
