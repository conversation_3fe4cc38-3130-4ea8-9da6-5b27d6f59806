{"version": 3, "file": "colorToUniform-DRYrsvow.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/colorToUniform.js"], "sourcesContent": ["const localUniformBit = {\n  name: \"local-uniform-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n\n            struct LocalUniforms {\n                uTransformMatrix:mat3x3<f32>,\n                uColor:vec4<f32>,\n                uRound:f32,\n            }\n\n            @group(1) @binding(0) var<uniform> localUniforms : LocalUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            vColor *= localUniforms.uColor;\n            modelMatrix *= localUniforms.uTransformMatrix;\n        `\n    ),\n    end: (\n      /* wgsl */\n      `\n            if(localUniforms.uRound == 1)\n            {\n                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);\n            }\n        `\n    )\n  }\n};\nconst localUniformBitGroup2 = {\n  ...localUniformBit,\n  vertex: {\n    ...localUniformBit.vertex,\n    // replace the group!\n    header: localUniformBit.vertex.header.replace(\"group(1)\", \"group(2)\")\n  }\n};\nconst localUniformBitGl = {\n  name: \"local-uniform-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n\n            uniform mat3 uTransformMatrix;\n            uniform vec4 uColor;\n            uniform float uRound;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            vColor *= uColor;\n            modelMatrix = uTransformMatrix;\n        `\n    ),\n    end: (\n      /* glsl */\n      `\n            if(uRound == 1.)\n            {\n                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\n            }\n        `\n    )\n  }\n};\nclass BatchableSprite {\n  constructor() {\n    this.batcherName = \"default\";\n    this.topology = \"triangle-list\";\n    this.attributeSize = 4;\n    this.indexSize = 6;\n    this.packAsQuad = true;\n    this.roundPixels = 0;\n    this._attributeStart = 0;\n    this._batcher = null;\n    this._batch = null;\n  }\n  get blendMode() {\n    return this.renderable.groupBlendMode;\n  }\n  get color() {\n    return this.renderable.groupColorAlpha;\n  }\n  reset() {\n    this.renderable = null;\n    this.texture = null;\n    this._batcher = null;\n    this._batch = null;\n    this.bounds = null;\n  }\n}\nfunction color32BitToUniform(abgr, out, offset) {\n  const alpha = (abgr >> 24 & 255) / 255;\n  out[offset++] = (abgr & 255) / 255 * alpha;\n  out[offset++] = (abgr >> 8 & 255) / 255 * alpha;\n  out[offset++] = (abgr >> 16 & 255) / 255 * alpha;\n  out[offset++] = alpha;\n}\nexport {\n  BatchableSprite as B,\n  localUniformBit as a,\n  localUniformBitGl as b,\n  color32BitToUniform as c,\n  localUniformBitGroup2 as l\n};\n"], "names": [], "mappings": "AAAK,MAAC,eAAe,GAAG;AACxB,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,GAAG;AACP;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE;AACG,MAAC,qBAAqB,GAAG;AAC9B,EAAE,GAAG,eAAe;AACpB,EAAE,MAAM,EAAE;AACV,IAAI,GAAG,eAAe,CAAC,MAAM;AAC7B;AACA,IAAI,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;AACzE,GAAG;AACH,EAAE;AACG,MAAC,iBAAiB,GAAG;AAC1B,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,GAAG;AACP;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;AACpC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;AAC3C,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,SAAS,mBAAmB,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE;AAChD,EAAE,MAAM,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;AACzC,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;AAC7C,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;AAClD,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;AACnD,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;AACxB;;;;"}