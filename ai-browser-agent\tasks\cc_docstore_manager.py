"""Simple task to rebuild CartonCloud documentation vector store."""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.tools.cc_docs_embedder import CartonCloudDocumentEmbedder
from src.tools.cc_docs_search import rebuild_cc_docstore

logger = logging.getLogger(__name__)


async def rebuild_docstore_task():
    """Rebuild the CartonCloud documentation vector store."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        logger.info("🚀 Starting CartonCloud documentation rebuild task")
        
        # Use the rebuild function from the search module
        result_message = await rebuild_cc_docstore()
        
        print("\n" + "="*60)
        print(result_message)
        print("="*60)
        
        logger.info("✅ Rebuild task completed")
        
    except Exception as e:
        logger.error(f"❌ Rebuild task failed: {e}")
        print(f"\nError: {e}")
        return False
    
    return True


def check_docstore_status():
    """Check the current status of the documentation vector store."""
    try:
        from src.tools.cc_docs_search import get_vectorstore_stats
        
        stats = get_vectorstore_stats()
        
        print("\n" + "="*60)
        print("📊 CARTONCLOUD DOCUMENTATION STATUS")
        print("="*60)
        
        status = stats.get("status", "unknown")
        
        if status == "available":
            print("✅ Vector store is available")
            print(f"📂 Path: {stats.get('vectorstore_path')}")
            print(f"📄 Documents: {stats.get('document_count', 0)}")
            
            if "local_docs" in stats:
                local_stats = stats["local_docs"]
                print(f"📁 Local files: {local_stats.get('total_files', 0)}")
                print(f"📁 Local directories found: {local_stats.get('directories_found', 0)}")
            
            if "web_docs" in stats:
                web_stats = stats["web_docs"]
                print(f"🌐 Web files: {web_stats.get('web_doc_files', 0)}")
        
        elif status == "not_found":
            print("❌ Vector store not found")
            print(f"📂 Expected path: {stats.get('path')}")
            print("💡 Run the rebuild task to create it")
        
        else:
            print(f"⚠️  Vector store status: {status}")
            if "error" in stats:
                print(f"❌ Error: {stats['error']}")
        
        print("="*60)
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")


async def main():
    """Main task function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="CartonCloud Documentation Tasks")
    parser.add_argument("action", choices=["rebuild", "status"], 
                       help="Action to perform")
    
    args = parser.parse_args()
    
    if args.action == "rebuild":
        success = await rebuild_docstore_task()
        sys.exit(0 if success else 1)
    elif args.action == "status":
        check_docstore_status()
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
