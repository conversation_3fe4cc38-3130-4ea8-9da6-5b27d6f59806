"""Build CartonCloud documentation vector store for RAG with local and web sources."""

import os
import asyncio
import logging
import click
from pathlib import Path
from typing import List, Optional, Set, Tuple
from urllib.parse import urljoin, urlparse, urlunparse
from urllib.robotparser import RobotFileParser
import requests
from bs4 import BeautifulSoup, Comment
import html2text
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.schema import Document
from datetime import datetime
import shutil
import time

# Import our enhanced tools
import sys
sys.path.append(str(Path(__file__).parent.parent))
from src.tools.cc_docs_embedder import CartonCloudDocumentEmbedder
from src.tools.cc_local_docs_loader import CartonCloudLocalDocsLoader

logger = logging.getLogger(__name__)


def load_url_list(file_path: str = "scripts/cc_doc_urls.txt") -> List[str]:
    """Load URLs from text file.
    
    Args:
        file_path: Path to text file containing URLs (one per line)
        
    Returns:
        List of URLs to process
    """
    urls_file = Path(file_path)
    if not urls_file.exists():
        logger.warning(f"URL list file not found: {file_path}")
        return []
    
    urls = []
    with open(urls_file, 'r', encoding='utf-8') as f:
        for line in f:
            url = line.strip()
            if url and not url.startswith('#'):  # Skip empty lines and comments
                urls.append(url)
    
    return urls


def clean_html_content(html_content: str) -> str:
    """Clean HTML content by removing navigation, footers, and converting to markdown.
    
    Args:
        html_content: Raw HTML content
        
    Returns:
        Cleaned markdown content
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.extract()
    
    # Remove comments
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()
    
    # Remove common navigation and footer elements
    for element in soup.find_all(['nav', 'header', 'footer']):
        element.extract()
    
    # Remove elements by class/id patterns
    selectors_to_remove = [
        '.navigation', '.nav', '.sidebar', '.footer', '.header',
        '#navigation', '#nav', '#sidebar', '#footer', '#header',
        '.breadcrumb', '.menu', '.social', '.advertisement'
    ]
    
    for selector in selectors_to_remove:
        for element in soup.select(selector):
            element.extract()
    
    # Convert to markdown
    h = html2text.HTML2Text()
    h.ignore_links = False
    h.ignore_images = False
    h.body_width = 0  # No line wrapping
    
    markdown_content = h.handle(str(soup))
    
    # Clean up excessive whitespace
    lines = markdown_content.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        line = line.strip()
        if not line:
            if not prev_empty:
                cleaned_lines.append('')
                prev_empty = True
        else:
            cleaned_lines.append(line)
            prev_empty = False
    
    return '\n'.join(cleaned_lines)


def download_and_save_page(url: str, output_dir: Path) -> Optional[str]:
    """Download a web page and save as markdown.
    
    Args:
        url: URL to download
        output_dir: Directory to save markdown files
        
    Returns:
        Path to saved file if successful, None otherwise
    """
    try:
        logger.info(f"Downloading: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Clean and convert content
        markdown_content = clean_html_content(response.text)
        
        # Generate filename from URL
        filename = url.replace('https://', '').replace('http://', '')
        filename = filename.replace('/', '_').replace('?', '_').replace('&', '_')
        filename = filename[:100] + '.md'  # Limit filename length
        
        # Add metadata header
        metadata = f"""---
source_url: {url}
downloaded_at: {datetime.now().isoformat()}
---

"""
        
        content_with_metadata = metadata + markdown_content
        
        # Save to file
        file_path = output_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content_with_metadata)
        
        logger.info(f"Saved: {file_path}")
        return str(file_path)
        
    except Exception as e:
        logger.error(f"Error downloading {url}: {e}")
        return None


def chunk_documents(
    docs_dir: Path,
    chunk_size: int = 1000,
    chunk_overlap: int = 200
) -> List[Document]:
    """Split markdown documents into chunks for embedding.
    
    Args:
        docs_dir: Directory containing markdown files
        chunk_size: Target chunk size in tokens
        chunk_overlap: Overlap between chunks
        
    Returns:
        List of Document objects with chunks
    """
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        separators=["\n\n", "\n", " ", ""]
    )
    
    documents = []
    
    for md_file in docs_dir.glob("*.md"):
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract metadata from frontmatter
            metadata = {"source": str(md_file)}
            if content.startswith("---"):
                end_frontmatter = content.find("---", 3)
                if end_frontmatter != -1:
                    frontmatter = content[3:end_frontmatter]
                    content = content[end_frontmatter + 3:].strip()
                    
                    # Parse simple frontmatter
                    for line in frontmatter.strip().split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            metadata[key.strip()] = value.strip()
            
            # Split into chunks
            chunks = text_splitter.split_text(content)
            
            for i, chunk in enumerate(chunks):
                doc_metadata = metadata.copy()
                doc_metadata["chunk_index"] = i
                doc_metadata["total_chunks"] = len(chunks)
                
                documents.append(Document(
                    page_content=chunk,
                    metadata=doc_metadata
                ))
                
        except Exception as e:
            logger.error(f"Error processing {md_file}: {e}")
    
    return documents


def build_vector_store(
    documents: List[Document],
    persist_directory: str,
    embedding_model: str = "text-embedding-3-small"
) -> Chroma:
    """Build and persist Chroma vector store.
    
    Args:
        documents: List of Document objects to embed
        persist_directory: Directory to persist the vector store
        embedding_model: OpenAI embedding model to use
        
    Returns:
        Chroma vector store instance
    """
    logger.info(f"Building vector store with {len(documents)} documents")
    
    # Initialize embeddings
    embeddings = OpenAIEmbeddings(model=embedding_model)
    
    # Create persist directory if it doesn't exist
    Path(persist_directory).mkdir(parents=True, exist_ok=True)
    
    # Build vector store
    vectorstore = Chroma.from_documents(
        documents=documents,
        embedding=embeddings,
        persist_directory=persist_directory,
        collection_name="cartoncloud_docs"
    )
    
    logger.info(f"Vector store built and persisted to {persist_directory}")
    return vectorstore


async def main():
    """Enhanced main function to build CartonCloud documentation vector store."""
    parser = argparse.ArgumentParser(description="Build CartonCloud documentation vector store")
    parser.add_argument("--sources", choices=["web", "local", "both"], default="both",
                       help="Which sources to include (default: both)")
    parser.add_argument("--force-rebuild", action="store_true",
                       help="Force rebuild of existing vector store")
    parser.add_argument("--vectorstore-path", type=str,
                       help="Custom path for vector store")
    parser.add_argument("--local-docs-dirs", nargs="*",
                       help="Custom directories for local documents")
    
    args = parser.parse_args()
    
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Determine which sources to include
        include_web = args.sources in ["web", "both"]
        include_local = args.sources in ["local", "both"]
        
        logger.info(f"Building CartonCloud docstore with sources: {args.sources}")
        logger.info(f"Web docs: {'✅' if include_web else '❌'}")
        logger.info(f"Local docs: {'✅' if include_local else '❌'}")
        
        # If web docs are requested, run the original web scraping first
        if include_web:
            logger.info("Starting web document collection...")
            await build_web_docs()
        
        # Initialize the enhanced embedder
        embedder = CartonCloudDocumentEmbedder(
            vectorstore_path=args.vectorstore_path,
            include_local_docs=include_local,
            include_web_docs=include_web,
            local_docs_dirs=args.local_docs_dirs
        )
        
        # Get initial stats
        stats = embedder.get_stats()
        logger.info("Pre-build statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # Build the vector store
        success = embedder.rebuild_vectorstore() if args.force_rebuild else embedder.build_vectorstore()
        
        if success:
            # Get final stats
            final_stats = embedder.get_stats()
            logger.info("✅ CartonCloud documentation vector store built successfully!")
            logger.info(f"📊 Total documents: {final_stats.get('document_count', 0)}")
            logger.info(f"📂 Vector store path: {final_stats.get('vectorstore_path')}")
            
            # Show breakdown by source type
            if include_local and final_stats.get("local_docs"):
                local_stats = final_stats["local_docs"]
                logger.info(f"📁 Local files: {local_stats.get('total_files', 0)}")
            
            if include_web and final_stats.get("web_docs"):
                web_stats = final_stats["web_docs"]
                logger.info(f"🌐 Web files: {web_stats.get('web_doc_files', 0)}")
        else:
            logger.error("❌ Failed to build vector store")
            
    except Exception as e:
        logger.error(f"❌ Error building vector store: {e}")
        raise


async def build_web_docs():
    """Build web documentation using the original logic."""
    # Configuration
    urls_file = "scripts/cc_doc_urls.txt"
    docs_dir = Path("docs/cartoncloud")
    
    # Create directories
    docs_dir.mkdir(parents=True, exist_ok=True)
    
    # Load URLs
    urls = load_url_list(urls_file)
    if not urls:
        logger.error(f"No URLs found in {urls_file}")
        return
    
    logger.info(f"Processing {len(urls)} URLs")
    
    # Download and save pages
    downloaded_files = []
    for url in urls:
        file_path = download_and_save_page(url, docs_dir)
        if file_path:
            downloaded_files.append(file_path)
    
    logger.info(f"Downloaded {len(downloaded_files)} web pages successfully")


if __name__ == "__main__":
    asyncio.run(main())
