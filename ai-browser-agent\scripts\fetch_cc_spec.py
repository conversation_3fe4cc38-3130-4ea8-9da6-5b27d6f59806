"""Fetch CartonCloud API specification from Postman collection and convert to OpenAPI stub."""

import os
import json
import yaml
import click
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PostmanToOpenAPIConverter:
    """Convert Postman collection to OpenAPI specification stub."""
    
    def __init__(self):
        self.openapi_spec = {
            "openapi": "3.0.3",
            "info": {
                "title": "CartonCloud API",
                "version": "1.0.0",
                "description": "CartonCloud REST API - Auto-generated from Postman collection"
            },
            "servers": [
                {"url": "https://api.cartoncloud.com", "description": "Production server"}
            ],
            "components": {
                "securitySchemes": {
                    "oauth2": {
                        "type": "oauth2",
                        "flows": {
                            "clientCredentials": {
                                "tokenUrl": "https://api.cartoncloud.com/uaa/oauth/token",
                                "scopes": {}
                            }
                        }
                    }
                },
                "schemas": {},
                "parameters": {
                    "AcceptVersion": {
                        "name": "Accept-Version",
                        "in": "header",
                        "required": True,
                        "schema": {"type": "string", "enum": ["1"]},
                        "description": "API version (must be '1')"
                    }
                }
            },
            "security": [{"oauth2": []}],
            "paths": {}
        }
    
    def _extract_path_parameters(self, url: str) -> tuple[str, List[str]]:
        """Extract path parameters from Postman URL variables."""
        # Convert {{variable}} to {variable} and track parameters
        parameters = []
        clean_url = url
        
        import re
        param_pattern = r'{{([^}]+)}}'
        matches = re.findall(param_pattern, url)
        
        for match in matches:
            param_name = match.strip()
            parameters.append(param_name)
            clean_url = clean_url.replace('{{' + match + '}}', '{' + param_name + '}')
        
        return clean_url, parameters
    
    def _generate_schema_from_body(self, body: Dict[str, Any]) -> Dict[str, Any]:
        """Generate JSON schema from request body example."""
        if not body or body.get('mode') != 'raw':
            return {}
        
        try:
            raw_data = body.get('raw', '{}')
            if raw_data:
                example_data = json.loads(raw_data)
                return self._infer_schema_from_data(example_data)
        except json.JSONDecodeError:
            pass
        
        return {"type": "object"}
    
    def _infer_schema_from_data(self, data: Any) -> Dict[str, Any]:
        """Infer JSON schema from data structure."""
        if isinstance(data, dict):
            properties = {}
            for key, value in data.items():
                properties[key] = self._infer_schema_from_data(value)
            return {
                "type": "object",
                "properties": properties
            }
        elif isinstance(data, list):
            if data:
                return {
                    "type": "array",
                    "items": self._infer_schema_from_data(data[0])
                }
            return {"type": "array", "items": {}}
        elif isinstance(data, str):
            return {"type": "string", "example": data}
        elif isinstance(data, bool):
            return {"type": "boolean", "example": data}
        elif isinstance(data, (int, float)):
            return {"type": "number", "example": data}
        else:
            return {"type": "string"}
    
    def _process_postman_item(self, item: Dict[str, Any], path_prefix: str = "") -> None:
        """Process a Postman collection item (request or folder)."""
        if 'item' in item:  # It's a folder
            folder_name = item.get('name', 'unknown')
            new_prefix = f"{path_prefix}/{folder_name.lower().replace(' ', '-')}"
            for sub_item in item['item']:
                self._process_postman_item(sub_item, new_prefix)
        elif 'request' in item:  # It's a request
            self._process_postman_request(item, path_prefix)
    
    def _process_postman_request(self, item: Dict[str, Any], path_prefix: str) -> None:
        """Process a single Postman request."""
        request = item['request']
        name = item.get('name', 'unknown')
        
        # Extract method and URL
        method = request.get('method', 'GET').lower()
        url_data = request.get('url', {})
        
        if isinstance(url_data, str):
            raw_url = url_data
        else:
            raw_url = url_data.get('raw', '')
        
        if not raw_url or not raw_url.startswith('{{base_url}}'):
            return
        
        # Clean up the path
        path = raw_url.replace('{{base_url}}', '').replace('{{baseUrl}}', '')
        if not path.startswith('/'):
            path = '/' + path
        
        # Extract path parameters
        clean_path, path_params = self._extract_path_parameters(path)
        
        # Build OpenAPI operation
        operation = {
            "summary": name,
            "description": item.get('description', f"CartonCloud API: {name}"),
            "parameters": [
                {"$ref": "#/components/parameters/AcceptVersion"}
            ],
            "responses": {
                "200": {
                    "description": "Successful response",
                    "content": {
                        "application/json": {
                            "schema": {"type": "object"}
                        }
                    }
                },
                "400": {"description": "Bad Request"},
                "401": {"description": "Unauthorized"},
                "404": {"description": "Not Found"},
                "500": {"description": "Internal Server Error"}
            },
            "tags": [path_prefix.strip('/') or 'general']
        }
        
        # Add path parameters
        for param in path_params:
            operation["parameters"].append({
                "name": param,
                "in": "path",
                "required": True,
                "schema": {"type": "string"},
                "description": f"Path parameter: {param}"
            })
        
        # Add query parameters if present
        url_params = url_data.get('query', []) if isinstance(url_data, dict) else []
        for param in url_params:
            param_name = param.get('key')
            if param_name:
                operation["parameters"].append({
                    "name": param_name,
                    "in": "query",
                    "required": False,
                    "schema": {"type": "string"},
                    "description": param.get('description', f"Query parameter: {param_name}")
                })
        
        # Add request body for POST/PUT/PATCH
        if method in ['post', 'put', 'patch'] and 'body' in request:
            body_schema = self._generate_schema_from_body(request['body'])
            if body_schema:
                operation["requestBody"] = {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": body_schema
                        }
                    }
                }
        
        # Add to paths
        if clean_path not in self.openapi_spec["paths"]:
            self.openapi_spec["paths"][clean_path] = {}
        
        self.openapi_spec["paths"][clean_path][method] = operation
    
    def convert(self, postman_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Postman collection to OpenAPI specification."""
        logger.info("Converting Postman collection to OpenAPI specification...")
        
        # Update info from Postman collection
        info = postman_data.get('info', {})
        if 'name' in info:
            self.openapi_spec["info"]["title"] = info['name']
        if 'description' in info:
            self.openapi_spec["info"]["description"] = info['description']
        
        # Process all items
        items = postman_data.get('item', [])
        for item in items:
            self._process_postman_item(item)
        
        logger.info(f"Generated OpenAPI spec with {len(self.openapi_spec['paths'])} paths")
        return self.openapi_spec


@click.command()
@click.option('--url', 
              default='https://api-docs.cartoncloud.com/postman/collection.json',
              help='URL to Postman collection JSON')
@click.option('--output-dir',
              default='.cache/cartoncloud_spec',
              help='Output directory for spec files')
@click.option('--force', is_flag=True, help='Force re-download even if cached')
def main(url: str, output_dir: str, force: bool):
    """Fetch CartonCloud API specification from Postman collection."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    postman_file = output_path / "postman_v1.json"
    openapi_file = output_path / "openapi_stub.yml"
    
    try:
        # Download Postman collection if needed
        if force or not postman_file.exists():
            logger.info(f"📥 Downloading Postman collection from: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Save raw Postman collection
            with open(postman_file, 'w', encoding='utf-8') as f:
                json.dump(response.json(), f, indent=2)
            
            logger.info(f"✅ Saved Postman collection to: {postman_file}")
        else:
            logger.info(f"📄 Using cached Postman collection: {postman_file}")
        
        # Load Postman collection
        with open(postman_file, 'r', encoding='utf-8') as f:
            postman_data = json.load(f)
        
        # Convert to OpenAPI
        converter = PostmanToOpenAPIConverter()
        openapi_spec = converter.convert(postman_data)
        
        # Save OpenAPI specification
        with open(openapi_file, 'w', encoding='utf-8') as f:
            yaml.dump(openapi_spec, f, default_flow_style=False, sort_keys=False)
        
        logger.info(f"✅ Generated OpenAPI specification: {openapi_file}")
        
        # Display summary
        paths_count = len(openapi_spec.get('paths', {}))
        methods_count = sum(len(methods) for methods in openapi_spec.get('paths', {}).values())
        
        logger.info("📊 API Specification Summary:")
        logger.info(f"  • Endpoints: {paths_count}")
        logger.info(f"  • HTTP methods: {methods_count}")
        logger.info(f"  • Security: OAuth2 Client Credentials")
        logger.info(f"  • Required header: Accept-Version: 1")
        
        # List some example endpoints
        example_paths = list(openapi_spec.get('paths', {}).keys())[:5]
        if example_paths:
            logger.info("📋 Example endpoints:")
            for path in example_paths:
                methods = list(openapi_spec['paths'][path].keys())
                logger.info(f"  • {path} ({', '.join(methods).upper()})")
        
        logger.info(f"🎉 CartonCloud API specification ready for client generation!")
        
    except requests.RequestException as e:
        logger.error(f"❌ Failed to download Postman collection: {e}")
        raise
    except Exception as e:
        logger.error(f"❌ Error processing specification: {e}")
        raise


if __name__ == "__main__":
    main()
