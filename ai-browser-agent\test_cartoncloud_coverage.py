"""
Comprehensive CartonCloud Knowledge Coverage Test

This tests that the agent has 100% coverage of CartonCloud concepts
and can properly handle all business scenarios.
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CartonCloudCoverageTest:
    """Test comprehensive CartonCloud knowledge coverage."""
    
    def __init__(self):
        self.test_results = []
        
    def test_concept_coverage(self):
        """Test that all CartonCloud concepts are covered."""
        test_result = {
            "test_name": "concept_coverage",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "concepts_tested": []
        }
        
        try:
            from src.knowledge.cartoncloud_knowledge import cartoncloud_kb
            
            # Get all concepts
            all_concepts = cartoncloud_kb.get_all_concepts()
            
            # Expected core concepts
            expected_concepts = [
                "rate_card", "customer", "storage_charge", "purchase_order",
                "sales_order", "invoice", "bulk_charges_report", "warehouse"
            ]
            
            found_concepts = [concept.name.lower().replace(" ", "_") for concept in all_concepts]
            
            for expected in expected_concepts:
                if expected in found_concepts:
                    test_result["concepts_tested"].append(f"✅ {expected}")
                else:
                    test_result["errors"].append(f"Missing concept: {expected}")
            
            # Test concept completeness
            for concept in all_concepts:
                if not concept.description:
                    test_result["errors"].append(f"{concept.name}: Missing description")
                if not concept.operations:
                    test_result["errors"].append(f"{concept.name}: Missing operations")
                if not concept.api_endpoints:
                    test_result["errors"].append(f"{concept.name}: Missing API endpoints")
            
            coverage_percentage = len(test_result["concepts_tested"]) / len(expected_concepts) * 100
            test_result["coverage_percentage"] = coverage_percentage
            test_result["status"] = "passed" if coverage_percentage >= 100 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Concept coverage test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def test_operation_understanding(self):
        """Test that the agent understands different operation types."""
        test_result = {
            "test_name": "operation_understanding",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "operations_tested": []
        }
        
        try:
            from src.knowledge.cartoncloud_knowledge import cartoncloud_kb
            
            # Test different operation types
            test_queries = [
                ("Look up ALS rate card", "search"),
                ("Create new customer record", "create"),
                ("Update rate card pricing", "update"),
                ("Delete old purchase order", "delete"),
                ("Generate bulk charges report", "report"),
                ("Show customer details", "read")
            ]
            
            for query, expected_operation in test_queries:
                analysis = cartoncloud_kb.analyze_request(query)
                detected_operation = analysis["operation_type"].value
                
                if detected_operation == expected_operation:
                    test_result["operations_tested"].append(f"✅ {query} -> {detected_operation}")
                else:
                    test_result["errors"].append(f"{query}: Expected {expected_operation}, got {detected_operation}")
            
            accuracy = len(test_result["operations_tested"]) / len(test_queries) * 100
            test_result["accuracy_percentage"] = accuracy
            test_result["status"] = "passed" if accuracy >= 80 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Operation understanding test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def test_approval_workflow(self):
        """Test that approval workflows are correctly identified."""
        test_result = {
            "test_name": "approval_workflow",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "approval_tests": []
        }
        
        try:
            from src.knowledge.cartoncloud_knowledge import cartoncloud_kb
            
            # Test approval requirements
            approval_tests = [
                ("Look up customer details", False),  # Read operation - no approval
                ("Update rate card pricing", True),   # Update operation - needs approval
                ("Create new customer", False),       # Create customer - confirmation only
                ("Delete rate card", True),          # Delete operation - needs approval
                ("Generate report", False),          # Report generation - no approval
                ("Modify invoice", True),            # Invoice modification - needs approval
            ]
            
            for query, should_need_approval in approval_tests:
                analysis = cartoncloud_kb.analyze_request(query)
                needs_approval = analysis["approval_needed"]
                
                if needs_approval == should_need_approval:
                    test_result["approval_tests"].append(f"✅ {query}: {'Approval required' if needs_approval else 'No approval needed'}")
                else:
                    test_result["errors"].append(f"{query}: Expected approval={should_need_approval}, got {needs_approval}")
            
            accuracy = len(test_result["approval_tests"]) / len(approval_tests) * 100
            test_result["accuracy_percentage"] = accuracy
            test_result["status"] = "passed" if accuracy >= 80 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Approval workflow test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def test_business_scenarios(self):
        """Test comprehensive business scenarios."""
        test_result = {
            "test_name": "business_scenarios",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "scenarios_tested": []
        }
        
        try:
            from gradio_ui import send_message_sync
            
            # Comprehensive business scenarios
            scenarios = [
                "Look up the rate card for ALS customer",
                "Check storage charges for customer XYZ for last month",
                "Update rate card pricing for customer ABC - increase storage to $50",
                "Create a new purchase order for supplier DEF",
                "Generate bulk charges report for January 2024",
                "Simulate invoice for customer GHI",
                "Find all customers with active rate cards",
                "Show warehouse capacity and utilization",
                "Delete old purchase order PO-2023-001",
                "Update customer contact information for JKL Corp"
            ]
            
            for scenario in scenarios:
                try:
                    result = send_message_sync(scenario, "")
                    response = result[0]
                    
                    # Check response quality
                    if len(response) > 200:  # Substantial response
                        test_result["scenarios_tested"].append(f"✅ {scenario[:40]}...")
                        
                        # Check for CartonCloud concepts
                        cc_keywords = ["rate card", "customer", "storage", "charges", "invoice", "warehouse"]
                        if any(keyword in response.lower() for keyword in cc_keywords):
                            test_result["scenarios_tested"].append(f"   ✅ Contains CartonCloud concepts")
                        
                        # Check for approval workflow
                        if any(word in scenario.lower() for word in ["update", "delete", "modify"]):
                            if "approval" in response.lower() or "confirm" in response.lower():
                                test_result["scenarios_tested"].append(f"   ✅ Correctly identifies approval needed")
                    else:
                        test_result["errors"].append(f"Insufficient response for: {scenario[:40]}...")
                        
                except Exception as e:
                    test_result["errors"].append(f"Scenario error: {scenario[:40]}... - {str(e)}")
            
            success_rate = len([t for t in test_result["scenarios_tested"] if t.startswith("✅")]) / len(scenarios) * 100
            test_result["success_rate"] = success_rate
            test_result["status"] = "passed" if success_rate >= 80 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Business scenarios test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def test_edge_cases_cartoncloud(self):
        """Test CartonCloud-specific edge cases."""
        test_result = {
            "test_name": "edge_cases_cartoncloud",
            "status": "running",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "edge_cases_tested": []
        }
        
        try:
            from src.knowledge.cartoncloud_knowledge import cartoncloud_kb
            
            # CartonCloud-specific edge cases
            edge_cases = [
                "rate card with no customer",
                "invoice for non-existent customer",
                "storage charges for empty warehouse",
                "purchase order with negative quantities",
                "rate card with past effective date",
                "customer with duplicate code",
                "warehouse with zero capacity",
                "charges with invalid currency"
            ]
            
            for edge_case in edge_cases:
                try:
                    analysis = cartoncloud_kb.analyze_request(edge_case)
                    
                    # Any analysis without crashing is good for edge cases
                    if analysis and "matching_concepts" in analysis:
                        test_result["edge_cases_tested"].append(f"✅ {edge_case}")
                    else:
                        test_result["errors"].append(f"Failed to analyze: {edge_case}")
                        
                except Exception as e:
                    # Some edge cases might cause exceptions, which is acceptable
                    test_result["edge_cases_tested"].append(f"⚠️ {edge_case}: {str(e)[:50]}...")
            
            handling_rate = len(test_result["edge_cases_tested"]) / len(edge_cases) * 100
            test_result["handling_rate"] = handling_rate
            test_result["status"] = "passed" if handling_rate >= 70 else "partial"
            
        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Edge cases test failed: {str(e)}")
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result
    
    def run_all_tests(self):
        """Run all CartonCloud coverage tests."""
        print("🚀 Starting Comprehensive CartonCloud Knowledge Coverage Tests")
        print("=" * 70)
        
        tests = [
            ("Concept Coverage", self.test_concept_coverage),
            ("Operation Understanding", self.test_operation_understanding),
            ("Approval Workflow", self.test_approval_workflow),
            ("Business Scenarios", self.test_business_scenarios),
            ("Edge Cases", self.test_edge_cases_cartoncloud),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name}...")
            result = test_func()
            self.print_test_result(test_name, result)
        
        self.generate_coverage_report()
    
    def print_test_result(self, test_name: str, result: dict):
        """Print test result."""
        status = result["status"]
        icon = "✅" if status == "passed" else "⚠️" if status == "partial" else "❌"
        
        print(f"  {icon} {test_name}: {status.upper()}")
        
        # Show specific metrics
        if "coverage_percentage" in result:
            print(f"    📊 Coverage: {result['coverage_percentage']:.1f}%")
        if "accuracy_percentage" in result:
            print(f"    📊 Accuracy: {result['accuracy_percentage']:.1f}%")
        if "success_rate" in result:
            print(f"    📊 Success Rate: {result['success_rate']:.1f}%")
        if "handling_rate" in result:
            print(f"    📊 Handling Rate: {result['handling_rate']:.1f}%")
        
        # Show some results
        for key in ["concepts_tested", "operations_tested", "approval_tests", "scenarios_tested", "edge_cases_tested"]:
            if key in result and result[key]:
                for item in result[key][:2]:  # Show first 2 items
                    print(f"    {item}")
        
        if result.get("errors"):
            for error in result["errors"][:2]:  # Show first 2 errors
                print(f"    ⚠️ {error}")
    
    def generate_coverage_report(self):
        """Generate comprehensive coverage report."""
        passed = len([r for r in self.test_results if r["status"] == "passed"])
        partial = len([r for r in self.test_results if r["status"] == "partial"])
        failed = len([r for r in self.test_results if r["status"] == "failed"])
        total = len(self.test_results)
        
        print(f"\n{'='*70}")
        print("CARTONCLOUD KNOWLEDGE COVERAGE REPORT")
        print(f"{'='*70}")
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Partial: {partial}")
        print(f"Failed: {failed}")
        
        if total > 0:
            coverage_score = (passed + partial * 0.7) / total * 100
            print(f"Overall Coverage Score: {coverage_score:.1f}%")
        
        # Assessment
        if coverage_score >= 95:
            assessment = "EXCELLENT - 100% CartonCloud Coverage Achieved"
            status_icon = "🎉"
        elif coverage_score >= 85:
            assessment = "VERY GOOD - Near Complete Coverage"
            status_icon = "✅"
        elif coverage_score >= 70:
            assessment = "GOOD - Solid Coverage with Minor Gaps"
            status_icon = "⚠️"
        else:
            assessment = "NEEDS IMPROVEMENT - Significant Gaps"
            status_icon = "❌"
        
        print(f"\n{status_icon} Assessment: {assessment}")
        
        # Save detailed report
        report_path = Path("test_reports") / f"cartoncloud_coverage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump({
                "summary": {
                    "total": total,
                    "passed": passed,
                    "partial": partial,
                    "failed": failed,
                    "coverage_score": coverage_score,
                    "assessment": assessment
                },
                "results": self.test_results,
                "timestamp": datetime.now().isoformat()
            }, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_path}")
        print(f"{'='*70}")
        
        return coverage_score >= 95


def main():
    """Main entry point."""
    tester = CartonCloudCoverageTest()
    success = tester.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
