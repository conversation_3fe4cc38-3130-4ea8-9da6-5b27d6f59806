{"version": 3, "file": "AudioPlayer-B5Sg8J5z.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/AudioPlayer.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute, add_styles, escape } from \"svelte/internal\";\nimport { onMount, createEventDispatcher } from \"svelte\";\nimport { ac as VolumeMuted, ad as VolumeLow, ae as VolumeHigh, af as Backward, a9 as Pause, H as Play, ag as Forward, _ as Undo, aa as Trim, k as Empty, M as Music } from \"./client.js\";\nimport { f as format_time } from \"./utils.js\";\nimport { r as resolve_wasm_src } from \"./DownloadLink.js\";\nimport \"./hls.js\";\nvar __awaiter$3 = function(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function(resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function(resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nfunction decode(audioData, sampleRate) {\n  return __awaiter$3(this, void 0, void 0, function* () {\n    const audioCtx = new AudioContext({ sampleRate });\n    const decode2 = audioCtx.decodeAudioData(audioData);\n    return decode2.finally(() => audioCtx.close());\n  });\n}\nfunction normalize(channelData) {\n  const firstChannel = channelData[0];\n  if (firstChannel.some((n2) => n2 > 1 || n2 < -1)) {\n    const length = firstChannel.length;\n    let max = 0;\n    for (let i2 = 0; i2 < length; i2++) {\n      const absN = Math.abs(firstChannel[i2]);\n      if (absN > max)\n        max = absN;\n    }\n    for (const channel of channelData) {\n      for (let i2 = 0; i2 < length; i2++) {\n        channel[i2] /= max;\n      }\n    }\n  }\n  return channelData;\n}\nfunction createBuffer(channelData, duration) {\n  if (typeof channelData[0] === \"number\")\n    channelData = [channelData];\n  normalize(channelData);\n  return {\n    duration,\n    length: channelData[0].length,\n    sampleRate: channelData[0].length / duration,\n    numberOfChannels: channelData.length,\n    getChannelData: (i2) => channelData === null || channelData === void 0 ? void 0 : channelData[i2],\n    copyFromChannel: AudioBuffer.prototype.copyFromChannel,\n    copyToChannel: AudioBuffer.prototype.copyToChannel\n  };\n}\nconst Decoder = {\n  decode,\n  createBuffer\n};\nvar __awaiter$2 = function(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function(resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function(resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nfunction fetchBlob(url, progressCallback, requestInit) {\n  var _a, _b;\n  return __awaiter$2(this, void 0, void 0, function* () {\n    const response = yield fetch(url, requestInit);\n    {\n      const reader = (_a = response.clone().body) === null || _a === void 0 ? void 0 : _a.getReader();\n      const contentLength = Number((_b = response.headers) === null || _b === void 0 ? void 0 : _b.get(\"Content-Length\"));\n      let receivedLength = 0;\n      const processChunk = (done, value) => __awaiter$2(this, void 0, void 0, function* () {\n        if (done)\n          return;\n        receivedLength += (value === null || value === void 0 ? void 0 : value.length) || 0;\n        const percentage = Math.round(receivedLength / contentLength * 100);\n        progressCallback(percentage);\n        return reader === null || reader === void 0 ? void 0 : reader.read().then(({ done: done2, value: value2 }) => processChunk(done2, value2));\n      });\n      reader === null || reader === void 0 ? void 0 : reader.read().then(({ done, value }) => processChunk(done, value));\n    }\n    return response.blob();\n  });\n}\nconst Fetcher = {\n  fetchBlob\n};\nclass EventEmitter {\n  constructor() {\n    this.listeners = {};\n    this.on = this.addEventListener;\n    this.un = this.removeEventListener;\n  }\n  /** Add an event listener */\n  addEventListener(event, listener, options) {\n    if (!this.listeners[event]) {\n      this.listeners[event] = /* @__PURE__ */ new Set();\n    }\n    this.listeners[event].add(listener);\n    if (options === null || options === void 0 ? void 0 : options.once) {\n      const unsubscribeOnce = () => {\n        this.removeEventListener(event, unsubscribeOnce);\n        this.removeEventListener(event, listener);\n      };\n      this.addEventListener(event, unsubscribeOnce);\n      return unsubscribeOnce;\n    }\n    return () => this.removeEventListener(event, listener);\n  }\n  removeEventListener(event, listener) {\n    var _a;\n    (_a = this.listeners[event]) === null || _a === void 0 ? void 0 : _a.delete(listener);\n  }\n  /** Subscribe to an event only once */\n  once(event, listener) {\n    return this.on(event, listener, { once: true });\n  }\n  /** Clear all events */\n  unAll() {\n    this.listeners = {};\n  }\n  /** Emit an event */\n  emit(eventName, ...args) {\n    if (this.listeners[eventName]) {\n      this.listeners[eventName].forEach((listener) => listener(...args));\n    }\n  }\n}\nclass Player extends EventEmitter {\n  constructor(options) {\n    super();\n    this.isExternalMedia = false;\n    if (options.media) {\n      this.media = options.media;\n      this.isExternalMedia = true;\n    } else {\n      this.media = document.createElement(\"audio\");\n    }\n    if (options.mediaControls) {\n      this.media.controls = true;\n    }\n    if (options.autoplay) {\n      this.media.autoplay = true;\n    }\n    if (options.playbackRate != null) {\n      this.onceMediaEvent(\"canplay\", () => {\n        if (options.playbackRate != null) {\n          this.media.playbackRate = options.playbackRate;\n        }\n      });\n    }\n  }\n  onMediaEvent(event, callback, options) {\n    this.media.addEventListener(event, callback, options);\n    return () => this.media.removeEventListener(event, callback);\n  }\n  onceMediaEvent(event, callback) {\n    return this.onMediaEvent(event, callback, { once: true });\n  }\n  getSrc() {\n    return this.media.currentSrc || this.media.src || \"\";\n  }\n  revokeSrc() {\n    const src = this.getSrc();\n    if (src.startsWith(\"blob:\")) {\n      URL.revokeObjectURL(src);\n    }\n  }\n  setSrc(url, blob) {\n    const src = this.getSrc();\n    if (src === url)\n      return;\n    this.revokeSrc();\n    const newSrc = blob instanceof Blob ? URL.createObjectURL(blob) : url;\n    this.media.src = newSrc;\n    this.media.load();\n  }\n  destroy() {\n    this.media.pause();\n    if (this.isExternalMedia)\n      return;\n    this.media.remove();\n    this.revokeSrc();\n    this.media.src = \"\";\n    this.media.load();\n  }\n  setMediaElement(element) {\n    this.media = element;\n  }\n  /** Start playing the audio */\n  play() {\n    return this.media.play();\n  }\n  /** Pause the audio */\n  pause() {\n    this.media.pause();\n  }\n  /** Check if the audio is playing */\n  isPlaying() {\n    return !this.media.paused && !this.media.ended;\n  }\n  /** Jumpt to a specific time in the audio (in seconds) */\n  setTime(time) {\n    this.media.currentTime = time;\n  }\n  /** Get the duration of the audio in seconds */\n  getDuration() {\n    return this.media.duration;\n  }\n  /** Get the current audio position in seconds */\n  getCurrentTime() {\n    return this.media.currentTime;\n  }\n  /** Get the audio volume */\n  getVolume() {\n    return this.media.volume;\n  }\n  /** Set the audio volume */\n  setVolume(volume) {\n    this.media.volume = volume;\n  }\n  /** Get the audio muted state */\n  getMuted() {\n    return this.media.muted;\n  }\n  /** Mute or unmute the audio */\n  setMuted(muted) {\n    this.media.muted = muted;\n  }\n  /** Get the playback speed */\n  getPlaybackRate() {\n    return this.media.playbackRate;\n  }\n  /** Set the playback speed, pass an optional false to NOT preserve the pitch */\n  setPlaybackRate(rate, preservePitch) {\n    if (preservePitch != null) {\n      this.media.preservesPitch = preservePitch;\n    }\n    this.media.playbackRate = rate;\n  }\n  /** Get the HTML media element */\n  getMediaElement() {\n    return this.media;\n  }\n  /** Set a sink id to change the audio output device */\n  setSinkId(sinkId) {\n    const media = this.media;\n    return media.setSinkId(sinkId);\n  }\n}\nfunction makeDraggable(element, onDrag, onStart, onEnd, threshold = 5) {\n  let unsub = () => {\n    return;\n  };\n  if (!element)\n    return unsub;\n  const down = (e2) => {\n    if (e2.button === 2)\n      return;\n    e2.preventDefault();\n    e2.stopPropagation();\n    element.style.touchAction = \"none\";\n    let startX = e2.clientX;\n    let startY = e2.clientY;\n    let isDragging = false;\n    const move = (e3) => {\n      e3.preventDefault();\n      e3.stopPropagation();\n      const x = e3.clientX;\n      const y = e3.clientY;\n      if (isDragging || Math.abs(x - startX) >= threshold || Math.abs(y - startY) >= threshold) {\n        const { left, top } = element.getBoundingClientRect();\n        if (!isDragging) {\n          isDragging = true;\n          onStart === null || onStart === void 0 ? void 0 : onStart(startX - left, startY - top);\n        }\n        onDrag(x - startX, y - startY, x - left, y - top);\n        startX = x;\n        startY = y;\n      }\n    };\n    const click = (e3) => {\n      if (isDragging) {\n        e3.preventDefault();\n        e3.stopPropagation();\n      }\n    };\n    const up = () => {\n      element.style.touchAction = \"\";\n      if (isDragging) {\n        onEnd === null || onEnd === void 0 ? void 0 : onEnd();\n      }\n      unsub();\n    };\n    document.addEventListener(\"pointermove\", move);\n    document.addEventListener(\"pointerup\", up);\n    document.addEventListener(\"pointerleave\", up);\n    document.addEventListener(\"click\", click, true);\n    unsub = () => {\n      document.removeEventListener(\"pointermove\", move);\n      document.removeEventListener(\"pointerup\", up);\n      document.removeEventListener(\"pointerleave\", up);\n      setTimeout(() => {\n        document.removeEventListener(\"click\", click, true);\n      }, 10);\n    };\n  };\n  element.addEventListener(\"pointerdown\", down);\n  return () => {\n    unsub();\n    element.removeEventListener(\"pointerdown\", down);\n  };\n}\nclass Renderer extends EventEmitter {\n  constructor(options, audioElement) {\n    super();\n    this.timeouts = [];\n    this.isScrolling = false;\n    this.audioData = null;\n    this.resizeObserver = null;\n    this.isDragging = false;\n    this.options = options;\n    const parent = this.parentFromOptionsContainer(options.container);\n    this.parent = parent;\n    const [div, shadow] = this.initHtml();\n    parent.appendChild(div);\n    this.container = div;\n    this.scrollContainer = shadow.querySelector(\".scroll\");\n    this.wrapper = shadow.querySelector(\".wrapper\");\n    this.canvasWrapper = shadow.querySelector(\".canvases\");\n    this.progressWrapper = shadow.querySelector(\".progress\");\n    this.cursor = shadow.querySelector(\".cursor\");\n    if (audioElement) {\n      shadow.appendChild(audioElement);\n    }\n    this.initEvents();\n  }\n  parentFromOptionsContainer(container) {\n    let parent;\n    if (typeof container === \"string\") {\n      parent = document.querySelector(container);\n    } else if (container instanceof HTMLElement) {\n      parent = container;\n    }\n    if (!parent) {\n      throw new Error(\"Container not found\");\n    }\n    return parent;\n  }\n  initEvents() {\n    const getClickPosition = (e2) => {\n      const rect = this.wrapper.getBoundingClientRect();\n      const x = e2.clientX - rect.left;\n      const y = e2.clientX - rect.left;\n      const relativeX = x / rect.width;\n      const relativeY = y / rect.height;\n      return [relativeX, relativeY];\n    };\n    this.wrapper.addEventListener(\"click\", (e2) => {\n      const [x, y] = getClickPosition(e2);\n      this.emit(\"click\", x, y);\n    });\n    this.wrapper.addEventListener(\"dblclick\", (e2) => {\n      const [x, y] = getClickPosition(e2);\n      this.emit(\"dblclick\", x, y);\n    });\n    if (this.options.dragToSeek) {\n      this.initDrag();\n    }\n    this.scrollContainer.addEventListener(\"scroll\", () => {\n      const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n      const startX = scrollLeft / scrollWidth;\n      const endX = (scrollLeft + clientWidth) / scrollWidth;\n      this.emit(\"scroll\", startX, endX);\n    });\n    const delay = this.createDelay(100);\n    this.resizeObserver = new ResizeObserver(() => {\n      delay(() => this.reRender());\n    });\n    this.resizeObserver.observe(this.scrollContainer);\n  }\n  initDrag() {\n    makeDraggable(\n      this.wrapper,\n      // On drag\n      (_, __, x) => {\n        this.emit(\"drag\", Math.max(0, Math.min(1, x / this.wrapper.getBoundingClientRect().width)));\n      },\n      // On start drag\n      () => this.isDragging = true,\n      // On end drag\n      () => this.isDragging = false\n    );\n  }\n  getHeight() {\n    const defaultHeight = 128;\n    if (this.options.height == null)\n      return defaultHeight;\n    if (!isNaN(Number(this.options.height)))\n      return Number(this.options.height);\n    if (this.options.height === \"auto\")\n      return this.parent.clientHeight || defaultHeight;\n    return defaultHeight;\n  }\n  initHtml() {\n    const div = document.createElement(\"div\");\n    const shadow = div.attachShadow({ mode: \"open\" });\n    shadow.innerHTML = `\n      <style>\n        :host {\n          user-select: none;\n          min-width: 1px;\n        }\n        :host audio {\n          display: block;\n          width: 100%;\n        }\n        :host .scroll {\n          overflow-x: auto;\n          overflow-y: hidden;\n          width: 100%;\n          position: relative;\n        }\n        :host .noScrollbar {\n          scrollbar-color: transparent;\n          scrollbar-width: none;\n        }\n        :host .noScrollbar::-webkit-scrollbar {\n          display: none;\n          -webkit-appearance: none;\n        }\n        :host .wrapper {\n          position: relative;\n          overflow: visible;\n          z-index: 2;\n        }\n        :host .canvases {\n          min-height: ${this.getHeight()}px;\n        }\n        :host .canvases > div {\n          position: relative;\n        }\n        :host canvas {\n          display: block;\n          position: absolute;\n          top: 0;\n          image-rendering: pixelated;\n        }\n        :host .progress {\n          pointer-events: none;\n          position: absolute;\n          z-index: 2;\n          top: 0;\n          left: 0;\n          width: 0;\n          height: 100%;\n          overflow: hidden;\n        }\n        :host .progress > div {\n          position: relative;\n        }\n        :host .cursor {\n          pointer-events: none;\n          position: absolute;\n          z-index: 5;\n          top: 0;\n          left: 0;\n          height: 100%;\n          border-radius: 2px;\n        }\n      </style>\n\n      <div class=\"scroll\" part=\"scroll\">\n        <div class=\"wrapper\" part=\"wrapper\">\n          <div class=\"canvases\"></div>\n          <div class=\"progress\" part=\"progress\"></div>\n          <div class=\"cursor\" part=\"cursor\"></div>\n        </div>\n      </div>\n    `;\n    return [div, shadow];\n  }\n  /** Wavesurfer itself calls this method. Do not call it manually. */\n  setOptions(options) {\n    if (this.options.container !== options.container) {\n      const newParent = this.parentFromOptionsContainer(options.container);\n      newParent.appendChild(this.container);\n      this.parent = newParent;\n    }\n    if (options.dragToSeek && !this.options.dragToSeek) {\n      this.initDrag();\n    }\n    this.options = options;\n    this.reRender();\n  }\n  getWrapper() {\n    return this.wrapper;\n  }\n  getScroll() {\n    return this.scrollContainer.scrollLeft;\n  }\n  destroy() {\n    var _a;\n    this.container.remove();\n    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n  }\n  createDelay(delayMs = 10) {\n    const context = {};\n    this.timeouts.push(context);\n    return (callback) => {\n      context.timeout && clearTimeout(context.timeout);\n      context.timeout = setTimeout(callback, delayMs);\n    };\n  }\n  // Convert array of color values to linear gradient\n  convertColorValues(color) {\n    if (!Array.isArray(color))\n      return color || \"\";\n    if (color.length < 2)\n      return color[0] || \"\";\n    const canvasElement = document.createElement(\"canvas\");\n    const ctx = canvasElement.getContext(\"2d\");\n    const gradient = ctx.createLinearGradient(0, 0, 0, canvasElement.height);\n    const colorStopPercentage = 1 / (color.length - 1);\n    color.forEach((color2, index) => {\n      const offset = index * colorStopPercentage;\n      gradient.addColorStop(offset, color2);\n    });\n    return gradient;\n  }\n  renderBarWaveform(channelData, options, ctx, vScale) {\n    const topChannel = channelData[0];\n    const bottomChannel = channelData[1] || channelData[0];\n    const length = topChannel.length;\n    const { width, height } = ctx.canvas;\n    const halfHeight = height / 2;\n    const pixelRatio = window.devicePixelRatio || 1;\n    const barWidth = options.barWidth ? options.barWidth * pixelRatio : 1;\n    const barGap = options.barGap ? options.barGap * pixelRatio : options.barWidth ? barWidth / 2 : 0;\n    const barRadius = options.barRadius || 0;\n    const barIndexScale = width / (barWidth + barGap) / length;\n    const rectFn = barRadius && \"roundRect\" in ctx ? \"roundRect\" : \"rect\";\n    ctx.beginPath();\n    let prevX = 0;\n    let maxTop = 0;\n    let maxBottom = 0;\n    for (let i2 = 0; i2 <= length; i2++) {\n      const x = Math.round(i2 * barIndexScale);\n      if (x > prevX) {\n        const topBarHeight = Math.round(maxTop * halfHeight * vScale);\n        const bottomBarHeight = Math.round(maxBottom * halfHeight * vScale);\n        const barHeight = topBarHeight + bottomBarHeight || 1;\n        let y = halfHeight - topBarHeight;\n        if (options.barAlign === \"top\") {\n          y = 0;\n        } else if (options.barAlign === \"bottom\") {\n          y = height - barHeight;\n        }\n        ctx[rectFn](prevX * (barWidth + barGap), y, barWidth, barHeight, barRadius);\n        prevX = x;\n        maxTop = 0;\n        maxBottom = 0;\n      }\n      const magnitudeTop = Math.abs(topChannel[i2] || 0);\n      const magnitudeBottom = Math.abs(bottomChannel[i2] || 0);\n      if (magnitudeTop > maxTop)\n        maxTop = magnitudeTop;\n      if (magnitudeBottom > maxBottom)\n        maxBottom = magnitudeBottom;\n    }\n    ctx.fill();\n    ctx.closePath();\n  }\n  renderLineWaveform(channelData, _options, ctx, vScale) {\n    const drawChannel = (index) => {\n      const channel = channelData[index] || channelData[0];\n      const length = channel.length;\n      const { height } = ctx.canvas;\n      const halfHeight = height / 2;\n      const hScale = ctx.canvas.width / length;\n      ctx.moveTo(0, halfHeight);\n      let prevX = 0;\n      let max = 0;\n      for (let i2 = 0; i2 <= length; i2++) {\n        const x = Math.round(i2 * hScale);\n        if (x > prevX) {\n          const h = Math.round(max * halfHeight * vScale) || 1;\n          const y = halfHeight + h * (index === 0 ? -1 : 1);\n          ctx.lineTo(prevX, y);\n          prevX = x;\n          max = 0;\n        }\n        const value = Math.abs(channel[i2] || 0);\n        if (value > max)\n          max = value;\n      }\n      ctx.lineTo(prevX, halfHeight);\n    };\n    ctx.beginPath();\n    drawChannel(0);\n    drawChannel(1);\n    ctx.fill();\n    ctx.closePath();\n  }\n  renderWaveform(channelData, options, ctx) {\n    ctx.fillStyle = this.convertColorValues(options.waveColor);\n    if (options.renderFunction) {\n      options.renderFunction(channelData, ctx);\n      return;\n    }\n    let vScale = options.barHeight || 1;\n    if (options.normalize) {\n      const max = Array.from(channelData[0]).reduce((max2, value) => Math.max(max2, Math.abs(value)), 0);\n      vScale = max ? 1 / max : 1;\n    }\n    if (options.barWidth || options.barGap || options.barAlign) {\n      this.renderBarWaveform(channelData, options, ctx, vScale);\n      return;\n    }\n    this.renderLineWaveform(channelData, options, ctx, vScale);\n  }\n  renderSingleCanvas(channelData, options, width, height, start, end, canvasContainer, progressContainer) {\n    const pixelRatio = window.devicePixelRatio || 1;\n    const canvas = document.createElement(\"canvas\");\n    const length = channelData[0].length;\n    canvas.width = Math.round(width * (end - start) / length);\n    canvas.height = height * pixelRatio;\n    canvas.style.width = `${Math.floor(canvas.width / pixelRatio)}px`;\n    canvas.style.height = `${height}px`;\n    canvas.style.left = `${Math.floor(start * width / pixelRatio / length)}px`;\n    canvasContainer.appendChild(canvas);\n    const ctx = canvas.getContext(\"2d\");\n    this.renderWaveform(channelData.map((channel) => channel.slice(start, end)), options, ctx);\n    if (canvas.width > 0 && canvas.height > 0) {\n      const progressCanvas = canvas.cloneNode();\n      const progressCtx = progressCanvas.getContext(\"2d\");\n      progressCtx.drawImage(canvas, 0, 0);\n      progressCtx.globalCompositeOperation = \"source-in\";\n      progressCtx.fillStyle = this.convertColorValues(options.progressColor);\n      progressCtx.fillRect(0, 0, canvas.width, canvas.height);\n      progressContainer.appendChild(progressCanvas);\n    }\n  }\n  renderChannel(channelData, options, width) {\n    const canvasContainer = document.createElement(\"div\");\n    const height = this.getHeight();\n    canvasContainer.style.height = `${height}px`;\n    this.canvasWrapper.style.minHeight = `${height}px`;\n    this.canvasWrapper.appendChild(canvasContainer);\n    const progressContainer = canvasContainer.cloneNode();\n    this.progressWrapper.appendChild(progressContainer);\n    const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer;\n    const len = channelData[0].length;\n    const scale = len / scrollWidth;\n    let viewportWidth = Math.min(Renderer.MAX_CANVAS_WIDTH, clientWidth);\n    if (options.barWidth || options.barGap) {\n      const barWidth = options.barWidth || 0.5;\n      const barGap = options.barGap || barWidth / 2;\n      const totalBarWidth = barWidth + barGap;\n      if (viewportWidth % totalBarWidth !== 0) {\n        viewportWidth = Math.floor(viewportWidth / totalBarWidth) * totalBarWidth;\n      }\n    }\n    const start = Math.floor(Math.abs(scrollLeft) * scale);\n    const end = Math.floor(start + viewportWidth * scale);\n    const viewportLen = end - start;\n    const draw = (start2, end2) => {\n      this.renderSingleCanvas(channelData, options, width, height, Math.max(0, start2), Math.min(end2, len), canvasContainer, progressContainer);\n    };\n    const headDelay = this.createDelay();\n    const tailDelay = this.createDelay();\n    const renderHead = (fromIndex, toIndex) => {\n      draw(fromIndex, toIndex);\n      if (fromIndex > 0) {\n        headDelay(() => {\n          renderHead(fromIndex - viewportLen, toIndex - viewportLen);\n        });\n      }\n    };\n    const renderTail = (fromIndex, toIndex) => {\n      draw(fromIndex, toIndex);\n      if (toIndex < len) {\n        tailDelay(() => {\n          renderTail(fromIndex + viewportLen, toIndex + viewportLen);\n        });\n      }\n    };\n    renderHead(start, end);\n    if (end < len) {\n      renderTail(end, end + viewportLen);\n    }\n  }\n  render(audioData) {\n    this.timeouts.forEach((context) => context.timeout && clearTimeout(context.timeout));\n    this.timeouts = [];\n    this.canvasWrapper.innerHTML = \"\";\n    this.progressWrapper.innerHTML = \"\";\n    this.wrapper.style.width = \"\";\n    if (this.options.width != null) {\n      this.scrollContainer.style.width = typeof this.options.width === \"number\" ? `${this.options.width}px` : this.options.width;\n    }\n    const pixelRatio = window.devicePixelRatio || 1;\n    const parentWidth = this.scrollContainer.clientWidth;\n    const scrollWidth = Math.ceil(audioData.duration * (this.options.minPxPerSec || 0));\n    this.isScrolling = scrollWidth > parentWidth;\n    const useParentWidth = this.options.fillParent && !this.isScrolling;\n    const width = (useParentWidth ? parentWidth : scrollWidth) * pixelRatio;\n    this.wrapper.style.width = useParentWidth ? \"100%\" : `${scrollWidth}px`;\n    this.scrollContainer.style.overflowX = this.isScrolling ? \"auto\" : \"hidden\";\n    this.scrollContainer.classList.toggle(\"noScrollbar\", !!this.options.hideScrollbar);\n    this.cursor.style.backgroundColor = `${this.options.cursorColor || this.options.progressColor}`;\n    this.cursor.style.width = `${this.options.cursorWidth}px`;\n    if (this.options.splitChannels) {\n      for (let i2 = 0; i2 < audioData.numberOfChannels; i2++) {\n        const options = Object.assign(Object.assign({}, this.options), this.options.splitChannels[i2]);\n        this.renderChannel([audioData.getChannelData(i2)], options, width);\n      }\n    } else {\n      const channels = [audioData.getChannelData(0)];\n      if (audioData.numberOfChannels > 1)\n        channels.push(audioData.getChannelData(1));\n      this.renderChannel(channels, this.options, width);\n    }\n    this.audioData = audioData;\n    this.emit(\"render\");\n  }\n  reRender() {\n    if (!this.audioData)\n      return;\n    const oldCursorPosition = this.progressWrapper.clientWidth;\n    this.render(this.audioData);\n    const newCursortPosition = this.progressWrapper.clientWidth;\n    this.scrollContainer.scrollLeft += newCursortPosition - oldCursorPosition;\n  }\n  zoom(minPxPerSec) {\n    this.options.minPxPerSec = minPxPerSec;\n    this.reRender();\n  }\n  scrollIntoView(progress, isPlaying = false) {\n    const { clientWidth, scrollLeft, scrollWidth } = this.scrollContainer;\n    const progressWidth = scrollWidth * progress;\n    const center = clientWidth / 2;\n    const minScroll = isPlaying && this.options.autoCenter && !this.isDragging ? center : clientWidth;\n    if (progressWidth > scrollLeft + minScroll || progressWidth < scrollLeft) {\n      if (this.options.autoCenter && !this.isDragging) {\n        const minDiff = center / 20;\n        if (progressWidth - (scrollLeft + center) >= minDiff && progressWidth < scrollLeft + clientWidth) {\n          this.scrollContainer.scrollLeft += minDiff;\n        } else {\n          this.scrollContainer.scrollLeft = progressWidth - center;\n        }\n      } else if (this.isDragging) {\n        const gap = 10;\n        this.scrollContainer.scrollLeft = progressWidth < scrollLeft ? progressWidth - gap : progressWidth - clientWidth + gap;\n      } else {\n        this.scrollContainer.scrollLeft = progressWidth;\n      }\n    }\n    {\n      const { scrollLeft: scrollLeft2 } = this.scrollContainer;\n      const startX = scrollLeft2 / scrollWidth;\n      const endX = (scrollLeft2 + clientWidth) / scrollWidth;\n      this.emit(\"scroll\", startX, endX);\n    }\n  }\n  renderProgress(progress, isPlaying) {\n    if (isNaN(progress))\n      return;\n    const percents = progress * 100;\n    this.canvasWrapper.style.clipPath = `polygon(${percents}% 0, 100% 0, 100% 100%, ${percents}% 100%)`;\n    this.progressWrapper.style.width = `${percents}%`;\n    this.cursor.style.left = `${percents}%`;\n    this.cursor.style.marginLeft = Math.round(percents) === 100 ? `-${this.options.cursorWidth}px` : \"\";\n    if (this.isScrolling && this.options.autoScroll) {\n      this.scrollIntoView(progress, isPlaying);\n    }\n  }\n}\nRenderer.MAX_CANVAS_WIDTH = 4e3;\nclass Timer extends EventEmitter {\n  constructor() {\n    super(...arguments);\n    this.unsubscribe = () => void 0;\n  }\n  start() {\n    this.unsubscribe = this.on(\"tick\", () => {\n      requestAnimationFrame(() => {\n        this.emit(\"tick\");\n      });\n    });\n    this.emit(\"tick\");\n  }\n  stop() {\n    this.unsubscribe();\n  }\n  destroy() {\n    this.unsubscribe();\n  }\n}\nvar __awaiter$1 = function(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function(resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function(resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nclass WebAudioPlayer extends EventEmitter {\n  constructor(audioContext = new AudioContext()) {\n    super();\n    this.bufferNode = null;\n    this.autoplay = false;\n    this.playStartTime = 0;\n    this.playedDuration = 0;\n    this._muted = false;\n    this.buffer = null;\n    this.currentSrc = \"\";\n    this.paused = true;\n    this.crossOrigin = null;\n    this.audioContext = audioContext;\n    this.gainNode = this.audioContext.createGain();\n    this.gainNode.connect(this.audioContext.destination);\n  }\n  load() {\n    return __awaiter$1(this, void 0, void 0, function* () {\n      return;\n    });\n  }\n  get src() {\n    return this.currentSrc;\n  }\n  set src(value) {\n    this.currentSrc = value;\n    fetch(value).then((response) => response.arrayBuffer()).then((arrayBuffer) => this.audioContext.decodeAudioData(arrayBuffer)).then((audioBuffer) => {\n      this.buffer = audioBuffer;\n      this.emit(\"loadedmetadata\");\n      this.emit(\"canplay\");\n      if (this.autoplay)\n        this.play();\n    });\n  }\n  _play() {\n    var _a;\n    if (!this.paused)\n      return;\n    this.paused = false;\n    (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.disconnect();\n    this.bufferNode = this.audioContext.createBufferSource();\n    this.bufferNode.buffer = this.buffer;\n    this.bufferNode.connect(this.gainNode);\n    if (this.playedDuration >= this.duration) {\n      this.playedDuration = 0;\n    }\n    this.bufferNode.start(this.audioContext.currentTime, this.playedDuration);\n    this.playStartTime = this.audioContext.currentTime;\n    this.bufferNode.onended = () => {\n      if (this.currentTime >= this.duration) {\n        this.pause();\n        this.emit(\"ended\");\n      }\n    };\n  }\n  _pause() {\n    var _a;\n    if (this.paused)\n      return;\n    this.paused = true;\n    (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.stop();\n    this.playedDuration += this.audioContext.currentTime - this.playStartTime;\n  }\n  play() {\n    return __awaiter$1(this, void 0, void 0, function* () {\n      this._play();\n      this.emit(\"play\");\n    });\n  }\n  pause() {\n    this._pause();\n    this.emit(\"pause\");\n  }\n  setSinkId(deviceId) {\n    return __awaiter$1(this, void 0, void 0, function* () {\n      const ac = this.audioContext;\n      return ac.setSinkId(deviceId);\n    });\n  }\n  get playbackRate() {\n    var _a, _b;\n    return (_b = (_a = this.bufferNode) === null || _a === void 0 ? void 0 : _a.playbackRate.value) !== null && _b !== void 0 ? _b : 1;\n  }\n  set playbackRate(value) {\n    if (this.bufferNode) {\n      this.bufferNode.playbackRate.value = value;\n    }\n  }\n  get currentTime() {\n    return this.paused ? this.playedDuration : this.playedDuration + this.audioContext.currentTime - this.playStartTime;\n  }\n  set currentTime(value) {\n    this.emit(\"seeking\");\n    if (this.paused) {\n      this.playedDuration = value;\n    } else {\n      this._pause();\n      this.playedDuration = value;\n      this._play();\n    }\n    this.emit(\"timeupdate\");\n  }\n  get duration() {\n    var _a;\n    return ((_a = this.buffer) === null || _a === void 0 ? void 0 : _a.duration) || 0;\n  }\n  get volume() {\n    return this.gainNode.gain.value;\n  }\n  set volume(value) {\n    this.gainNode.gain.value = value;\n    this.emit(\"volumechange\");\n  }\n  get muted() {\n    return this._muted;\n  }\n  set muted(value) {\n    if (this._muted === value)\n      return;\n    this._muted = value;\n    if (this._muted) {\n      this.gainNode.disconnect();\n    } else {\n      this.gainNode.connect(this.audioContext.destination);\n    }\n  }\n  /** Get the GainNode used to play the audio. Can be used to attach filters. */\n  getGainNode() {\n    return this.gainNode;\n  }\n}\nvar __awaiter = function(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function(resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function(resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e2) {\n        reject(e2);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nconst defaultOptions = {\n  waveColor: \"#999\",\n  progressColor: \"#555\",\n  cursorWidth: 1,\n  minPxPerSec: 0,\n  fillParent: true,\n  interact: true,\n  dragToSeek: false,\n  autoScroll: true,\n  autoCenter: true,\n  sampleRate: 8e3\n};\nclass WaveSurfer extends Player {\n  /** Create a new WaveSurfer instance */\n  static create(options) {\n    return new WaveSurfer(options);\n  }\n  /** Create a new WaveSurfer instance */\n  constructor(options) {\n    const media = options.media || (options.backend === \"WebAudio\" ? new WebAudioPlayer() : void 0);\n    super({\n      media,\n      mediaControls: options.mediaControls,\n      autoplay: options.autoplay,\n      playbackRate: options.audioRate\n    });\n    this.plugins = [];\n    this.decodedData = null;\n    this.subscriptions = [];\n    this.mediaSubscriptions = [];\n    this.options = Object.assign({}, defaultOptions, options);\n    this.timer = new Timer();\n    const audioElement = media ? void 0 : this.getMediaElement();\n    this.renderer = new Renderer(this.options, audioElement);\n    this.initPlayerEvents();\n    this.initRendererEvents();\n    this.initTimerEvents();\n    this.initPlugins();\n    const url = this.options.url || this.getSrc();\n    if (url) {\n      this.load(url, this.options.peaks, this.options.duration);\n    } else if (this.options.peaks && this.options.duration) {\n      this.loadPredecoded();\n    }\n  }\n  initTimerEvents() {\n    this.subscriptions.push(this.timer.on(\"tick\", () => {\n      const currentTime = this.getCurrentTime();\n      this.renderer.renderProgress(currentTime / this.getDuration(), true);\n      this.emit(\"timeupdate\", currentTime);\n      this.emit(\"audioprocess\", currentTime);\n    }));\n  }\n  initPlayerEvents() {\n    this.mediaSubscriptions.push(this.onMediaEvent(\"timeupdate\", () => {\n      const currentTime = this.getCurrentTime();\n      this.renderer.renderProgress(currentTime / this.getDuration(), this.isPlaying());\n      this.emit(\"timeupdate\", currentTime);\n    }), this.onMediaEvent(\"play\", () => {\n      this.emit(\"play\");\n      this.timer.start();\n    }), this.onMediaEvent(\"pause\", () => {\n      this.emit(\"pause\");\n      this.timer.stop();\n    }), this.onMediaEvent(\"emptied\", () => {\n      this.timer.stop();\n    }), this.onMediaEvent(\"ended\", () => {\n      this.emit(\"finish\");\n    }), this.onMediaEvent(\"seeking\", () => {\n      this.emit(\"seeking\", this.getCurrentTime());\n    }));\n  }\n  initRendererEvents() {\n    this.subscriptions.push(\n      // Seek on click\n      this.renderer.on(\"click\", (relativeX, relativeY) => {\n        if (this.options.interact) {\n          this.seekTo(relativeX);\n          this.emit(\"interaction\", relativeX * this.getDuration());\n          this.emit(\"click\", relativeX, relativeY);\n        }\n      }),\n      // Double click\n      this.renderer.on(\"dblclick\", (relativeX, relativeY) => {\n        this.emit(\"dblclick\", relativeX, relativeY);\n      }),\n      // Scroll\n      this.renderer.on(\"scroll\", (startX, endX) => {\n        const duration = this.getDuration();\n        this.emit(\"scroll\", startX * duration, endX * duration);\n      }),\n      // Redraw\n      this.renderer.on(\"render\", () => {\n        this.emit(\"redraw\");\n      })\n    );\n    {\n      let debounce;\n      this.subscriptions.push(this.renderer.on(\"drag\", (relativeX) => {\n        if (!this.options.interact)\n          return;\n        this.renderer.renderProgress(relativeX);\n        clearTimeout(debounce);\n        debounce = setTimeout(() => {\n          this.seekTo(relativeX);\n        }, this.isPlaying() ? 0 : 200);\n        this.emit(\"interaction\", relativeX * this.getDuration());\n        this.emit(\"drag\", relativeX);\n      }));\n    }\n  }\n  initPlugins() {\n    var _a;\n    if (!((_a = this.options.plugins) === null || _a === void 0 ? void 0 : _a.length))\n      return;\n    this.options.plugins.forEach((plugin) => {\n      this.registerPlugin(plugin);\n    });\n  }\n  unsubscribePlayerEvents() {\n    this.mediaSubscriptions.forEach((unsubscribe) => unsubscribe());\n    this.mediaSubscriptions = [];\n  }\n  /** Set new wavesurfer options and re-render it */\n  setOptions(options) {\n    this.options = Object.assign({}, this.options, options);\n    this.renderer.setOptions(this.options);\n    if (options.audioRate) {\n      this.setPlaybackRate(options.audioRate);\n    }\n    if (options.mediaControls != null) {\n      this.getMediaElement().controls = options.mediaControls;\n    }\n  }\n  /** Register a wavesurfer.js plugin */\n  registerPlugin(plugin) {\n    plugin.init(this);\n    this.plugins.push(plugin);\n    this.subscriptions.push(plugin.once(\"destroy\", () => {\n      this.plugins = this.plugins.filter((p) => p !== plugin);\n    }));\n    return plugin;\n  }\n  /** For plugins only: get the waveform wrapper div */\n  getWrapper() {\n    return this.renderer.getWrapper();\n  }\n  /** Get the current scroll position in pixels */\n  getScroll() {\n    return this.renderer.getScroll();\n  }\n  /** Get all registered plugins */\n  getActivePlugins() {\n    return this.plugins;\n  }\n  loadPredecoded() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.options.peaks && this.options.duration) {\n        this.decodedData = Decoder.createBuffer(this.options.peaks, this.options.duration);\n        yield Promise.resolve();\n        this.renderDecoded();\n      }\n    });\n  }\n  renderDecoded() {\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.decodedData) {\n        this.emit(\"decode\", this.getDuration());\n        this.renderer.render(this.decodedData);\n      }\n    });\n  }\n  loadAudio(url, blob, channelData, duration) {\n    return __awaiter(this, void 0, void 0, function* () {\n      this.emit(\"load\", url);\n      if (!this.options.media && this.isPlaying())\n        this.pause();\n      this.decodedData = null;\n      if (!blob && !channelData) {\n        const onProgress = (percentage) => this.emit(\"loading\", percentage);\n        blob = yield Fetcher.fetchBlob(url, onProgress, this.options.fetchParams);\n      }\n      this.setSrc(url, blob);\n      duration = (yield Promise.resolve(duration || this.getDuration())) || (yield new Promise((resolve) => {\n        this.onceMediaEvent(\"loadedmetadata\", () => resolve(this.getDuration()));\n      })) || (yield Promise.resolve(0));\n      if (channelData) {\n        this.decodedData = Decoder.createBuffer(channelData, duration);\n      } else if (blob) {\n        const arrayBuffer = yield blob.arrayBuffer();\n        this.decodedData = yield Decoder.decode(arrayBuffer, this.options.sampleRate);\n      }\n      this.renderDecoded();\n      this.emit(\"ready\", this.getDuration());\n    });\n  }\n  /** Load an audio file by URL, with optional pre-decoded audio data */\n  load(url, channelData, duration) {\n    return __awaiter(this, void 0, void 0, function* () {\n      yield this.loadAudio(url, void 0, channelData, duration);\n    });\n  }\n  /** Load an audio blob */\n  loadBlob(blob, channelData, duration) {\n    return __awaiter(this, void 0, void 0, function* () {\n      yield this.loadAudio(\"blob\", blob, channelData, duration);\n    });\n  }\n  /** Zoom the waveform by a given pixels-per-second factor */\n  zoom(minPxPerSec) {\n    if (!this.decodedData) {\n      throw new Error(\"No audio loaded\");\n    }\n    this.renderer.zoom(minPxPerSec);\n    this.emit(\"zoom\", minPxPerSec);\n  }\n  /** Get the decoded audio data */\n  getDecodedData() {\n    return this.decodedData;\n  }\n  /** Get decoded peaks */\n  exportPeaks({ channels = 2, maxLength = 8e3, precision = 1e4 } = {}) {\n    if (!this.decodedData) {\n      throw new Error(\"The audio has not been decoded yet\");\n    }\n    const maxChannels = Math.min(channels, this.decodedData.numberOfChannels);\n    const peaks = [];\n    for (let i2 = 0; i2 < maxChannels; i2++) {\n      const channel = this.decodedData.getChannelData(i2);\n      const data = [];\n      const sampleSize = Math.round(channel.length / maxLength);\n      for (let i3 = 0; i3 < maxLength; i3++) {\n        const sample = channel.slice(i3 * sampleSize, (i3 + 1) * sampleSize);\n        const max = Math.max(...sample);\n        data.push(Math.round(max * precision) / precision);\n      }\n      peaks.push(data);\n    }\n    return peaks;\n  }\n  /** Get the duration of the audio in seconds */\n  getDuration() {\n    let duration = super.getDuration() || 0;\n    if ((duration === 0 || duration === Infinity) && this.decodedData) {\n      duration = this.decodedData.duration;\n    }\n    return duration;\n  }\n  /** Toggle if the waveform should react to clicks */\n  toggleInteraction(isInteractive) {\n    this.options.interact = isInteractive;\n  }\n  /** Seek to a percentage of audio as [0..1] (0 = beginning, 1 = end) */\n  seekTo(progress) {\n    const time = this.getDuration() * progress;\n    this.setTime(time);\n  }\n  /** Play or pause the audio */\n  playPause() {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.isPlaying() ? this.pause() : this.play();\n    });\n  }\n  /** Stop the audio and go to the beginning */\n  stop() {\n    this.pause();\n    this.setTime(0);\n  }\n  /** Skip N or -N seconds from the current position */\n  skip(seconds) {\n    this.setTime(this.getCurrentTime() + seconds);\n  }\n  /** Empty the waveform by loading a tiny silent audio */\n  empty() {\n    this.load(\"\", [[0]], 1e-3);\n  }\n  /** Set HTML media element */\n  setMediaElement(element) {\n    this.unsubscribePlayerEvents();\n    super.setMediaElement(element);\n    this.initPlayerEvents();\n  }\n  /** Unmount wavesurfer */\n  destroy() {\n    this.emit(\"destroy\");\n    this.plugins.forEach((plugin) => plugin.destroy());\n    this.subscriptions.forEach((unsubscribe) => unsubscribe());\n    this.unsubscribePlayerEvents();\n    this.timer.destroy();\n    this.renderer.destroy();\n    super.destroy();\n  }\n}\nfunction audioBufferToWav(audioBuffer) {\n  const numOfChan = audioBuffer.numberOfChannels;\n  const length = audioBuffer.length * numOfChan * 2 + 44;\n  const buffer = new ArrayBuffer(length);\n  const view = new DataView(buffer);\n  let offset = 0;\n  const writeString = function(view2, offset2, string) {\n    for (let i2 = 0; i2 < string.length; i2++) {\n      view2.setUint8(offset2 + i2, string.charCodeAt(i2));\n    }\n  };\n  writeString(view, offset, \"RIFF\");\n  offset += 4;\n  view.setUint32(offset, length - 8, true);\n  offset += 4;\n  writeString(view, offset, \"WAVE\");\n  offset += 4;\n  writeString(view, offset, \"fmt \");\n  offset += 4;\n  view.setUint32(offset, 16, true);\n  offset += 4;\n  view.setUint16(offset, 1, true);\n  offset += 2;\n  view.setUint16(offset, numOfChan, true);\n  offset += 2;\n  view.setUint32(offset, audioBuffer.sampleRate, true);\n  offset += 4;\n  view.setUint32(offset, audioBuffer.sampleRate * 2 * numOfChan, true);\n  offset += 4;\n  view.setUint16(offset, numOfChan * 2, true);\n  offset += 2;\n  view.setUint16(offset, 16, true);\n  offset += 2;\n  writeString(view, offset, \"data\");\n  offset += 4;\n  view.setUint32(offset, audioBuffer.length * numOfChan * 2, true);\n  offset += 4;\n  for (let i2 = 0; i2 < audioBuffer.length; i2++) {\n    for (let channel = 0; channel < numOfChan; channel++) {\n      const sample = Math.max(\n        -1,\n        Math.min(1, audioBuffer.getChannelData(channel)[i2])\n      );\n      view.setInt16(offset, sample * 32767, true);\n      offset += 2;\n    }\n  }\n  return new Uint8Array(buffer);\n}\nconst process_audio = async (audioBuffer, start, end, waveform_sample_rate) => {\n  const audioContext = new AudioContext({\n    sampleRate: waveform_sample_rate || audioBuffer.sampleRate\n  });\n  const numberOfChannels = audioBuffer.numberOfChannels;\n  const sampleRate = waveform_sample_rate || audioBuffer.sampleRate;\n  let trimmedLength = audioBuffer.length;\n  let startOffset = 0;\n  if (start && end) {\n    startOffset = Math.round(start * sampleRate);\n    const endOffset = Math.round(end * sampleRate);\n    trimmedLength = endOffset - startOffset;\n  }\n  const trimmedAudioBuffer = audioContext.createBuffer(\n    numberOfChannels,\n    trimmedLength,\n    sampleRate\n  );\n  for (let channel = 0; channel < numberOfChannels; channel++) {\n    const channelData = audioBuffer.getChannelData(channel);\n    const trimmedData = trimmedAudioBuffer.getChannelData(channel);\n    for (let i2 = 0; i2 < trimmedLength; i2++) {\n      trimmedData[i2] = channelData[startOffset + i2];\n    }\n  }\n  return audioBufferToWav(trimmedAudioBuffer);\n};\nconst skip_audio = (waveform, amount) => {\n  if (!waveform)\n    return;\n  waveform.skip(amount);\n};\nconst get_skip_rewind_amount = (audio_duration, skip_length) => {\n  if (!skip_length) {\n    skip_length = 5;\n  }\n  return audio_duration / 100 * skip_length || 5;\n};\nclass t {\n  constructor() {\n    this.listeners = {}, this.on = this.addEventListener, this.un = this.removeEventListener;\n  }\n  addEventListener(t2, e2, i2) {\n    if (this.listeners[t2] || (this.listeners[t2] = /* @__PURE__ */ new Set()), this.listeners[t2].add(e2), null == i2 ? void 0 : i2.once) {\n      const i3 = () => {\n        this.removeEventListener(t2, i3), this.removeEventListener(t2, e2);\n      };\n      return this.addEventListener(t2, i3), i3;\n    }\n    return () => this.removeEventListener(t2, e2);\n  }\n  removeEventListener(t2, e2) {\n    var i2;\n    null === (i2 = this.listeners[t2]) || void 0 === i2 || i2.delete(e2);\n  }\n  once(t2, e2) {\n    return this.on(t2, e2, { once: true });\n  }\n  unAll() {\n    this.listeners = {};\n  }\n  emit(t2, ...e2) {\n    this.listeners[t2] && this.listeners[t2].forEach((t3) => t3(...e2));\n  }\n}\nclass e extends t {\n  constructor(t2) {\n    super(), this.subscriptions = [], this.options = t2;\n  }\n  onInit() {\n  }\n  init(t2) {\n    this.wavesurfer = t2, this.onInit();\n  }\n  destroy() {\n    this.emit(\"destroy\"), this.subscriptions.forEach((t2) => t2());\n  }\n}\nfunction i(t2, e2, i2, n2, s2 = 5) {\n  let r = () => {\n  };\n  if (!t2)\n    return r;\n  const o = (o2) => {\n    if (2 === o2.button)\n      return;\n    o2.preventDefault(), o2.stopPropagation(), t2.style.touchAction = \"none\";\n    let a = o2.clientX, h = o2.clientY, l = false;\n    const d = (n3) => {\n      n3.preventDefault(), n3.stopPropagation();\n      const r2 = n3.clientX, o3 = n3.clientY;\n      if (l || Math.abs(r2 - a) >= s2 || Math.abs(o3 - h) >= s2) {\n        const { left: n4, top: s3 } = t2.getBoundingClientRect();\n        l || (l = true, null == i2 || i2(a - n4, h - s3)), e2(r2 - a, o3 - h, r2 - n4, o3 - s3), a = r2, h = o3;\n      }\n    }, u = (t3) => {\n      l && (t3.preventDefault(), t3.stopPropagation());\n    }, c = () => {\n      t2.style.touchAction = \"\", l && (null == n2 || n2()), r();\n    };\n    document.addEventListener(\"pointermove\", d), document.addEventListener(\"pointerup\", c), document.addEventListener(\"pointerleave\", c), document.addEventListener(\"click\", u, true), r = () => {\n      document.removeEventListener(\"pointermove\", d), document.removeEventListener(\"pointerup\", c), document.removeEventListener(\"pointerleave\", c), setTimeout(() => {\n        document.removeEventListener(\"click\", u, true);\n      }, 10);\n    };\n  };\n  return t2.addEventListener(\"pointerdown\", o), () => {\n    r(), t2.removeEventListener(\"pointerdown\", o);\n  };\n}\nclass n extends t {\n  constructor(t2, e2, i2 = 0) {\n    var n2, s2, r, o, a, h, l;\n    super(), this.totalDuration = e2, this.numberOfChannels = i2, this.minLength = 0, this.maxLength = 1 / 0, this.id = t2.id || `region-${Math.random().toString(32).slice(2)}`, this.start = this.clampPosition(t2.start), this.end = this.clampPosition(null !== (n2 = t2.end) && void 0 !== n2 ? n2 : t2.start), this.drag = null === (s2 = t2.drag) || void 0 === s2 || s2, this.resize = null === (r = t2.resize) || void 0 === r || r, this.color = null !== (o = t2.color) && void 0 !== o ? o : \"rgba(0, 0, 0, 0.1)\", this.minLength = null !== (a = t2.minLength) && void 0 !== a ? a : this.minLength, this.maxLength = null !== (h = t2.maxLength) && void 0 !== h ? h : this.maxLength, this.channelIdx = null !== (l = t2.channelIdx) && void 0 !== l ? l : -1, this.element = this.initElement(), this.setContent(t2.content), this.setPart(), this.renderPosition(), this.initMouseEvents();\n  }\n  clampPosition(t2) {\n    return Math.max(0, Math.min(this.totalDuration, t2));\n  }\n  setPart() {\n    const t2 = this.start === this.end;\n    this.element.setAttribute(\"part\", `${t2 ? \"marker\" : \"region\"} ${this.id}`);\n  }\n  addResizeHandles(t2) {\n    const e2 = document.createElement(\"div\");\n    e2.setAttribute(\"data-resize\", \"left\"), e2.setAttribute(\"style\", \"\\n        position: absolute;\\n        z-index: 2;\\n        width: 6px;\\n        height: 100%;\\n        top: 0;\\n        left: 0;\\n        border-left: 2px solid rgba(0, 0, 0, 0.5);\\n        border-radius: 2px 0 0 2px;\\n        cursor: ew-resize;\\n        word-break: keep-all;\\n      \"), e2.setAttribute(\"part\", \"region-handle region-handle-left\");\n    const n2 = e2.cloneNode();\n    n2.setAttribute(\"data-resize\", \"right\"), n2.style.left = \"\", n2.style.right = \"0\", n2.style.borderRight = n2.style.borderLeft, n2.style.borderLeft = \"\", n2.style.borderRadius = \"0 2px 2px 0\", n2.setAttribute(\"part\", \"region-handle region-handle-right\"), t2.appendChild(e2), t2.appendChild(n2);\n    i(e2, (t3) => this.onResize(t3, \"start\"), () => null, () => this.onEndResizing(), 1), i(n2, (t3) => this.onResize(t3, \"end\"), () => null, () => this.onEndResizing(), 1);\n  }\n  removeResizeHandles(t2) {\n    const e2 = t2.querySelector('[data-resize=\"left\"]'), i2 = t2.querySelector('[data-resize=\"right\"]');\n    e2 && t2.removeChild(e2), i2 && t2.removeChild(i2);\n  }\n  initElement() {\n    const t2 = document.createElement(\"div\"), e2 = this.start === this.end;\n    let i2 = 0, n2 = 100;\n    return this.channelIdx >= 0 && this.channelIdx < this.numberOfChannels && (n2 = 100 / this.numberOfChannels, i2 = n2 * this.channelIdx), t2.setAttribute(\"style\", `\n      position: absolute;\n      top: ${i2}%;\n      height: ${n2}%;\n      background-color: ${e2 ? \"none\" : this.color};\n      border-left: ${e2 ? \"2px solid \" + this.color : \"none\"};\n      border-radius: 2px;\n      box-sizing: border-box;\n      transition: background-color 0.2s ease;\n      cursor: ${this.drag ? \"grab\" : \"default\"};\n      pointer-events: all;\n    `), !e2 && this.resize && this.addResizeHandles(t2), t2;\n  }\n  renderPosition() {\n    const t2 = this.start / this.totalDuration, e2 = (this.totalDuration - this.end) / this.totalDuration;\n    this.element.style.left = 100 * t2 + \"%\", this.element.style.right = 100 * e2 + \"%\";\n  }\n  initMouseEvents() {\n    const { element: t2 } = this;\n    t2 && (t2.addEventListener(\"click\", (t3) => this.emit(\"click\", t3)), t2.addEventListener(\"mouseenter\", (t3) => this.emit(\"over\", t3)), t2.addEventListener(\"mouseleave\", (t3) => this.emit(\"leave\", t3)), t2.addEventListener(\"dblclick\", (t3) => this.emit(\"dblclick\", t3)), i(t2, (t3) => this.onMove(t3), () => this.onStartMoving(), () => this.onEndMoving()));\n  }\n  onStartMoving() {\n    this.drag && (this.element.style.cursor = \"grabbing\");\n  }\n  onEndMoving() {\n    this.drag && (this.element.style.cursor = \"grab\", this.emit(\"update-end\"));\n  }\n  _onUpdate(t2, e2) {\n    if (!this.element.parentElement)\n      return;\n    const i2 = t2 / this.element.parentElement.clientWidth * this.totalDuration, n2 = e2 && \"start\" !== e2 ? this.start : this.start + i2, s2 = e2 && \"end\" !== e2 ? this.end : this.end + i2, r = s2 - n2;\n    n2 >= 0 && s2 <= this.totalDuration && n2 <= s2 && r >= this.minLength && r <= this.maxLength && (this.start = n2, this.end = s2, this.renderPosition(), this.emit(\"update\"));\n  }\n  onMove(t2) {\n    this.drag && this._onUpdate(t2);\n  }\n  onResize(t2, e2) {\n    this.resize && this._onUpdate(t2, e2);\n  }\n  onEndResizing() {\n    this.resize && this.emit(\"update-end\");\n  }\n  _setTotalDuration(t2) {\n    this.totalDuration = t2, this.renderPosition();\n  }\n  play() {\n    this.emit(\"play\");\n  }\n  setContent(t2) {\n    var e2;\n    if (null === (e2 = this.content) || void 0 === e2 || e2.remove(), t2) {\n      if (\"string\" == typeof t2) {\n        this.content = document.createElement(\"div\");\n        const e3 = this.start === this.end;\n        this.content.style.padding = `0.2em ${e3 ? 0.2 : 0.4}em`, this.content.textContent = t2;\n      } else\n        this.content = t2;\n      this.content.setAttribute(\"part\", \"region-content\"), this.element.appendChild(this.content);\n    } else\n      this.content = void 0;\n  }\n  setOptions(t2) {\n    var e2, i2;\n    if (t2.color && (this.color = t2.color, this.element.style.backgroundColor = this.color), void 0 !== t2.drag && (this.drag = t2.drag, this.element.style.cursor = this.drag ? \"grab\" : \"default\"), void 0 !== t2.start || void 0 !== t2.end) {\n      const n2 = this.start === this.end;\n      this.start = this.clampPosition(null !== (e2 = t2.start) && void 0 !== e2 ? e2 : this.start), this.end = this.clampPosition(null !== (i2 = t2.end) && void 0 !== i2 ? i2 : n2 ? this.start : this.end), this.renderPosition(), this.setPart();\n    }\n    if (t2.content && this.setContent(t2.content), t2.id && (this.id = t2.id, this.setPart()), void 0 !== t2.resize && t2.resize !== this.resize) {\n      const e3 = this.start === this.end;\n      this.resize = t2.resize, this.resize && !e3 ? this.addResizeHandles(this.element) : this.removeResizeHandles(this.element);\n    }\n  }\n  remove() {\n    this.emit(\"remove\"), this.element.remove(), this.element = null;\n  }\n}\nclass s extends e {\n  constructor(t2) {\n    super(t2), this.regions = [], this.regionsContainer = this.initRegionsContainer();\n  }\n  static create(t2) {\n    return new s(t2);\n  }\n  onInit() {\n    if (!this.wavesurfer)\n      throw Error(\"WaveSurfer is not initialized\");\n    this.wavesurfer.getWrapper().appendChild(this.regionsContainer);\n    let t2 = [];\n    this.subscriptions.push(this.wavesurfer.on(\"timeupdate\", (e2) => {\n      const i2 = this.regions.filter((t3) => t3.start <= e2 && t3.end >= e2);\n      i2.forEach((e3) => {\n        t2.includes(e3) || this.emit(\"region-in\", e3);\n      }), t2.forEach((t3) => {\n        i2.includes(t3) || this.emit(\"region-out\", t3);\n      }), t2 = i2;\n    }));\n  }\n  initRegionsContainer() {\n    const t2 = document.createElement(\"div\");\n    return t2.setAttribute(\"style\", \"\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      z-index: 3;\\n      pointer-events: none;\\n    \"), t2;\n  }\n  getRegions() {\n    return this.regions;\n  }\n  avoidOverlapping(t2) {\n    if (!t2.content)\n      return;\n    const e2 = t2.content, i2 = e2.getBoundingClientRect().left, n2 = t2.element.scrollWidth, s2 = this.regions.filter((e3) => {\n      if (e3 === t2 || !e3.content)\n        return false;\n      const s3 = e3.content.getBoundingClientRect().left, r = e3.element.scrollWidth;\n      return i2 < s3 + r && s3 < i2 + n2;\n    }).map((t3) => {\n      var e3;\n      return (null === (e3 = t3.content) || void 0 === e3 ? void 0 : e3.getBoundingClientRect().height) || 0;\n    }).reduce((t3, e3) => t3 + e3, 0);\n    e2.style.marginTop = `${s2}px`;\n  }\n  saveRegion(t2) {\n    this.regionsContainer.appendChild(t2.element), this.avoidOverlapping(t2), this.regions.push(t2);\n    const e2 = [t2.on(\"update-end\", () => {\n      this.avoidOverlapping(t2), this.emit(\"region-updated\", t2);\n    }), t2.on(\"play\", () => {\n      var e3, i2;\n      null === (e3 = this.wavesurfer) || void 0 === e3 || e3.play(), null === (i2 = this.wavesurfer) || void 0 === i2 || i2.setTime(t2.start);\n    }), t2.on(\"click\", (e3) => {\n      this.emit(\"region-clicked\", t2, e3);\n    }), t2.on(\"dblclick\", (e3) => {\n      this.emit(\"region-double-clicked\", t2, e3);\n    }), t2.once(\"remove\", () => {\n      e2.forEach((t3) => t3()), this.regions = this.regions.filter((e3) => e3 !== t2);\n    })];\n    this.subscriptions.push(...e2), this.emit(\"region-created\", t2);\n  }\n  addRegion(t2) {\n    var e2, i2;\n    if (!this.wavesurfer)\n      throw Error(\"WaveSurfer is not initialized\");\n    const s2 = this.wavesurfer.getDuration(), r = null === (i2 = null === (e2 = this.wavesurfer) || void 0 === e2 ? void 0 : e2.getDecodedData()) || void 0 === i2 ? void 0 : i2.numberOfChannels, o = new n(t2, s2, r);\n    return s2 ? this.saveRegion(o) : this.subscriptions.push(this.wavesurfer.once(\"ready\", (t3) => {\n      o._setTotalDuration(t3), this.saveRegion(o);\n    })), o;\n  }\n  enableDragSelection(t2) {\n    var e2, s2;\n    const r = null === (s2 = null === (e2 = this.wavesurfer) || void 0 === e2 ? void 0 : e2.getWrapper()) || void 0 === s2 ? void 0 : s2.querySelector(\"div\");\n    if (!r)\n      return () => {\n      };\n    let o = null, a = 0;\n    return i(r, (t3, e3, i2) => {\n      o && o._onUpdate(t3, i2 > a ? \"end\" : \"start\");\n    }, (e3) => {\n      var i2, s3;\n      if (a = e3, !this.wavesurfer)\n        return;\n      const r2 = this.wavesurfer.getDuration(), h = null === (s3 = null === (i2 = this.wavesurfer) || void 0 === i2 ? void 0 : i2.getDecodedData()) || void 0 === s3 ? void 0 : s3.numberOfChannels, l = this.wavesurfer.getWrapper().clientWidth, d = e3 / l * r2, u = (e3 + 5) / l * r2;\n      o = new n(Object.assign(Object.assign({}, t2), { start: d, end: u }), r2, h), this.regionsContainer.appendChild(o.element);\n    }, () => {\n      o && (this.saveRegion(o), o = null);\n    });\n  }\n  clearRegions() {\n    this.regions.forEach((t2) => t2.remove());\n  }\n  destroy() {\n    this.clearRegions(), super.destroy();\n  }\n}\nconst VolumeLevels = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { currentVolume } = $$props;\n  if ($$props.currentVolume === void 0 && $$bindings.currentVolume && currentVolume !== void 0)\n    $$bindings.currentVolume(currentVolume);\n  return `${currentVolume == 0 ? `${validate_component(VolumeMuted, \"VolumeMuted\").$$render($$result, {}, {}, {})}` : `${currentVolume < 0.5 ? `${validate_component(VolumeLow, \"VolumeLow\").$$render($$result, {}, {}, {})}` : `${currentVolume >= 0.5 ? `${validate_component(VolumeHigh, \"VolumeHigh\").$$render($$result, {}, {}, {})}` : ``}`}`}`;\n});\nconst css$2 = {\n  code: '.volume-slider.svelte-wuo8j5{-webkit-appearance:none;appearance:none;width:var(--size-20);accent-color:var(--color-accent);height:4px;cursor:pointer;outline:none;border-radius:15px;background-color:var(--neutral-400)}input[type=\"range\"].svelte-wuo8j5::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;height:15px;width:15px;background-color:var(--color-accent);border-radius:50%;border:none;transition:0.2s ease-in-out}input[type=\"range\"].svelte-wuo8j5::-moz-range-thumb{height:15px;width:15px;background-color:var(--color-accent);border-radius:50%;border:none;transition:0.2s ease-in-out}',\n  map: '{\"version\":3,\"file\":\"VolumeControl.svelte\",\"sources\":[\"VolumeControl.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nexport let currentVolume = 1;\\\\nexport let show_volume_slider = false;\\\\nexport let waveform;\\\\nlet volumeElement;\\\\nonMount(() => {\\\\n    adjustSlider();\\\\n});\\\\nconst adjustSlider = () => {\\\\n    let slider = volumeElement;\\\\n    if (!slider)\\\\n        return;\\\\n    slider.style.background = `linear-gradient(to right, var(--color-accent) ${currentVolume * 100}%, var(--neutral-400) ${currentVolume * 100}%)`;\\\\n};\\\\n$: currentVolume, adjustSlider();\\\\n<\\/script>\\\\n\\\\n<input\\\\n\\\\tbind:this={volumeElement}\\\\n\\\\tid=\\\\\"volume\\\\\"\\\\n\\\\tclass=\\\\\"volume-slider\\\\\"\\\\n\\\\ttype=\\\\\"range\\\\\"\\\\n\\\\tmin=\\\\\"0\\\\\"\\\\n\\\\tmax=\\\\\"1\\\\\"\\\\n\\\\tstep=\\\\\"0.01\\\\\"\\\\n\\\\tvalue={currentVolume}\\\\n\\\\ton:focusout={() => (show_volume_slider = false)}\\\\n\\\\ton:input={(e) => {\\\\n\\\\t\\\\tif (e.target instanceof HTMLInputElement) {\\\\n\\\\t\\\\t\\\\tcurrentVolume = parseFloat(e.target.value);\\\\n\\\\t\\\\t\\\\twaveform?.setVolume(currentVolume);\\\\n\\\\t\\\\t}\\\\n\\\\t}}\\\\n/>\\\\n\\\\n<style>\\\\n\\\\t.volume-slider {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\taccent-color: var(--color-accent);\\\\n\\\\t\\\\theight: 4px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\toutline: none;\\\\n\\\\t\\\\tborder-radius: 15px;\\\\n\\\\t\\\\tbackground-color: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\tinput[type=\\\\\"range\\\\\"]::-webkit-slider-thumb {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\theight: 15px;\\\\n\\\\t\\\\twidth: 15px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttransition: 0.2s ease-in-out;\\\\n\\\\t}\\\\n\\\\n\\\\tinput[type=\\\\\"range\\\\\"]::-moz-range-thumb {\\\\n\\\\t\\\\theight: 15px;\\\\n\\\\t\\\\twidth: 15px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttransition: 0.2s ease-in-out;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqCC,4BAAe,CACd,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,MAAM,CAAE,GAAG,CACX,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,CACnB,gBAAgB,CAAE,IAAI,aAAa,CACpC,CAEA,KAAK,CAAC,IAAI,CAAC,OAAO,eAAC,sBAAuB,CACzC,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAAC,WAClB,CAEA,KAAK,CAAC,IAAI,CAAC,OAAO,eAAC,kBAAmB,CACrC,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,CAAC,WAClB\"}'\n};\nconst VolumeControl = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { currentVolume = 1 } = $$props;\n  let { show_volume_slider = false } = $$props;\n  let { waveform } = $$props;\n  let volumeElement;\n  onMount(() => {\n    adjustSlider();\n  });\n  const adjustSlider = () => {\n    return;\n  };\n  if ($$props.currentVolume === void 0 && $$bindings.currentVolume && currentVolume !== void 0)\n    $$bindings.currentVolume(currentVolume);\n  if ($$props.show_volume_slider === void 0 && $$bindings.show_volume_slider && show_volume_slider !== void 0)\n    $$bindings.show_volume_slider(show_volume_slider);\n  if ($$props.waveform === void 0 && $$bindings.waveform && waveform !== void 0)\n    $$bindings.waveform(waveform);\n  $$result.css.add(css$2);\n  return `<input id=\"volume\" class=\"volume-slider svelte-wuo8j5\" type=\"range\" min=\"0\" max=\"1\" step=\"0.01\"${add_attribute(\"value\", currentVolume, 0)}${add_attribute(\"this\", volumeElement, 0)}>`;\n});\nconst css$1 = {\n  code: '.settings-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:self-end;align-items:center;grid-area:editing}.text-button.svelte-ije4bl.svelte-ije4bl{border:1px solid var(--neutral-400);border-radius:var(--radius-sm);font-weight:300;font-size:var(--size-3);text-align:center;color:var(--neutral-400);height:var(--size-5);font-weight:bold;padding:0 5px;margin-left:5px}.text-button.svelte-ije4bl.svelte-ije4bl:hover,.text-button.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent);border-color:var(--color-accent)}.controls.svelte-ije4bl.svelte-ije4bl{display:grid;grid-template-columns:1fr 1fr 1fr;grid-template-areas:\"controls playback editing\";margin-top:5px;align-items:center;position:relative;flex-wrap:wrap;justify-content:space-between}.controls.svelte-ije4bl div.svelte-ije4bl{margin:var(--size-1) 0}@media(max-width: 600px){.controls.svelte-ije4bl.svelte-ije4bl{grid-template-columns:1fr 1fr;grid-template-rows:auto auto;grid-template-areas:\"playback playback\"\\n\t\t\t\t\"controls editing\"}}@media(max-width: 319px){.controls.svelte-ije4bl.svelte-ije4bl{overflow-x:scroll}}.hidden.svelte-ije4bl.svelte-ije4bl{display:none}.control-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:self-start;align-items:center;justify-content:space-between;grid-area:controls}.action.svelte-ije4bl.svelte-ije4bl{width:var(--size-5);color:var(--neutral-400);margin-left:var(--spacing-md)}.icon.svelte-ije4bl.svelte-ije4bl:hover,.icon.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent)}.play-pause-wrapper.svelte-ije4bl.svelte-ije4bl{display:flex;justify-self:center;grid-area:playback}@media(max-width: 600px){.play-pause-wrapper.svelte-ije4bl.svelte-ije4bl{margin:var(--spacing-md)}}.playback.svelte-ije4bl.svelte-ije4bl{border:1px solid var(--neutral-400);border-radius:var(--radius-sm);width:5.5ch;font-weight:300;font-size:var(--size-3);text-align:center;color:var(--neutral-400);height:var(--size-5);font-weight:bold}.playback.svelte-ije4bl.svelte-ije4bl:hover,.playback.svelte-ije4bl.svelte-ije4bl:focus{color:var(--color-accent);border-color:var(--color-accent)}.rewind.svelte-ije4bl.svelte-ije4bl,.skip.svelte-ije4bl.svelte-ije4bl{margin:0 10px;color:var(--neutral-400)}.play-pause-button.svelte-ije4bl.svelte-ije4bl{width:var(--size-8);display:flex;align-items:center;justify-content:center;color:var(--neutral-400);fill:var(--neutral-400)}.volume.svelte-ije4bl.svelte-ije4bl{position:relative;display:flex;justify-content:center;margin-right:var(--spacing-xl);width:var(--size-5)}',\n  map: '{\"version\":3,\"file\":\"WaveformControls.svelte\",\"sources\":[\"WaveformControls.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Play, Pause, Forward, Backward, Undo, Trim } from \\\\\"@gradio/icons\\\\\";\\\\nimport { get_skip_rewind_amount } from \\\\\"../shared/utils\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport RegionsPlugin, {} from \\\\\"wavesurfer.js/dist/plugins/regions.js\\\\\";\\\\nimport VolumeLevels from \\\\\"./VolumeLevels.svelte\\\\\";\\\\nimport VolumeControl from \\\\\"./VolumeControl.svelte\\\\\";\\\\nexport let waveform;\\\\nexport let audio_duration;\\\\nexport let i18n;\\\\nexport let playing;\\\\nexport let show_redo = false;\\\\nexport let interactive = false;\\\\nexport let handle_trim_audio;\\\\nexport let mode = \\\\\"\\\\\";\\\\nexport let container;\\\\nexport let handle_reset_value;\\\\nexport let waveform_options = {};\\\\nexport let trim_region_settings = {};\\\\nexport let show_volume_slider = false;\\\\nexport let editable = true;\\\\nexport let trimDuration = 0;\\\\nlet playbackSpeeds = [0.5, 1, 1.5, 2];\\\\nlet playbackSpeed = playbackSpeeds[1];\\\\nlet trimRegion = null;\\\\nlet activeRegion = null;\\\\nlet leftRegionHandle;\\\\nlet rightRegionHandle;\\\\nlet activeHandle = \\\\\"\\\\\";\\\\nlet currentVolume = 1;\\\\n$: trimRegion = container && waveform ? waveform.registerPlugin(RegionsPlugin.create()) : null;\\\\n$: trimRegion?.on(\\\\\"region-out\\\\\", (region) => {\\\\n    region.play();\\\\n});\\\\n$: trimRegion?.on(\\\\\"region-updated\\\\\", (region) => {\\\\n    trimDuration = region.end - region.start;\\\\n});\\\\n$: trimRegion?.on(\\\\\"region-clicked\\\\\", (region, e) => {\\\\n    e.stopPropagation();\\\\n    activeRegion = region;\\\\n    region.play();\\\\n});\\\\nconst addTrimRegion = () => {\\\\n    if (!trimRegion)\\\\n        return;\\\\n    activeRegion = trimRegion?.addRegion({\\\\n        start: audio_duration / 4,\\\\n        end: audio_duration / 2,\\\\n        ...trim_region_settings\\\\n    });\\\\n    trimDuration = activeRegion.end - activeRegion.start;\\\\n};\\\\n$: if (activeRegion) {\\\\n    const shadowRoot = container.children[0].shadowRoot;\\\\n    rightRegionHandle = shadowRoot.querySelector(\\'[data-resize=\\\\\"right\\\\\"]\\');\\\\n    leftRegionHandle = shadowRoot.querySelector(\\'[data-resize=\\\\\"left\\\\\"]\\');\\\\n    if (leftRegionHandle && rightRegionHandle) {\\\\n        leftRegionHandle.setAttribute(\\\\\"role\\\\\", \\\\\"button\\\\\");\\\\n        rightRegionHandle.setAttribute(\\\\\"role\\\\\", \\\\\"button\\\\\");\\\\n        leftRegionHandle?.setAttribute(\\\\\"aria-label\\\\\", \\\\\"Drag to adjust start time\\\\\");\\\\n        rightRegionHandle?.setAttribute(\\\\\"aria-label\\\\\", \\\\\"Drag to adjust end time\\\\\");\\\\n        leftRegionHandle?.setAttribute(\\\\\"tabindex\\\\\", \\\\\"0\\\\\");\\\\n        rightRegionHandle?.setAttribute(\\\\\"tabindex\\\\\", \\\\\"0\\\\\");\\\\n        leftRegionHandle.addEventListener(\\\\\"focus\\\\\", () => {\\\\n            if (trimRegion)\\\\n                activeHandle = \\\\\"left\\\\\";\\\\n        });\\\\n        rightRegionHandle.addEventListener(\\\\\"focus\\\\\", () => {\\\\n            if (trimRegion)\\\\n                activeHandle = \\\\\"right\\\\\";\\\\n        });\\\\n    }\\\\n}\\\\nconst trimAudio = () => {\\\\n    if (waveform && trimRegion) {\\\\n        if (activeRegion) {\\\\n            const start = activeRegion.start;\\\\n            const end = activeRegion.end;\\\\n            handle_trim_audio(start, end);\\\\n            mode = \\\\\"\\\\\";\\\\n            activeRegion = null;\\\\n        }\\\\n    }\\\\n};\\\\nconst clearRegions = () => {\\\\n    trimRegion?.getRegions().forEach((region) => {\\\\n        region.remove();\\\\n    });\\\\n    trimRegion?.clearRegions();\\\\n};\\\\nconst toggleTrimmingMode = () => {\\\\n    clearRegions();\\\\n    if (mode === \\\\\"edit\\\\\") {\\\\n        mode = \\\\\"\\\\\";\\\\n    }\\\\n    else {\\\\n        mode = \\\\\"edit\\\\\";\\\\n        addTrimRegion();\\\\n    }\\\\n};\\\\nconst adjustRegionHandles = (handle, key) => {\\\\n    let newStart;\\\\n    let newEnd;\\\\n    if (!activeRegion)\\\\n        return;\\\\n    if (handle === \\\\\"left\\\\\") {\\\\n        if (key === \\\\\"ArrowLeft\\\\\") {\\\\n            newStart = activeRegion.start - 0.05;\\\\n            newEnd = activeRegion.end;\\\\n        }\\\\n        else {\\\\n            newStart = activeRegion.start + 0.05;\\\\n            newEnd = activeRegion.end;\\\\n        }\\\\n    }\\\\n    else {\\\\n        if (key === \\\\\"ArrowLeft\\\\\") {\\\\n            newStart = activeRegion.start;\\\\n            newEnd = activeRegion.end - 0.05;\\\\n        }\\\\n        else {\\\\n            newStart = activeRegion.start;\\\\n            newEnd = activeRegion.end + 0.05;\\\\n        }\\\\n    }\\\\n    activeRegion.setOptions({\\\\n        start: newStart,\\\\n        end: newEnd\\\\n    });\\\\n    trimDuration = activeRegion.end - activeRegion.start;\\\\n};\\\\n$: trimRegion && window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n    if (e.key === \\\\\"ArrowLeft\\\\\") {\\\\n        adjustRegionHandles(activeHandle, \\\\\"ArrowLeft\\\\\");\\\\n    }\\\\n    else if (e.key === \\\\\"ArrowRight\\\\\") {\\\\n        adjustRegionHandles(activeHandle, \\\\\"ArrowRight\\\\\");\\\\n    }\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"controls\\\\\" data-testid=\\\\\"waveform-controls\\\\\">\\\\n\\\\t<div class=\\\\\"control-wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"action icon volume\\\\\"\\\\n\\\\t\\\\t\\\\tstyle:color={show_volume_slider\\\\n\\\\t\\\\t\\\\t\\\\t? \\\\\"var(--color-accent)\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"var(--neutral-400)\\\\\"}\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Adjust volume\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => (show_volume_slider = !show_volume_slider)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<VolumeLevels {currentVolume} />\\\\n\\\\t\\\\t</button>\\\\n\\\\n\\\\t\\\\t{#if show_volume_slider}\\\\n\\\\t\\\\t\\\\t<VolumeControl bind:currentVolume bind:show_volume_slider {waveform} />\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass:hidden={show_volume_slider}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"playback icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label={`Adjust playback speed to ${\\\\n\\\\t\\\\t\\\\t\\\\tplaybackSpeeds[\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\\\\n\\\\t\\\\t\\\\t\\\\t]\\\\n\\\\t\\\\t\\\\t}x`}\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tplaybackSpeed =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tplaybackSpeeds[\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t];\\\\n\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.setPlaybackRate(playbackSpeed);\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<span>{playbackSpeed}x</span>\\\\n\\\\t\\\\t</button>\\\\n\\\\t</div>\\\\n\\\\n\\\\t<div class=\\\\\"play-pause-wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"rewind icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label={`Skip backwards by ${get_skip_rewind_amount(\\\\n\\\\t\\\\t\\\\t\\\\taudio_duration,\\\\n\\\\t\\\\t\\\\t\\\\twaveform_options.skip_length\\\\n\\\\t\\\\t\\\\t)} seconds`}\\\\n\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.skip(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length) *\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t-1\\\\n\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<Backward />\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"play-pause-button icon\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => waveform?.playPause()}\\\\n\\\\t\\\\t\\\\taria-label={playing ? i18n(\\\\\"audio.pause\\\\\") : i18n(\\\\\"audio.play\\\\\")}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if playing}\\\\n\\\\t\\\\t\\\\t\\\\t<Pause />\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<Play />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</button>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tclass=\\\\\"skip icon\\\\\"\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Skip forward by {get_skip_rewind_amount(\\\\n\\\\t\\\\t\\\\t\\\\taudio_duration,\\\\n\\\\t\\\\t\\\\t\\\\twaveform_options.skip_length\\\\n\\\\t\\\\t\\\\t)} seconds\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() =>\\\\n\\\\t\\\\t\\\\t\\\\twaveform?.skip(\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tget_skip_rewind_amount(audio_duration, waveform_options.skip_length)\\\\n\\\\t\\\\t\\\\t\\\\t)}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<Forward />\\\\n\\\\t\\\\t</button>\\\\n\\\\t</div>\\\\n\\\\n\\\\t<div class=\\\\\"settings-wrapper\\\\\">\\\\n\\\\t\\\\t{#if editable && interactive}\\\\n\\\\t\\\\t\\\\t{#if show_redo && mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"action icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Reset audio\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_reset_value();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclearRegions();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmode = \\\\\"\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Undo />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t{#if mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"action icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"Trim audio to selection\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={toggleTrimmingMode}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Trim />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"text-button\\\\\" on:click={trimAudio}>Trim</button>\\\\n\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"text-button\\\\\" on:click={toggleTrimmingMode}>Cancel</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.settings-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: self-end;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgrid-area: editing;\\\\n\\\\t}\\\\n\\\\t.text-button {\\\\n\\\\t\\\\tborder: 1px solid var(--neutral-400);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tfont-weight: 300;\\\\n\\\\t\\\\tfont-size: var(--size-3);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t\\\\tpadding: 0 5px;\\\\n\\\\t\\\\tmargin-left: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.text-button:hover,\\\\n\\\\t.text-button:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tgrid-template-columns: 1fr 1fr 1fr;\\\\n\\\\t\\\\tgrid-template-areas: \\\\\"controls playback editing\\\\\";\\\\n\\\\t\\\\tmargin-top: 5px;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t}\\\\n\\\\t.controls div {\\\\n\\\\t\\\\tmargin: var(--size-1) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) {\\\\n\\\\t\\\\t.controls {\\\\n\\\\t\\\\t\\\\tgrid-template-columns: 1fr 1fr;\\\\n\\\\t\\\\t\\\\tgrid-template-rows: auto auto;\\\\n\\\\t\\\\t\\\\tgrid-template-areas:\\\\n\\\\t\\\\t\\\\t\\\\t\\\\\"playback playback\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\\"controls editing\\\\\";\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 319px) {\\\\n\\\\t\\\\t.controls {\\\\n\\\\t\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.control-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: self-start;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tgrid-area: controls;\\\\n\\\\t}\\\\n\\\\n\\\\t.action {\\\\n\\\\t\\\\twidth: var(--size-5);\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\tmargin-left: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\t.icon:hover,\\\\n\\\\t.icon:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t}\\\\n\\\\t.play-pause-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-self: center;\\\\n\\\\t\\\\tgrid-area: playback;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 600px) {\\\\n\\\\t\\\\t.play-pause-wrapper {\\\\n\\\\t\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\t.playback {\\\\n\\\\t\\\\tborder: 1px solid var(--neutral-400);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\twidth: 5.5ch;\\\\n\\\\t\\\\tfont-weight: 300;\\\\n\\\\t\\\\tfont-size: var(--size-3);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t}\\\\n\\\\n\\\\t.playback:hover,\\\\n\\\\t.playback:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.rewind,\\\\n\\\\t.skip {\\\\n\\\\t\\\\tmargin: 0 10px;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.play-pause-button {\\\\n\\\\t\\\\twidth: var(--size-8);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\tfill: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.volume {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmargin-right: var(--spacing-xl);\\\\n\\\\t\\\\twidth: var(--size-5);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA6PC,6CAAkB,CACjB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,QAAQ,CACtB,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,OACZ,CACA,wCAAa,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,CAAC,CAAC,GAAG,CACd,WAAW,CAAE,GACd,CAEA,wCAAY,MAAM,CAClB,wCAAY,MAAO,CAClB,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,qCAAU,CACT,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAClC,mBAAmB,CAAE,2BAA2B,CAChD,UAAU,CAAE,GAAG,CACf,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,IAAI,CACf,eAAe,CAAE,aAClB,CACA,uBAAS,CAAC,iBAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,CACvB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qCAAU,CACT,qBAAqB,CAAE,GAAG,CAAC,GAAG,CAC9B,kBAAkB,CAAE,IAAI,CAAC,IAAI,CAC7B,mBAAmB,CAClB,mBAAmB;AACvB,IAAI,kBACF,CACD,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qCAAU,CACT,UAAU,CAAE,MACb,CACD,CAEA,mCAAQ,CACP,OAAO,CAAE,IACV,CAEA,4CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,UAAU,CACxB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,QACZ,CAEA,mCAAQ,CACP,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,WAAW,CAAE,IAAI,YAAY,CAC9B,CACA,iCAAK,MAAM,CACX,iCAAK,MAAO,CACX,KAAK,CAAE,IAAI,cAAc,CAC1B,CACA,+CAAoB,CACnB,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,MAAM,CACpB,SAAS,CAAE,QACZ,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,+CAAoB,CACnB,MAAM,CAAE,IAAI,YAAY,CACzB,CACD,CACA,qCAAU,CACT,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,WAAW,CAAE,IACd,CAEA,qCAAS,MAAM,CACf,qCAAS,MAAO,CACf,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,mCAAO,CACP,iCAAM,CACL,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,8CAAmB,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,IAAI,CAAE,IAAI,aAAa,CACxB,CAEA,mCAAQ,CACP,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,YAAY,CAAE,IAAI,YAAY,CAAC,CAC/B,KAAK,CAAE,IAAI,QAAQ,CACpB\"}'\n};\nconst WaveformControls = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { waveform } = $$props;\n  let { audio_duration } = $$props;\n  let { i18n } = $$props;\n  let { playing } = $$props;\n  let { show_redo = false } = $$props;\n  let { interactive = false } = $$props;\n  let { handle_trim_audio } = $$props;\n  let { mode = \"\" } = $$props;\n  let { container } = $$props;\n  let { handle_reset_value } = $$props;\n  let { waveform_options = {} } = $$props;\n  let { trim_region_settings = {} } = $$props;\n  let { show_volume_slider = false } = $$props;\n  let { editable = true } = $$props;\n  let { trimDuration = 0 } = $$props;\n  let playbackSpeeds = [0.5, 1, 1.5, 2];\n  let playbackSpeed = playbackSpeeds[1];\n  let trimRegion = null;\n  let activeRegion = null;\n  let leftRegionHandle;\n  let rightRegionHandle;\n  let activeHandle = \"\";\n  let currentVolume = 1;\n  const adjustRegionHandles = (handle, key) => {\n    let newStart;\n    let newEnd;\n    if (!activeRegion)\n      return;\n    if (handle === \"left\") {\n      if (key === \"ArrowLeft\") {\n        newStart = activeRegion.start - 0.05;\n        newEnd = activeRegion.end;\n      } else {\n        newStart = activeRegion.start + 0.05;\n        newEnd = activeRegion.end;\n      }\n    } else {\n      if (key === \"ArrowLeft\") {\n        newStart = activeRegion.start;\n        newEnd = activeRegion.end - 0.05;\n      } else {\n        newStart = activeRegion.start;\n        newEnd = activeRegion.end + 0.05;\n      }\n    }\n    activeRegion.setOptions({ start: newStart, end: newEnd });\n    trimDuration = activeRegion.end - activeRegion.start;\n  };\n  if ($$props.waveform === void 0 && $$bindings.waveform && waveform !== void 0)\n    $$bindings.waveform(waveform);\n  if ($$props.audio_duration === void 0 && $$bindings.audio_duration && audio_duration !== void 0)\n    $$bindings.audio_duration(audio_duration);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.playing === void 0 && $$bindings.playing && playing !== void 0)\n    $$bindings.playing(playing);\n  if ($$props.show_redo === void 0 && $$bindings.show_redo && show_redo !== void 0)\n    $$bindings.show_redo(show_redo);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.handle_trim_audio === void 0 && $$bindings.handle_trim_audio && handle_trim_audio !== void 0)\n    $$bindings.handle_trim_audio(handle_trim_audio);\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.trim_region_settings === void 0 && $$bindings.trim_region_settings && trim_region_settings !== void 0)\n    $$bindings.trim_region_settings(trim_region_settings);\n  if ($$props.show_volume_slider === void 0 && $$bindings.show_volume_slider && show_volume_slider !== void 0)\n    $$bindings.show_volume_slider(show_volume_slider);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.trimDuration === void 0 && $$bindings.trimDuration && trimDuration !== void 0)\n    $$bindings.trimDuration(trimDuration);\n  $$result.css.add(css$1);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    trimRegion = container && waveform ? waveform.registerPlugin(s.create()) : null;\n    {\n      trimRegion?.on(\"region-out\", (region) => {\n        region.play();\n      });\n    }\n    {\n      trimRegion?.on(\"region-updated\", (region) => {\n        trimDuration = region.end - region.start;\n      });\n    }\n    {\n      trimRegion?.on(\"region-clicked\", (region, e2) => {\n        e2.stopPropagation();\n        activeRegion = region;\n        region.play();\n      });\n    }\n    {\n      if (activeRegion) {\n        const shadowRoot = container.children[0].shadowRoot;\n        rightRegionHandle = shadowRoot.querySelector('[data-resize=\"right\"]');\n        leftRegionHandle = shadowRoot.querySelector('[data-resize=\"left\"]');\n        if (leftRegionHandle && rightRegionHandle) {\n          leftRegionHandle.setAttribute(\"role\", \"button\");\n          rightRegionHandle.setAttribute(\"role\", \"button\");\n          leftRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust start time\");\n          rightRegionHandle?.setAttribute(\"aria-label\", \"Drag to adjust end time\");\n          leftRegionHandle?.setAttribute(\"tabindex\", \"0\");\n          rightRegionHandle?.setAttribute(\"tabindex\", \"0\");\n          leftRegionHandle.addEventListener(\"focus\", () => {\n            if (trimRegion)\n              activeHandle = \"left\";\n          });\n          rightRegionHandle.addEventListener(\"focus\", () => {\n            if (trimRegion)\n              activeHandle = \"right\";\n          });\n        }\n      }\n    }\n    trimRegion && window.addEventListener(\"keydown\", (e2) => {\n      if (e2.key === \"ArrowLeft\") {\n        adjustRegionHandles(activeHandle, \"ArrowLeft\");\n      } else if (e2.key === \"ArrowRight\") {\n        adjustRegionHandles(activeHandle, \"ArrowRight\");\n      }\n    });\n    $$rendered = `<div class=\"controls svelte-ije4bl\" data-testid=\"waveform-controls\"><div class=\"control-wrapper svelte-ije4bl\"><button class=\"action icon volume svelte-ije4bl\" aria-label=\"Adjust volume\"${add_styles({\n      \"color\": show_volume_slider ? \"var(--color-accent)\" : \"var(--neutral-400)\"\n    })}>${validate_component(VolumeLevels, \"VolumeLevels\").$$render($$result, { currentVolume }, {}, {})}</button> ${show_volume_slider ? `${validate_component(VolumeControl, \"VolumeControl\").$$render(\n      $$result,\n      {\n        waveform,\n        currentVolume,\n        show_volume_slider\n      },\n      {\n        currentVolume: ($$value) => {\n          currentVolume = $$value;\n          $$settled = false;\n        },\n        show_volume_slider: ($$value) => {\n          show_volume_slider = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : ``} <button class=\"${[\"playback icon svelte-ije4bl\", show_volume_slider ? \"hidden\" : \"\"].join(\" \").trim()}\"${add_attribute(\"aria-label\", `Adjust playback speed to ${playbackSpeeds[(playbackSpeeds.indexOf(playbackSpeed) + 1) % playbackSpeeds.length]}x`, 0)}><span>${escape(playbackSpeed)}x</span></button></div> <div class=\"play-pause-wrapper svelte-ije4bl\"><button class=\"rewind icon svelte-ije4bl\"${add_attribute(\"aria-label\", `Skip backwards by ${get_skip_rewind_amount(audio_duration, waveform_options.skip_length)} seconds`, 0)}>${validate_component(Backward, \"Backward\").$$render($$result, {}, {}, {})}</button> <button class=\"play-pause-button icon svelte-ije4bl\"${add_attribute(\"aria-label\", playing ? i18n(\"audio.pause\") : i18n(\"audio.play\"), 0)}>${playing ? `${validate_component(Pause, \"Pause\").$$render($$result, {}, {}, {})}` : `${validate_component(Play, \"Play\").$$render($$result, {}, {}, {})}`}</button> <button class=\"skip icon svelte-ije4bl\" aria-label=\"${\"Skip forward by \" + escape(get_skip_rewind_amount(audio_duration, waveform_options.skip_length), true) + \" seconds\"}\">${validate_component(Forward, \"Forward\").$$render($$result, {}, {}, {})}</button></div> <div class=\"settings-wrapper svelte-ije4bl\">${editable && interactive ? `${show_redo && mode === \"\" ? `<button class=\"action icon svelte-ije4bl\" aria-label=\"Reset audio\">${validate_component(Undo, \"Undo\").$$render($$result, {}, {}, {})}</button>` : ``} ${mode === \"\" ? `<button class=\"action icon svelte-ije4bl\" aria-label=\"Trim audio to selection\">${validate_component(Trim, \"Trim\").$$render($$result, {}, {}, {})}</button>` : `<button class=\"text-button svelte-ije4bl\" data-svelte-h=\"svelte-1brf00d\">Trim</button> <button class=\"text-button svelte-ije4bl\" data-svelte-h=\"svelte-1r0ma01\">Cancel</button>`}` : ``}</div> </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst css = {\n  code: \".component-wrapper.svelte-19usgod{padding:var(--size-3);width:100%}::part(wrapper){margin-bottom:var(--size-2)}.timestamps.svelte-19usgod{display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--size-1) 0}#time.svelte-19usgod{color:var(--neutral-400)}#duration.svelte-19usgod{color:var(--neutral-400)}#trim-duration.svelte-19usgod{color:var(--color-accent);margin-right:var(--spacing-sm)}.waveform-container.svelte-19usgod{display:flex;align-items:center;justify-content:center;width:var(--size-full)}#waveform.svelte-19usgod{width:100%;height:100%;position:relative}.standard-player.svelte-19usgod{width:100%;padding:var(--size-2)}.hidden.svelte-19usgod{display:none}\",\n  map: '{\"version\":3,\"file\":\"AudioPlayer.svelte\",\"sources\":[\"AudioPlayer.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { Music } from \\\\\"@gradio/icons\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport { skip_audio, process_audio } from \\\\\"../shared/utils\\\\\";\\\\nimport WaveformControls from \\\\\"../shared/WaveformControls.svelte\\\\\";\\\\nimport { Empty } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { resolve_wasm_src } from \\\\\"@gradio/wasm/svelte\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport Hls from \\\\\"hls.js\\\\\";\\\\nexport let value = null;\\\\n$: url = value?.url;\\\\nexport let label;\\\\nexport let i18n;\\\\nexport let dispatch_blob = () => Promise.resolve();\\\\nexport let interactive = false;\\\\nexport let editable = true;\\\\nexport let trim_region_settings = {};\\\\nexport let waveform_settings;\\\\nexport let waveform_options;\\\\nexport let mode = \\\\\"\\\\\";\\\\nexport let loop;\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nlet container;\\\\nlet waveform;\\\\nlet playing = false;\\\\nlet timeRef;\\\\nlet durationRef;\\\\nlet audio_duration;\\\\nlet trimDuration = 0;\\\\nlet show_volume_slider = false;\\\\nlet audio_player;\\\\nlet stream_active = false;\\\\nconst dispatch = createEventDispatcher();\\\\n$: use_waveform = waveform_options.show_recording_waveform && !value?.is_stream;\\\\nconst create_waveform = () => {\\\\n    waveform = WaveSurfer.create({\\\\n        container,\\\\n        ...waveform_settings\\\\n    });\\\\n    resolve_wasm_src(value?.url).then((resolved_src) => {\\\\n        if (resolved_src && waveform) {\\\\n            return waveform.load(resolved_src);\\\\n        }\\\\n    });\\\\n};\\\\n$: if (use_waveform && container !== void 0 && container !== null) {\\\\n    if (waveform !== void 0)\\\\n        waveform.destroy();\\\\n    container.innerHTML = \\\\\"\\\\\";\\\\n    create_waveform();\\\\n    playing = false;\\\\n}\\\\n$: waveform?.on(\\\\\"decode\\\\\", (duration) => {\\\\n    audio_duration = duration;\\\\n    durationRef && (durationRef.textContent = format_time(duration));\\\\n});\\\\n$: waveform?.on(\\\\\"timeupdate\\\\\", (currentTime) => timeRef && (timeRef.textContent = format_time(currentTime)));\\\\n$: waveform?.on(\\\\\"ready\\\\\", () => {\\\\n    if (!waveform_settings.autoplay) {\\\\n        waveform?.stop();\\\\n    }\\\\n    else {\\\\n        waveform?.play();\\\\n    }\\\\n});\\\\n$: waveform?.on(\\\\\"finish\\\\\", () => {\\\\n    if (loop) {\\\\n        waveform?.play();\\\\n    }\\\\n    else {\\\\n        playing = false;\\\\n        dispatch(\\\\\"stop\\\\\");\\\\n    }\\\\n});\\\\n$: waveform?.on(\\\\\"pause\\\\\", () => {\\\\n    playing = false;\\\\n    dispatch(\\\\\"pause\\\\\");\\\\n});\\\\n$: waveform?.on(\\\\\"play\\\\\", () => {\\\\n    playing = true;\\\\n    dispatch(\\\\\"play\\\\\");\\\\n});\\\\n$: waveform?.on(\\\\\"load\\\\\", () => {\\\\n    dispatch(\\\\\"load\\\\\");\\\\n});\\\\nconst handle_trim_audio = async (start, end) => {\\\\n    mode = \\\\\"\\\\\";\\\\n    const decodedData = waveform?.getDecodedData();\\\\n    if (decodedData)\\\\n        await process_audio(decodedData, start, end, waveform_settings.sampleRate).then(async (trimmedBlob) => {\\\\n            await dispatch_blob([trimmedBlob], \\\\\"change\\\\\");\\\\n            waveform?.destroy();\\\\n            container.innerHTML = \\\\\"\\\\\";\\\\n        });\\\\n    dispatch(\\\\\"edit\\\\\");\\\\n};\\\\nasync function load_audio(data) {\\\\n    stream_active = false;\\\\n    await resolve_wasm_src(data).then((resolved_src) => {\\\\n        if (!resolved_src || value?.is_stream)\\\\n            return;\\\\n        if (waveform_options.show_recording_waveform) {\\\\n            waveform?.load(resolved_src);\\\\n        }\\\\n        else if (audio_player) {\\\\n            audio_player.src = resolved_src;\\\\n        }\\\\n    });\\\\n}\\\\n$: url && load_audio(url);\\\\nfunction load_stream(value2) {\\\\n    if (!value2 || !value2.is_stream || !value2.url)\\\\n        return;\\\\n    if (!audio_player)\\\\n        return;\\\\n    if (Hls.isSupported() && !stream_active) {\\\\n        const hls = new Hls({\\\\n            maxBufferLength: 1,\\\\n            maxMaxBufferLength: 1,\\\\n            lowLatencyMode: true\\\\n        });\\\\n        hls.loadSource(value2.url);\\\\n        hls.attachMedia(audio_player);\\\\n        hls.on(Hls.Events.MANIFEST_PARSED, function () {\\\\n            if (waveform_settings.autoplay)\\\\n                audio_player.play();\\\\n        });\\\\n        hls.on(Hls.Events.ERROR, function (event, data) {\\\\n            console.error(\\\\\"HLS error:\\\\\", event, data);\\\\n            if (data.fatal) {\\\\n                switch (data.type) {\\\\n                    case Hls.ErrorTypes.NETWORK_ERROR:\\\\n                        console.error(\\\\\"Fatal network error encountered, trying to recover\\\\\");\\\\n                        hls.startLoad();\\\\n                        break;\\\\n                    case Hls.ErrorTypes.MEDIA_ERROR:\\\\n                        console.error(\\\\\"Fatal media error encountered, trying to recover\\\\\");\\\\n                        hls.recoverMediaError();\\\\n                        break;\\\\n                    default:\\\\n                        console.error(\\\\\"Fatal error, cannot recover\\\\\");\\\\n                        hls.destroy();\\\\n                        break;\\\\n                }\\\\n            }\\\\n        });\\\\n        stream_active = true;\\\\n    }\\\\n    else if (!stream_active) {\\\\n        audio_player.src = value2.url;\\\\n        if (waveform_settings.autoplay)\\\\n            audio_player.play();\\\\n        stream_active = true;\\\\n    }\\\\n}\\\\n$: load_stream(value);\\\\nonMount(() => {\\\\n    window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n        if (!waveform || show_volume_slider)\\\\n            return;\\\\n        if (e.key === \\\\\"ArrowRight\\\\\" && mode !== \\\\\"edit\\\\\") {\\\\n            skip_audio(waveform, 0.1);\\\\n        }\\\\n        else if (e.key === \\\\\"ArrowLeft\\\\\" && mode !== \\\\\"edit\\\\\") {\\\\n            skip_audio(waveform, -0.1);\\\\n        }\\\\n    });\\\\n});\\\\n<\\/script>\\\\n\\\\n<audio\\\\n\\\\tclass=\\\\\"standard-player\\\\\"\\\\n\\\\tclass:hidden={use_waveform}\\\\n\\\\tcontrols\\\\n\\\\tautoplay={waveform_settings.autoplay}\\\\n\\\\ton:load\\\\n\\\\tbind:this={audio_player}\\\\n\\\\ton:ended={() => dispatch(\\\\\"stop\\\\\")}\\\\n\\\\ton:play={() => dispatch(\\\\\"play\\\\\")}\\\\n/>\\\\n{#if value === null}\\\\n\\\\t<Empty size=\\\\\"small\\\\\">\\\\n\\\\t\\\\t<Music />\\\\n\\\\t</Empty>\\\\n{:else if use_waveform}\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"component-wrapper\\\\\"\\\\n\\\\t\\\\tdata-testid={label ? \\\\\"waveform-\\\\\" + label : \\\\\"unlabelled-audio\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t<div class=\\\\\"waveform-container\\\\\">\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tid=\\\\\"waveform\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={container}\\\\n\\\\t\\\\t\\\\t\\\\tstyle:height={container ? null : \\\\\"58px\\\\\"}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t<div class=\\\\\"timestamps\\\\\">\\\\n\\\\t\\\\t\\\\t<time bind:this={timeRef} id=\\\\\"time\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t<div>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"edit\\\\\" && trimDuration > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time id=\\\\\"trim-duration\\\\\">{format_time(trimDuration)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t<time bind:this={durationRef} id=\\\\\"duration\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\n\\\\t\\\\t<WaveformControls\\\\n\\\\t\\\\t\\\\t{container}\\\\n\\\\t\\\\t\\\\t{waveform}\\\\n\\\\t\\\\t\\\\t{playing}\\\\n\\\\t\\\\t\\\\t{audio_duration}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{interactive}\\\\n\\\\t\\\\t\\\\t{handle_trim_audio}\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\tbind:trimDuration\\\\n\\\\t\\\\t\\\\tbind:show_volume_slider\\\\n\\\\t\\\\t\\\\tshow_redo={interactive}\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{trim_region_settings}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t/>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.component-wrapper {\\\\n\\\\t\\\\tpadding: var(--size-3);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(wrapper)) {\\\\n\\\\t\\\\tmargin-bottom: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.timestamps {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-1) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t#time {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t#duration {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t#trim-duration {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\t.waveform-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t#waveform {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.standard-player {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAsOC,iCAAmB,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IACR,CAEQ,eAAiB,CACxB,aAAa,CAAE,IAAI,QAAQ,CAC5B,CAEA,0BAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,CACxB,CAEA,oBAAM,CACL,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,6BAAe,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,YAAY,CAC/B,CACA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,wBAAU,CACT,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QACX,CAEA,+BAAiB,CAChB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CACtB,CAEA,sBAAQ,CACP,OAAO,CAAE,IACV\"}'\n};\nconst AudioPlayer = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let url;\n  let use_waveform;\n  let { value = null } = $$props;\n  let { label } = $$props;\n  let { i18n } = $$props;\n  let { dispatch_blob = () => Promise.resolve() } = $$props;\n  let { interactive = false } = $$props;\n  let { editable = true } = $$props;\n  let { trim_region_settings = {} } = $$props;\n  let { waveform_settings } = $$props;\n  let { waveform_options } = $$props;\n  let { mode = \"\" } = $$props;\n  let { loop } = $$props;\n  let { handle_reset_value = () => {\n  } } = $$props;\n  let container;\n  let waveform;\n  let playing = false;\n  let timeRef;\n  let durationRef;\n  let audio_duration;\n  let trimDuration = 0;\n  let show_volume_slider = false;\n  let audio_player;\n  const dispatch = createEventDispatcher();\n  const create_waveform = () => {\n    waveform = WaveSurfer.create({ container, ...waveform_settings });\n    resolve_wasm_src(value?.url).then((resolved_src) => {\n      if (resolved_src && waveform) {\n        return waveform.load(resolved_src);\n      }\n    });\n  };\n  const handle_trim_audio = async (start, end) => {\n    mode = \"\";\n    const decodedData = waveform?.getDecodedData();\n    if (decodedData)\n      await process_audio(decodedData, start, end, waveform_settings.sampleRate).then(async (trimmedBlob) => {\n        await dispatch_blob([trimmedBlob], \"change\");\n        waveform?.destroy();\n        container.innerHTML = \"\";\n      });\n    dispatch(\"edit\");\n  };\n  async function load_audio(data) {\n    await resolve_wasm_src(data).then((resolved_src) => {\n      if (!resolved_src || value?.is_stream)\n        return;\n      if (waveform_options.show_recording_waveform) {\n        waveform?.load(resolved_src);\n      }\n    });\n  }\n  function load_stream(value2) {\n    if (!value2 || !value2.is_stream || !value2.url)\n      return;\n    return;\n  }\n  onMount(() => {\n    window.addEventListener(\"keydown\", (e2) => {\n      if (!waveform || show_volume_slider)\n        return;\n      if (e2.key === \"ArrowRight\" && mode !== \"edit\") {\n        skip_audio(waveform, 0.1);\n      } else if (e2.key === \"ArrowLeft\" && mode !== \"edit\") {\n        skip_audio(waveform, -0.1);\n      }\n    });\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.dispatch_blob === void 0 && $$bindings.dispatch_blob && dispatch_blob !== void 0)\n    $$bindings.dispatch_blob(dispatch_blob);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.trim_region_settings === void 0 && $$bindings.trim_region_settings && trim_region_settings !== void 0)\n    $$bindings.trim_region_settings(trim_region_settings);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    url = value?.url;\n    use_waveform = waveform_options.show_recording_waveform && !value?.is_stream;\n    {\n      if (use_waveform && container !== void 0 && container !== null) {\n        if (waveform !== void 0)\n          waveform.destroy();\n        container.innerHTML = \"\";\n        create_waveform();\n        playing = false;\n      }\n    }\n    {\n      waveform?.on(\"decode\", (duration) => {\n        audio_duration = duration;\n      });\n    }\n    {\n      waveform?.on(\"timeupdate\", (currentTime) => timeRef);\n    }\n    {\n      waveform?.on(\"ready\", () => {\n        if (!waveform_settings.autoplay) {\n          waveform?.stop();\n        } else {\n          waveform?.play();\n        }\n      });\n    }\n    {\n      waveform?.on(\"finish\", () => {\n        if (loop) {\n          waveform?.play();\n        } else {\n          playing = false;\n          dispatch(\"stop\");\n        }\n      });\n    }\n    {\n      waveform?.on(\"pause\", () => {\n        playing = false;\n        dispatch(\"pause\");\n      });\n    }\n    {\n      waveform?.on(\"play\", () => {\n        playing = true;\n        dispatch(\"play\");\n      });\n    }\n    {\n      waveform?.on(\"load\", () => {\n        dispatch(\"load\");\n      });\n    }\n    url && load_audio(url);\n    {\n      load_stream(value);\n    }\n    $$rendered = `<audio class=\"${[\"standard-player svelte-19usgod\", use_waveform ? \"hidden\" : \"\"].join(\" \").trim()}\" controls ${waveform_settings.autoplay ? \"autoplay\" : \"\"}${add_attribute(\"this\", audio_player, 0)}></audio> ${value === null ? `${validate_component(Empty, \"Empty\").$$render($$result, { size: \"small\" }, {}, {\n      default: () => {\n        return `${validate_component(Music, \"Music\").$$render($$result, {}, {}, {})}`;\n      }\n    })}` : `${use_waveform ? `<div class=\"component-wrapper svelte-19usgod\"${add_attribute(\"data-testid\", label ? \"waveform-\" + label : \"unlabelled-audio\", 0)}><div class=\"waveform-container svelte-19usgod\"><div id=\"waveform\" class=\"svelte-19usgod\"${add_styles({ \"height\": \"58px\" })}${add_attribute(\"this\", container, 0)}></div></div> <div class=\"timestamps svelte-19usgod\"><time id=\"time\" class=\"svelte-19usgod\"${add_attribute(\"this\", timeRef, 0)} data-svelte-h=\"svelte-lp3mlp\">0:00</time> <div>${mode === \"edit\" && trimDuration > 0 ? `<time id=\"trim-duration\" class=\"svelte-19usgod\">${escape(format_time(trimDuration))}</time>` : ``} <time id=\"duration\" class=\"svelte-19usgod\"${add_attribute(\"this\", durationRef, 0)} data-svelte-h=\"svelte-1jd0owv\">0:00</time></div></div> ${validate_component(WaveformControls, \"WaveformControls\").$$render(\n      $$result,\n      {\n        container,\n        waveform,\n        playing,\n        audio_duration,\n        i18n,\n        interactive,\n        handle_trim_audio,\n        show_redo: interactive,\n        handle_reset_value,\n        waveform_options,\n        trim_region_settings,\n        editable,\n        mode,\n        trimDuration,\n        show_volume_slider\n      },\n      {\n        mode: ($$value) => {\n          mode = $$value;\n          $$settled = false;\n        },\n        trimDuration: ($$value) => {\n          trimDuration = $$value;\n          $$settled = false;\n        },\n        show_volume_slider: ($$value) => {\n          show_volume_slider = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}</div>` : ``}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst AudioPlayer$1 = AudioPlayer;\nexport {\n  AudioPlayer$1 as A,\n  skip_audio as s\n};\n"], "names": [], "mappings": ";;;;AAMA,IAAI,WAAW,GAAG,SAAS,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC9D,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACxB,IAAI,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE;AAChE,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAC5D,IAAI,SAAS,SAAS,CAAC,KAAK,EAAE;AAC9B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC7B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAgB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAAS,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE;AACvC,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,IAAI,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;AACtD,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AACxD,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AACnD,GAAG,CAAC,CAAC;AACL,CAAC;AACD,SAAS,SAAS,CAAC,WAAW,EAAE;AAChC,EAAE,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;AACpD,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AACvC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,EAAE,EAAE,EAAE,EAAE;AACxC,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,MAAM,IAAI,IAAI,GAAG,GAAG;AACpB,QAAQ,GAAG,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE;AACvC,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,EAAE,EAAE,EAAE,EAAE;AAC1C,QAAQ,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC;AACD,SAAS,YAAY,CAAC,WAAW,EAAE,QAAQ,EAAE;AAC7C,EAAE,IAAI,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ;AACxC,IAAI,WAAW,GAAG,CAAC,WAAW,CAAC,CAAC;AAChC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;AACzB,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM;AACjC,IAAI,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ;AAChD,IAAI,gBAAgB,EAAE,WAAW,CAAC,MAAM;AACxC,IAAI,cAAc,EAAE,CAAC,EAAE,KAAK,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC;AACrG,IAAI,eAAe,EAAE,WAAW,CAAC,SAAS,CAAC,eAAe;AAC1D,IAAI,aAAa,EAAE,WAAW,CAAC,SAAS,CAAC,aAAa;AACtD,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,OAAO,GAAG;AAChB,EAAE,MAAM;AACR,EAAE,YAAY;AACd,CAAC,CAAC;AACF,IAAI,WAAW,GAAG,SAAS,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC9D,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACxB,IAAI,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE;AAChE,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAC5D,IAAI,SAAS,SAAS,CAAC,KAAK,EAAE;AAC9B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC7B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAgB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,SAAS,SAAS,CAAC,GAAG,EAAE,gBAAgB,EAAE,WAAW,EAAE;AACvD,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACb,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACnD,IAAI;AACJ,MAAM,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;AACtG,MAAM,MAAM,aAAa,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC1H,MAAM,IAAI,cAAc,GAAG,CAAC,CAAC;AAC7B,MAAM,MAAM,YAAY,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAC3F,QAAQ,IAAI,IAAI;AAChB,UAAU,OAAO;AACjB,QAAQ,cAAc,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAC5F,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,aAAa,GAAG,GAAG,CAAC,CAAC;AAC5E,QAAQ,gBAAgB,CAAC,UAAU,CAAC,CAAC;AACrC,QAAQ,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AACnJ,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACzH,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC3B,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,OAAO,GAAG;AAChB,EAAE,SAAS;AACX,CAAC,CAAC;AACF,MAAM,YAAY,CAAC;AACnB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpC,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACvC,GAAG;AACH;AACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAChC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACxC,IAAI,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE;AACxE,MAAM,MAAM,eAAe,GAAG,MAAM;AACpC,QAAQ,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;AACzD,QAAQ,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAClD,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;AACpD,MAAM,OAAO,eAAe,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE;AACvC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC1F,GAAG;AACH;AACA,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACpD,GAAG;AACH;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE;AAC3B,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACnC,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACzE,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,MAAM,SAAS,YAAY,CAAC;AAClC,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;AACjC,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AACjC,MAAM,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAClC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,aAAa,EAAE;AAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC1B,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;AACtC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM;AAC3C,QAAQ,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;AAC1C,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AACzD,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;AACzC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1D,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AACzD,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9B,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AACjC,MAAM,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE;AACpB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC9B,IAAI,IAAI,GAAG,KAAK,GAAG;AACnB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;AACrB,IAAI,MAAM,MAAM,GAAG,IAAI,YAAY,IAAI,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AAC1E,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;AAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACvB,IAAI,IAAI,IAAI,CAAC,eAAe;AAC5B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,eAAe,CAAC,OAAO,EAAE;AAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AACzB,GAAG;AACH;AACA,EAAE,IAAI,GAAG;AACT,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACvB,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AACnD,GAAG;AACH;AACA,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;AAClC,GAAG;AACH;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AAClC,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AACnC,GAAG;AACH;AACA,EAAE,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE;AACvC,IAAI,IAAI,aAAa,IAAI,IAAI,EAAE;AAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,aAAa,CAAC;AAChD,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AACnC,GAAG;AACH;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG;AACH;AACA,EAAE,SAAS,CAAC,MAAM,EAAE;AACpB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACnC,GAAG;AACH,CAAC;AACD,SAAS,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,GAAG,CAAC,EAAE;AACvE,EAAE,IAAI,KAAK,GAAG,MAAM;AACpB,IAAI,OAAO;AACX,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,MAAM,IAAI,GAAG,CAAC,EAAE,KAAK;AACvB,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC;AACvB,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,cAAc,EAAE,CAAC;AACxB,IAAI,EAAE,CAAC,eAAe,EAAE,CAAC;AACzB,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC;AAC5B,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC;AAC5B,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAI,MAAM,IAAI,GAAG,CAAC,EAAE,KAAK;AACzB,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,EAAE,CAAC,eAAe,EAAE,CAAC;AAC3B,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;AAC3B,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;AAC3B,MAAM,IAAI,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,SAAS,EAAE;AAChG,QAAQ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;AAC9D,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,UAAU,UAAU,GAAG,IAAI,CAAC;AAC5B,UAAU,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,CAAC;AACjG,SAAS;AACT,QAAQ,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAQ,MAAM,GAAG,CAAC,CAAC;AACnB,QAAQ,MAAM,GAAG,CAAC,CAAC;AACnB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,KAAK,GAAG,CAAC,EAAE,KAAK;AAC1B,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,EAAE,CAAC,cAAc,EAAE,CAAC;AAC5B,QAAQ,EAAE,CAAC,eAAe,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,EAAE,GAAG,MAAM;AACrB,MAAM,OAAO,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC;AACrC,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC;AAC9D,OAAO;AACP,MAAM,KAAK,EAAE,CAAC;AACd,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC/C,IAAI,QAAQ,CAAC,gBAAgB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;AAClD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,KAAK,GAAG,MAAM;AAClB,MAAM,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACxD,MAAM,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACpD,MAAM,QAAQ,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;AACvD,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3D,OAAO,EAAE,EAAE,CAAC,CAAC;AACb,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AAChD,EAAE,OAAO,MAAM;AACf,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,OAAO,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACrD,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,QAAQ,SAAS,YAAY,CAAC;AACpC,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE;AACrC,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACtE,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC1C,IAAI,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACzB,IAAI,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAClD,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,0BAA0B,CAAC,SAAS,EAAE;AACxC,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;AACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AACjD,KAAK,MAAM,IAAI,SAAS,YAAY,WAAW,EAAE;AACjD,MAAM,MAAM,GAAG,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,gBAAgB,GAAG,CAAC,EAAE,KAAK;AACrC,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;AACxD,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AACvC,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AACvC,MAAM,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC,MAAM,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxC,MAAM,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK;AACnD,MAAM,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,EAAE,KAAK;AACtD,MAAM,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACjC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;AAC1D,MAAM,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AAC5E,MAAM,MAAM,MAAM,GAAG,UAAU,GAAG,WAAW,CAAC;AAC9C,MAAM,MAAM,IAAI,GAAG,CAAC,UAAU,GAAG,WAAW,IAAI,WAAW,CAAC;AAC5D,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM;AACnD,MAAM,KAAK,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,aAAa;AACjB,MAAM,IAAI,CAAC,OAAO;AAClB;AACA,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK;AACpB,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpG,OAAO;AACP;AACA,MAAM,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI;AAClC;AACA,MAAM,MAAM,IAAI,CAAC,UAAU,GAAG,KAAK;AACnC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC;AAC9B,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI;AACnC,MAAM,OAAO,aAAa,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3C,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM;AACtC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,aAAa,CAAC;AACvD,IAAI,OAAO,aAAa,CAAC;AACzB,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AACtD,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,EAAE;AACtD,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC3E,MAAM,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5C,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACxD,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;AAC3C,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AAC5B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;AACpF,GAAG;AACH,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO,CAAC,QAAQ,KAAK;AACzB,MAAM,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvD,MAAM,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACtD,KAAK,CAAC;AACN,GAAG;AACH;AACA,EAAE,kBAAkB,CAAC,KAAK,EAAE;AAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7B,MAAM,OAAO,KAAK,IAAI,EAAE,CAAC;AACzB,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;AACxB,MAAM,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5B,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC3D,IAAI,MAAM,GAAG,GAAG,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;AAC7E,IAAI,MAAM,mBAAmB,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACrC,MAAM,MAAM,MAAM,GAAG,KAAK,GAAG,mBAAmB,CAAC;AACjD,MAAM,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC5C,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,iBAAiB,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE;AACvD,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACrC,IAAI,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;AAClC,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC;AAC1E,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,UAAU,GAAG,OAAO,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;AACtG,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;AAC7C,IAAI,MAAM,aAAa,GAAG,KAAK,IAAI,QAAQ,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;AAC/D,IAAI,MAAM,MAAM,GAAG,SAAS,IAAI,WAAW,IAAI,GAAG,GAAG,WAAW,GAAG,MAAM,CAAC;AAC1E,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;AACpB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;AACtB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,MAAM,EAAE,EAAE,EAAE,EAAE;AACzC,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,aAAa,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,GAAG,KAAK,EAAE;AACrB,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC;AACtE,QAAQ,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC;AAC5E,QAAQ,MAAM,SAAS,GAAG,YAAY,GAAG,eAAe,IAAI,CAAC,CAAC;AAC9D,QAAQ,IAAI,CAAC,GAAG,UAAU,GAAG,YAAY,CAAC;AAC1C,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;AACxC,UAAU,CAAC,GAAG,CAAC,CAAC;AAChB,SAAS,MAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAClD,UAAU,CAAC,GAAG,MAAM,GAAG,SAAS,CAAC;AACjC,SAAS;AACT,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,IAAI,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACpF,QAAQ,KAAK,GAAG,CAAC,CAAC;AAClB,QAAQ,MAAM,GAAG,CAAC,CAAC;AACnB,QAAQ,SAAS,GAAG,CAAC,CAAC;AACtB,OAAO;AACP,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD,MAAM,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/D,MAAM,IAAI,YAAY,GAAG,MAAM;AAC/B,QAAQ,MAAM,GAAG,YAAY,CAAC;AAC9B,MAAM,IAAI,eAAe,GAAG,SAAS;AACrC,QAAQ,SAAS,GAAG,eAAe,CAAC;AACpC,KAAK;AACL,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;AACf,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,kBAAkB,CAAC,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE;AACzD,IAAI,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACnC,MAAM,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAC3D,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACpC,MAAM,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;AACpC,MAAM,MAAM,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;AACpC,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC;AAC/C,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAChC,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;AACpB,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAClB,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,MAAM,EAAE,EAAE,EAAE,EAAE;AAC3C,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC;AAC1C,QAAQ,IAAI,CAAC,GAAG,KAAK,EAAE;AACvB,UAAU,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/D,UAAU,MAAM,CAAC,GAAG,UAAU,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5D,UAAU,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC/B,UAAU,KAAK,GAAG,CAAC,CAAC;AACpB,UAAU,GAAG,GAAG,CAAC,CAAC;AAClB,SAAS;AACT,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,QAAQ,IAAI,KAAK,GAAG,GAAG;AACvB,UAAU,GAAG,GAAG,KAAK,CAAC;AACtB,OAAO;AACP,MAAM,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;AACpB,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AACnB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;AACf,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE;AAC5C,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,IAAI,OAAO,CAAC,cAAc,EAAE;AAChC,MAAM,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AAC/C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC;AACxC,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE;AAC3B,MAAM,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzG,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE;AAChE,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAChE,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/D,GAAG;AACH,EAAE,kBAAkB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,eAAe,EAAE,iBAAiB,EAAE;AAC1G,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;AAC9D,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AACxC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACtE,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/E,IAAI,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACxC,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;AAC/F,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,MAAM,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AAChD,MAAM,MAAM,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1D,MAAM,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,MAAM,WAAW,CAAC,wBAAwB,GAAG,WAAW,CAAC;AACzD,MAAM,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7E,MAAM,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9D,MAAM,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACpD,KAAK;AACL,GAAG;AACH,EAAE,aAAa,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE;AAC7C,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1D,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;AACpC,IAAI,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AACpD,IAAI,MAAM,iBAAiB,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;AAC1D,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACxD,IAAI,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1E,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACtC,IAAI,MAAM,KAAK,GAAG,GAAG,GAAG,WAAW,CAAC;AACpC,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;AACzE,IAAI,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5C,MAAM,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,GAAG,CAAC;AAC/C,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,CAAC;AACpD,MAAM,MAAM,aAAa,GAAG,QAAQ,GAAG,MAAM,CAAC;AAC9C,MAAM,IAAI,aAAa,GAAG,aAAa,KAAK,CAAC,EAAE;AAC/C,QAAQ,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC,GAAG,aAAa,CAAC;AAClF,OAAO;AACP,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3D,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,KAAK,CAAC,CAAC;AAC1D,IAAI,MAAM,WAAW,GAAG,GAAG,GAAG,KAAK,CAAC;AACpC,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,KAAK;AACnC,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,eAAe,EAAE,iBAAiB,CAAC,CAAC;AACjJ,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACzC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACzC,IAAI,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,OAAO,KAAK;AAC/C,MAAM,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC/B,MAAM,IAAI,SAAS,GAAG,CAAC,EAAE;AACzB,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,UAAU,CAAC,SAAS,GAAG,WAAW,EAAE,OAAO,GAAG,WAAW,CAAC,CAAC;AACrE,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,OAAO,KAAK;AAC/C,MAAM,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC/B,MAAM,IAAI,OAAO,GAAG,GAAG,EAAE;AACzB,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,UAAU,CAAC,SAAS,GAAG,WAAW,EAAE,OAAO,GAAG,WAAW,CAAC,CAAC;AACrE,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE;AACnB,MAAM,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,WAAW,CAAC,CAAC;AACzC,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AACzF,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE,CAAC;AACtC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,EAAE,CAAC;AACxC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;AACpC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACjI,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;AACzD,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;AACxF,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;AACjD,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;AACxE,IAAI,MAAM,KAAK,GAAG,CAAC,cAAc,GAAG,WAAW,GAAG,WAAW,IAAI,UAAU,CAAC;AAC5E,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,GAAG,MAAM,GAAG,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,GAAG,MAAM,GAAG,QAAQ,CAAC;AAChF,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AACpG,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC9D,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;AACpC,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE;AAC9D,QAAQ,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,QAAQ,IAAI,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,OAAO;AACP,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,MAAM,IAAI,SAAS,CAAC,gBAAgB,GAAG,CAAC;AACxC,QAAQ,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxB,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS;AACvB,MAAM,OAAO;AACb,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;AAC/D,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChC,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;AAChE,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,kBAAkB,GAAG,iBAAiB,CAAC;AAC9E,GAAG;AACH,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,cAAc,CAAC,QAAQ,EAAE,SAAS,GAAG,KAAK,EAAE;AAC9C,IAAI,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1E,IAAI,MAAM,aAAa,GAAG,WAAW,GAAG,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC;AACnC,IAAI,MAAM,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,MAAM,GAAG,WAAW,CAAC;AACtG,IAAI,IAAI,aAAa,GAAG,UAAU,GAAG,SAAS,IAAI,aAAa,GAAG,UAAU,EAAE;AAC9E,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACvD,QAAQ,MAAM,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC;AACpC,QAAQ,IAAI,aAAa,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,OAAO,IAAI,aAAa,GAAG,UAAU,GAAG,WAAW,EAAE;AAC1G,UAAU,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,OAAO,CAAC;AACrD,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,aAAa,GAAG,MAAM,CAAC;AACnE,SAAS;AACT,OAAO,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;AAClC,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC;AACvB,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa,GAAG,GAAG,GAAG,aAAa,GAAG,WAAW,GAAG,GAAG,CAAC;AAC/H,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,aAAa,CAAC;AACxD,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;AAC/D,MAAM,MAAM,MAAM,GAAG,WAAW,GAAG,WAAW,CAAC;AAC/C,MAAM,MAAM,IAAI,GAAG,CAAC,WAAW,GAAG,WAAW,IAAI,WAAW,CAAC;AAC7D,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE;AACtC,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC;AACvB,MAAM,OAAO;AACb,IAAI,MAAM,QAAQ,GAAG,QAAQ,GAAG,GAAG,CAAC;AACpC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,wBAAwB,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACxG,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AACxG,IAAI,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;AACrD,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,gBAAgB,GAAG,GAAG,CAAC;AAChC,MAAM,KAAK,SAAS,YAAY,CAAC;AACjC,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM;AAC7C,MAAM,qBAAqB,CAAC,MAAM;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,GAAG;AACH,CAAC;AACD,IAAI,WAAW,GAAG,SAAS,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC9D,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACxB,IAAI,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE;AAChE,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAC5D,IAAI,SAAS,SAAS,CAAC,KAAK,EAAE;AAC9B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC7B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAgB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAM,cAAc,SAAS,YAAY,CAAC;AAC1C,EAAE,WAAW,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,EAAE;AACjD,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;AACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAC1D,MAAM,OAAO;AACb,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,GAAG,CAAC,KAAK,EAAE;AACjB,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK;AACxJ,MAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;AAChC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3B,MAAM,IAAI,IAAI,CAAC,QAAQ;AACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;AACpB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC;AAChF,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;AAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,IAAI,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC9C,MAAM,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC9E,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AACvD,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,MAAM;AACpC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7C,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,IAAI,CAAC,MAAM;AACnB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AAC1E,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;AAC9E,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAC1D,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,SAAS,CAAC,QAAQ,EAAE;AACtB,IAAI,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAC1D,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC;AACnC,MAAM,OAAO,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACpC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACvI,GAAG;AACH,EAAE,IAAI,YAAY,CAAC,KAAK,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;AACzB,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACjD,KAAK;AACL,GAAG;AACH,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC;AACxH,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,KAAK,EAAE;AACzB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAClC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,MAAM,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAClC,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,CAAC,CAAC;AACtF,GAAG;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AACpC,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK;AAC7B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AAC3D,KAAK;AACL,GAAG;AACH;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG;AACH,CAAC;AACD,IAAI,SAAS,GAAG,SAAS,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE;AAC5D,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACxB,IAAI,OAAO,KAAK,YAAY,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE;AAChE,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE,SAAS,OAAO,EAAE,MAAM,EAAE;AAC5D,IAAI,SAAS,SAAS,CAAC,KAAK,EAAE;AAC9B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC7B,MAAM,IAAI;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,CAAC,EAAE,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAgB,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,GAAG,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAM,cAAc,GAAG;AACvB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,aAAa,EAAE,MAAM;AACvB,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,UAAU,EAAE,KAAK;AACnB,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,UAAU,EAAE,GAAG;AACjB,CAAC,CAAC;AACF,MAAM,UAAU,SAAS,MAAM,CAAC;AAChC;AACA,EAAE,OAAO,MAAM,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;AACnC,GAAG;AACH;AACA,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,OAAO,KAAK,UAAU,GAAG,IAAI,cAAc,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACpG,IAAI,KAAK,CAAC;AACV,MAAM,KAAK;AACX,MAAM,aAAa,EAAE,OAAO,CAAC,aAAa;AAC1C,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,MAAM,YAAY,EAAE,OAAO,CAAC,SAAS;AACrC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC7B,IAAI,MAAM,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;AACjE,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClD,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAChE,KAAK,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AAC5D,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC5B,KAAK;AACL,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM;AACxD,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAChD,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC;AAC3E,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;AAC7C,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM;AACvE,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAChD,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AACvF,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAC3C,KAAK,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM;AACxC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACzB,KAAK,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM;AACzC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACxB,KAAK,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM;AAC3C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AACxB,KAAK,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM;AACzC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1B,KAAK,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM;AAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;AAClD,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI;AAC3B;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK;AAC1D,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACnC,UAAU,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjC,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACnE,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACnD,SAAS;AACT,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK;AAC7D,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACpD,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,KAAK;AACnD,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC5C,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,IAAI,GAAG,QAAQ,CAAC,CAAC;AAChE,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,MAAM;AACvC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5B,OAAO,CAAC;AACR,KAAK,CAAC;AACN,IAAI;AACJ,MAAM,IAAI,QAAQ,CAAC;AACnB,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,SAAS,KAAK;AACtE,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;AAClC,UAAU,OAAO;AACjB,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;AAChD,QAAQ,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC/B,QAAQ,QAAQ,GAAG,UAAU,CAAC,MAAM;AACpC,UAAU,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACjC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACjE,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrC,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;AACrF,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAClC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,uBAAuB,GAAG;AAC5B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;AACjC,GAAG;AACH;AACA,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE;AAC3B,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC9C,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE;AACvC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC;AAC9D,KAAK;AACL,GAAG;AACH;AACA,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM;AACzD,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC;AAC9D,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;AACtC,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;AACrC,GAAG;AACH;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvD,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC3F,QAAQ,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;AAChC,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;AAC5B,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAChD,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE;AAC9C,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,EAAE;AACjD,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,MAAM,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;AACjC,QAAQ,MAAM,UAAU,GAAG,CAAC,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAC5E,QAAQ,IAAI,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAClF,OAAO;AACP,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7B,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK;AAC5G,QAAQ,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AACjF,OAAO,CAAC,CAAC,KAAK,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACvE,OAAO,MAAM,IAAI,IAAI,EAAE;AACvB,QAAQ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;AACrD,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtF,OAAO;AACP,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC7C,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,IAAI,CAAC,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAE;AACnC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC/D,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE;AACxC,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAChE,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B,MAAM,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACnC,GAAG;AACH;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,WAAW,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,SAAS,GAAG,GAAG,EAAE,SAAS,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;AACvE,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B,MAAM,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAC9E,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,EAAE,EAAE;AAC7C,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AAC1D,MAAM,MAAM,IAAI,GAAG,EAAE,CAAC;AACtB,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;AAChE,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,EAAE;AAC7C,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC;AAC7E,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC3D,OAAO;AACP,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,WAAW,EAAE;AACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH;AACA,EAAE,iBAAiB,CAAC,aAAa,EAAE;AACnC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,MAAM,CAAC,QAAQ,EAAE;AACnB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC;AAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,OAAO,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AACxD,MAAM,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAC3D,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACpB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,CAAC,CAAC;AAClD,GAAG;AACH;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,eAAe,CAAC,OAAO,EAAE;AAC3B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACnC,IAAI,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5B,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;AACpB,GAAG;AACH,CAAC;AACD,SAAS,gBAAgB,CAAC,WAAW,EAAE;AACvC,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,gBAAgB,CAAC;AACjD,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC;AACzD,EAAE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpC,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,MAAM,WAAW,GAAG,SAAS,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE;AACvD,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC/C,MAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3C,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAC1C,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACvD,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;AACvE,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9C,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACpC,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AACnE,EAAE,MAAM,IAAI,CAAC,CAAC;AACd,EAAE,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,EAAE;AAC1D,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG;AAC7B,QAAQ,CAAC,CAAC;AACV,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5D,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;AAClD,MAAM,MAAM,IAAI,CAAC,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AACD,MAAM,aAAa,GAAG,OAAO,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,oBAAoB,KAAK;AAC/E,EAAE,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC;AACxC,IAAI,UAAU,EAAE,oBAAoB,IAAI,WAAW,CAAC,UAAU;AAC9D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;AACxD,EAAE,MAAM,UAAU,GAAG,oBAAoB,IAAI,WAAW,CAAC,UAAU,CAAC;AACpE,EAAE,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC;AACzC,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC;AACtB,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE;AACpB,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC;AACjD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;AACnD,IAAI,aAAa,GAAG,SAAS,GAAG,WAAW,CAAC;AAC5C,GAAG;AACH,EAAE,MAAM,kBAAkB,GAAG,YAAY,CAAC,YAAY;AACtD,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,gBAAgB,EAAE,OAAO,EAAE,EAAE;AAC/D,IAAI,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAI,MAAM,WAAW,GAAG,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACnE,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,EAAE;AAC/C,MAAM,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;AACtD,KAAK;AACL,GAAG;AACH,EAAE,OAAO,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC9C,CAAC,CAAC;AAMF,MAAM,sBAAsB,GAAG,CAAC,cAAc,EAAE,WAAW,KAAK;AAChE,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,WAAW,GAAG,CAAC,CAAC;AACpB,GAAG;AACH,EAAE,OAAO,cAAc,GAAG,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC;AACjD,CAAC,CAAC;AACF,MAAM,CAAC,CAAC;AACR,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC7F,GAAG;AACH,EAAE,gBAAgB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,mBAAmB,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AAC3I,MAAM,MAAM,EAAE,GAAG,MAAM;AACvB,QAAQ,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3E,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACzE,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;AACf,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;AAClB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxE,GAAG;AACH,CAAC;AACD,MAAM,CAAC,SAAS,CAAC,CAAC;AAClB,EAAE,WAAW,CAAC,EAAE,EAAE;AAClB,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACxD,GAAG;AACH,EAAE,MAAM,GAAG;AACX,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AACxC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACnE,GAAG;AACH,CAAC;AACD,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE;AACnC,EAAE,IAAI,CAAC,GAAG,MAAM;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,EAAE;AACT,IAAI,OAAO,CAAC,CAAC;AACb,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK;AACpB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM;AACvB,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,eAAe,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AAC7E,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,CAAC;AAClD,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK;AACtB,MAAM,EAAE,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,eAAe,EAAE,CAAC;AAChD,MAAM,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC;AAC7C,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;AACjE,QAAQ,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC;AACjE,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC;AAChH,OAAO;AACP,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK;AACnB,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,eAAe,EAAE,CAAC,CAAC;AACvD,KAAK,EAAE,CAAC,GAAG,MAAM;AACjB,MAAM,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChE,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM;AACjM,MAAM,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM;AACtK,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACvD,OAAO,EAAE,EAAE,CAAC,CAAC;AACb,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,MAAM;AACtD,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAClD,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,CAAC,SAAS,CAAC,CAAC;AAClB,EAAE,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE;AAC9B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,IAAI,CAAC,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,oBAAoB,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;AAC52B,GAAG;AACH,EAAE,aAAa,CAAC,EAAE,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AACvC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,gBAAgB,CAAC,EAAE,EAAE;AACvB,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,+RAA+R,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,kCAAkC,CAAC,CAAC;AACna,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;AAC9B,IAAI,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,GAAG,aAAa,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,mCAAmC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACzS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7K,GAAG;AACH,EAAE,mBAAmB,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,sBAAsB,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;AACxG,IAAI,EAAE,IAAI,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACvD,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AAC3E,IAAI,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC;AACzB,IAAI,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,KAAK,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;AACvK;AACA,WAAW,EAAE,EAAE,CAAC;AAChB,cAAc,EAAE,EAAE,CAAC;AACnB,wBAAwB,EAAE,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;AACnD,mBAAmB,EAAE,EAAE,GAAG,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7D;AACA;AACA;AACA,cAAc,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,SAAS,CAAC;AAC/C;AACA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AAC5D,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC;AAC1G,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC;AACxF,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACjC,IAAI,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AACxW,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa;AACnC,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,IAAI,OAAO,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,KAAK,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAC3M,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClL,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE;AACnB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,iBAAiB,CAAC,EAAE,EAAE;AACxB,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;AACnD,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE;AACjB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC1E,MAAM,IAAI,QAAQ,IAAI,OAAO,EAAE,EAAE;AACjC,QAAQ,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACrD,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AAC3C,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;AAChG,OAAO;AACP,QAAQ,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClG,KAAK;AACL,MAAM,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE;AACjB,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,SAAS,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE;AACjP,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AACzC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AACpP,KAAK;AACL,IAAI,IAAI,EAAE,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;AAClJ,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AACzC,MAAM,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACjI,KAAK;AACL,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACpE,GAAG;AACH,CAAC;AACD,MAAM,CAAC,SAAS,CAAC,CAAC;AAClB,EAAE,WAAW,CAAC,EAAE,EAAE;AAClB,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;AACtF,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,EAAE,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AACrB,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU;AACxB,MAAM,MAAM,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpE,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;AAChB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK;AACrE,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;AAC7E,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK;AACzB,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACtD,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK;AAC7B,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACvD,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC;AAClB,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,oBAAoB,GAAG;AACzB,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,2JAA2J,CAAC,EAAE,EAAE,CAAC;AACrM,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG;AACH,EAAE,gBAAgB,CAAC,EAAE,EAAE;AACvB,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO;AACnB,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK;AAC/H,MAAM,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO;AAClC,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;AACrF,MAAM,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK;AACnB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;AAC7G,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACtC,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE;AACjB,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpG,IAAI,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM;AAC1C,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACjE,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM;AAC5B,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9I,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK;AAC/B,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,KAAK;AAClC,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjD,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;AAChC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACtF,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU;AACxB,MAAM,MAAM,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACnD,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACxN,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK;AACnG,MAAM,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACX,GAAG;AACH,EAAE,mBAAmB,CAAC,EAAE,EAAE;AAC1B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9J,IAAI,IAAI,CAAC,CAAC;AACV,MAAM,OAAO,MAAM;AACnB,OAAO,CAAC;AACR,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK;AAChC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC;AACrD,KAAK,EAAE,CAAC,EAAE,KAAK;AACf,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU;AAClC,QAAQ,OAAO;AACf,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE,CAAC,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAC1R,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACjI,KAAK,EAAE,MAAM;AACb,MAAM,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;AACzC,GAAG;AACH,CAAC;AACD,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,OAAO,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,aAAa,IAAI,GAAG,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtV,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,2lBAA2lB;AACnmB,EAAE,GAAG,EAAE,mnFAAmnF;AAC1nF,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,aAAa,CAAC;AAOpB,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,+FAA+F,EAAE,aAAa,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjM,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,q9EAAq9E;AAC79E,EAAE,GAAG,EAAE,o1aAAo1a;AAC31a,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACxF,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,oBAAoB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,cAAc,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,EAAE,IAAI,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AACxC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC;AAC1B,EAAE,IAAI,gBAAgB,CAAC;AACvB,EAAE,IAAI,iBAAiB,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,IAAI,aAAa,GAAG,CAAC,CAAC;AACxB,EAAE,MAAM,mBAAmB,GAAG,CAAC,MAAM,EAAE,GAAG,KAAK;AAC/C,IAAI,IAAI,QAAQ,CAAC;AACjB,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAI,CAAC,YAAY;AACrB,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,IAAI,GAAG,KAAK,WAAW,EAAE;AAC/B,QAAQ,QAAQ,GAAG,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7C,QAAQ,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC;AAClC,OAAO,MAAM;AACb,QAAQ,QAAQ,GAAG,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7C,QAAQ,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC;AAClC,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,KAAK,WAAW,EAAE;AAC/B,QAAQ,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;AACtC,QAAQ,MAAM,GAAG,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;AACtC,QAAQ,MAAM,GAAG,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC;AACzC,OAAO;AACP,KAAK;AACL,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;AAC9D,IAAI,YAAY,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,KAAK,CAAC;AACzD,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,SAAS,IAAI,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC;AACpF,IAAI;AACJ,MAAM,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK;AAC/C,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,UAAU,EAAE,EAAE,CAAC,gBAAgB,EAAE,CAAC,MAAM,KAAK;AACnD,QAAQ,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;AACjD,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,UAAU,EAAE,EAAE,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,EAAE,KAAK;AACvD,QAAQ,EAAE,CAAC,eAAe,EAAE,CAAC;AAC7B,QAAQ,YAAY,GAAG,MAAM,CAAC;AAC9B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AAC5D,QAAQ,iBAAiB,GAAG,UAAU,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;AAC9E,QAAQ,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;AAC5E,QAAQ,IAAI,gBAAgB,IAAI,iBAAiB,EAAE;AACnD,UAAU,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC1D,UAAU,iBAAiB,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3D,UAAU,gBAAgB,EAAE,YAAY,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;AACpF,UAAU,iBAAiB,EAAE,YAAY,CAAC,YAAY,EAAE,yBAAyB,CAAC,CAAC;AACnF,UAAU,gBAAgB,EAAE,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC1D,UAAU,iBAAiB,EAAE,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC3D,UAAU,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC3D,YAAY,IAAI,UAAU;AAC1B,cAAc,YAAY,GAAG,MAAM,CAAC;AACpC,WAAW,CAAC,CAAC;AACb,UAAU,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC5D,YAAY,IAAI,UAAU;AAC1B,cAAc,YAAY,GAAG,OAAO,CAAC;AACrC,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK;AAC7D,MAAM,IAAI,EAAE,CAAC,GAAG,KAAK,WAAW,EAAE;AAClC,QAAQ,mBAAmB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACvD,OAAO,MAAM,IAAI,EAAE,CAAC,GAAG,KAAK,YAAY,EAAE;AAC1C,QAAQ,mBAAmB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACxD,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,GAAG,CAAC,0LAA0L,EAAE,UAAU,CAAC;AACzN,MAAM,OAAO,EAAE,kBAAkB,GAAG,qBAAqB,GAAG,oBAAoB;AAChF,KAAK,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,UAAU,EAAE,kBAAkB,GAAG,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ;AACxM,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ;AAChB,QAAQ,aAAa;AACrB,QAAQ,kBAAkB;AAC1B,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,CAAC,OAAO,KAAK;AACpC,UAAU,aAAa,GAAG,OAAO,CAAC;AAClC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,OAAO,KAAK;AACzC,UAAU,kBAAkB,GAAG,OAAO,CAAC;AACvC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,6BAA6B,EAAE,kBAAkB,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,+GAA+G,EAAE,aAAa,CAAC,YAAY,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,cAAc,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,8DAA8D,EAAE,aAAa,CAAC,YAAY,EAAE,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,8DAA8D,EAAE,kBAAkB,GAAG,MAAM,CAAC,sBAAsB,CAAC,cAAc,EAAE,gBAAgB,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,4DAA4D,EAAE,QAAQ,IAAI,WAAW,GAAG,CAAC,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,mEAAmE,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,EAAE,GAAG,CAAC,+EAA+E,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,+KAA+K,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAC1xD,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,4rBAA4rB;AACpsB,EAAE,GAAG,EAAE,05RAA05R;AACj6R,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,GAAG,OAAO,CAAC;AAC5D,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,oBAAoB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,kBAAkB,GAAG,MAAM;AACnC,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,YAAY,GAAG,CAAC,CAAC;AACvB,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACjC,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,MAAM,eAAe,GAAG,MAAM;AAChC,IAAI,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC;AACtE,IAAI,gBAAgB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,KAAK;AACxD,MAAM,IAAI,YAAY,IAAI,QAAQ,EAAE;AACpC,QAAQ,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,OAAO,KAAK,EAAE,GAAG,KAAK;AAClD,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAI,MAAM,WAAW,GAAG,QAAQ,EAAE,cAAc,EAAE,CAAC;AACnD,IAAI,IAAI,WAAW;AACnB,MAAM,MAAM,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,WAAW,KAAK;AAC7G,QAAQ,MAAM,aAAa,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC;AACrD,QAAQ,QAAQ,EAAE,OAAO,EAAE,CAAC;AAC5B,QAAQ,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;AACjC,OAAO,CAAC,CAAC;AACT,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,eAAe,UAAU,CAAC,IAAI,EAAE;AAClC,IAAI,MAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,KAAK;AACxD,MAAM,IAAI,CAAC,YAAY,IAAI,KAAK,EAAE,SAAS;AAC3C,QAAQ,OAAO;AACf,MAAM,IAAI,gBAAgB,CAAC,uBAAuB,EAAE;AACpD,QAAQ,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACrC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,MAAM,EAAE;AAC/B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG;AACnD,MAAM,OAAO;AACb,IAAI,OAAO;AACX,GAAG;AAYH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC;AACrB,IAAI,YAAY,GAAG,gBAAgB,CAAC,uBAAuB,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC;AACjF,IAAI;AACJ,MAAM,IAAI,YAAY,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,SAAS,KAAK,IAAI,EAAE;AACtE,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/B,UAAU,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC7B,QAAQ,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;AACjC,QAAQ,eAAe,EAAE,CAAC;AAC1B,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,KAAK;AAC3C,QAAQ,cAAc,GAAG,QAAQ,CAAC;AAClC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,YAAY,EAAE,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM;AAClC,QAAQ,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE;AACzC,UAAU,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC3B,SAAS,MAAM;AACf,UAAU,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,MAAM;AACnC,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC3B,SAAS,MAAM;AACf,UAAU,OAAO,GAAG,KAAK,CAAC;AAC1B,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM;AAClC,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM;AACjC,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM;AACjC,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI;AACJ,MAAM,WAAW,CAAC,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,cAAc,EAAE,CAAC,gCAAgC,EAAE,YAAY,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,iBAAiB,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,UAAU,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AACpU,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACtF,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,6CAA6C,EAAE,aAAa,CAAC,aAAa,EAAE,KAAK,GAAG,WAAW,GAAG,KAAK,GAAG,kBAAkB,EAAE,CAAC,CAAC,CAAC,yFAAyF,EAAE,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,2FAA2F,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,gDAAgD,EAAE,IAAI,KAAK,MAAM,IAAI,YAAY,GAAG,CAAC,GAAG,CAAC,gDAAgD,EAAE,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,2CAA2C,EAAE,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,wDAAwD,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACz0B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,OAAO;AACf,QAAQ,cAAc;AACtB,QAAQ,IAAI;AACZ,QAAQ,WAAW;AACnB,QAAQ,iBAAiB;AACzB,QAAQ,SAAS,EAAE,WAAW;AAC9B,QAAQ,kBAAkB;AAC1B,QAAQ,gBAAgB;AACxB,QAAQ,oBAAoB;AAC5B,QAAQ,QAAQ;AAChB,QAAQ,IAAI;AACZ,QAAQ,YAAY;AACpB,QAAQ,kBAAkB;AAC1B,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,IAAI,GAAG,OAAO,CAAC;AACzB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,YAAY,EAAE,CAAC,OAAO,KAAK;AACnC,UAAU,YAAY,GAAG,OAAO,CAAC;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,kBAAkB,EAAE,CAAC,OAAO,KAAK;AACzC,UAAU,kBAAkB,GAAG,OAAO,CAAC;AACvC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,aAAa,GAAG;;;;"}