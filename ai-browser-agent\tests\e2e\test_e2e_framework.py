"""
End-to-End Testing Framework for AI Browser Agent

This module provides comprehensive E2E testing with browser automation,
UI testing, and agent behavior validation.
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any

import pytest
from fastapi.testclient import TestClient
from playwright.async_api import Browser
from playwright.async_api import <PERSON>rowser<PERSON>ontext
from playwright.async_api import Page
from playwright.async_api import async_playwright

from src.agent.run_agent import cleanup_browser
from src.agent.run_agent import run_agent
from tests.e2e.test_scenarios import TestScenarios
from tests.e2e.test_scenarios import UserQuestions
from webui import app

logger = logging.getLogger(__name__)

class E2ETestFramework:
    """Comprehensive E2E testing framework."""

    def __init__(self):
        self.test_client = TestClient(app)
        self.browser: Browser | None = None
        self.context: BrowserContext | None = None
        self.page: Page | None = None
        self.test_results: list[dict[str, Any]] = []
        self.start_time = None

    async def setup(self, headless: bool = True):
        """Setup browser and test environment."""
        self.start_time = time.time()

        # Launch browser
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )

        # Create context
        self.context = await self.browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )

        # Create page
        self.page = await self.context.new_page()

        # Setup console logging
        self.page.on('console', lambda msg: logger.info(f"Browser console: {msg.text}"))
        self.page.on('pageerror', lambda error: logger.error(f"Browser error: {error}"))

        logger.info("E2E test framework setup complete")

    async def teardown(self):
        """Clean up test environment."""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()

        # Cleanup agent browser
        await cleanup_browser()

        # Generate test report
        await self.generate_test_report()

        logger.info("E2E test framework teardown complete")

    async def test_ui_functionality(self) -> dict[str, Any]:
        """Test the web UI functionality."""
        test_result = {
            "test_name": "ui_functionality",
            "start_time": datetime.now().isoformat(),
            "status": "running",
            "errors": [],
            "steps": []
        }

        try:
            # Start the FastAPI server in background
            import threading

            import uvicorn

            def run_server():
                uvicorn.run(app, host="127.0.0.1", port=7861, log_level="error")

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # Wait for server to start
            await asyncio.sleep(2)

            # Navigate to the application
            await self.page.goto("http://127.0.0.1:7861")
            test_result["steps"].append("Navigated to application")

            # Check if page loads correctly
            title = await self.page.title()
            assert "AI Browser Agent" in title or title != ""
            test_result["steps"].append(f"Page loaded with title: {title}")

            # Check for main UI elements
            chat_title = await self.page.locator("#chat-title").text_content()
            assert chat_title is not None
            test_result["steps"].append("Chat title element found")

            # Test new chat creation
            new_chat_btn = self.page.locator('button:has-text("+ New Chat")')
            await new_chat_btn.click()
            test_result["steps"].append("Clicked new chat button")

            # Wait for chat to be created
            await asyncio.sleep(1)

            # Test message input
            message_input = self.page.locator("#message-input")
            test_message = "Hello, this is a test message"
            await message_input.fill(test_message)
            test_result["steps"].append(f"Filled message input: {test_message}")

            # Test send button
            send_btn = self.page.locator('button[type="submit"]')
            await send_btn.click()
            test_result["steps"].append("Clicked send button")

            # Wait for response
            await asyncio.sleep(3)

            # Check if message appears in chat
            chat_body = self.page.locator("#chat-body")
            chat_content = await chat_body.text_content()

            if test_message in chat_content:
                test_result["steps"].append("Message appeared in chat")
            else:
                test_result["errors"].append("Message did not appear in chat")

            test_result["status"] = "passed" if not test_result["errors"] else "failed"

        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"UI test failed: {str(e)}")
            logger.error(f"UI test error: {e}")

        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)
        return test_result

    async def test_agent_scenarios(self, scenarios: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Test agent with various scenarios."""
        results = []

        for scenario in scenarios:
            result = await self.run_scenario_test(scenario)
            results.append(result)

        return results

    async def run_scenario_test(self, scenario: dict[str, Any]) -> dict[str, Any]:
        """Run a single scenario test."""
        test_result = {
            "test_name": scenario["name"],
            "description": scenario["description"],
            "start_time": datetime.now().isoformat(),
            "status": "running",
            "errors": [],
            "steps": [],
            "agent_output": "",
            "execution_time": 0
        }

        start_time = time.time()

        try:
            logger.info(f"Running scenario: {scenario['name']}")

            # Create chat history
            history = [{"role": "user", "content": scenario["user_input"]}]

            # Run agent
            agent_output = ""
            async for token in run_agent(history):
                agent_output += token

            test_result["agent_output"] = agent_output
            test_result["steps"].append(f"Agent completed with output length: {len(agent_output)}")

            # Validate success criteria
            success_count = 0
            for criteria in scenario.get("success_criteria", []):
                if criteria.lower() in agent_output.lower():
                    success_count += 1
                    test_result["steps"].append(f"Success criteria met: {criteria}")
                else:
                    test_result["errors"].append(f"Success criteria not met: {criteria}")

            # Determine test status
            expect_failure = scenario.get("expect_failure", False)
            if expect_failure:
                test_result["status"] = "passed" if test_result["errors"] else "failed"
            else:
                test_result["status"] = "passed" if success_count > 0 else "failed"

        except Exception as e:
            test_result["status"] = "failed"
            test_result["errors"].append(f"Scenario test failed: {str(e)}")
            logger.error(f"Scenario test error: {e}")

        test_result["execution_time"] = time.time() - start_time
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results.append(test_result)

        return test_result

    async def test_edge_cases(self) -> list[dict[str, Any]]:
        """Test edge cases and error handling."""
        edge_cases = UserQuestions.EDGE_CASE_QUESTIONS
        results = []

        for i, question in enumerate(edge_cases):
            test_result = {
                "test_name": f"edge_case_{i}",
                "description": f"Edge case test with input: {repr(question)[:50]}...",
                "start_time": datetime.now().isoformat(),
                "status": "running",
                "errors": [],
                "agent_output": "",
                "input": question
            }

            try:
                history = [{"role": "user", "content": question}]

                agent_output = ""
                async for token in run_agent(history):
                    agent_output += token

                test_result["agent_output"] = agent_output

                # For edge cases, we expect graceful handling
                if "error" in agent_output.lower() or "❌" in agent_output:
                    test_result["status"] = "passed"  # Graceful error handling
                elif question == "":
                    test_result["status"] = "passed" if "error" in agent_output.lower() else "failed"
                else:
                    test_result["status"] = "passed"  # Any response is good for edge cases

            except Exception as e:
                test_result["status"] = "passed"  # Expected for some edge cases
                test_result["errors"].append(f"Expected error: {str(e)}")

            test_result["end_time"] = datetime.now().isoformat()
            results.append(test_result)
            self.test_results.append(test_result)

        return results

    async def generate_test_report(self):
        """Generate comprehensive test report."""
        if not self.test_results:
            return

        report = {
            "test_session": {
                "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration": time.time() - self.start_time,
                "total_tests": len(self.test_results)
            },
            "summary": {
                "passed": len([r for r in self.test_results if r["status"] == "passed"]),
                "failed": len([r for r in self.test_results if r["status"] == "failed"]),
                "errors": sum(len(r["errors"]) for r in self.test_results)
            },
            "results": self.test_results
        }

        # Save report
        report_path = Path("test_reports") / f"e2e_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"Test report saved to: {report_path}")

        # Print summary
        print(f"\n{'='*60}")
        print("E2E TEST REPORT SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {report['summary']['passed'] + report['summary']['failed']}")
        print(f"Passed: {report['summary']['passed']}")
        print(f"Failed: {report['summary']['failed']}")
        print(f"Success Rate: {report['summary']['passed']/(report['summary']['passed'] + report['summary']['failed'])*100:.1f}%")
        print(f"Total Duration: {report['test_session']['duration']:.2f}s")
        print(f"Report saved to: {report_path}")
        print(f"{'='*60}\n")


# Pytest fixtures and test functions
@pytest.fixture
async def e2e_framework():
    """Pytest fixture for E2E framework."""
    framework = E2ETestFramework()
    await framework.setup(headless=True)
    yield framework
    await framework.teardown()


@pytest.mark.asyncio
async def test_ui_functionality(e2e_framework):
    """Test UI functionality."""
    result = await e2e_framework.test_ui_functionality()
    assert result["status"] == "passed", f"UI test failed: {result['errors']}"


@pytest.mark.asyncio
async def test_basic_scenarios(e2e_framework):
    """Test basic agent scenarios."""
    scenarios = TestScenarios.get_basic_scenarios()
    results = await e2e_framework.test_agent_scenarios(scenarios)

    passed = len([r for r in results if r["status"] == "passed"])
    len(results)

    assert passed > 0, f"No basic scenarios passed. Results: {results}"


@pytest.mark.asyncio
async def test_edge_cases(e2e_framework):
    """Test edge cases."""
    results = await e2e_framework.test_edge_cases()

    # For edge cases, we expect most to be handled gracefully
    passed = len([r for r in results if r["status"] == "passed"])
    total = len(results)

    assert passed >= total * 0.5, f"Too many edge cases failed. Results: {results}"
