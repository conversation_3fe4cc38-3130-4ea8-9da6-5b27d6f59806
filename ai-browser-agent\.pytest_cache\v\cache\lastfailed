{"tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_total_pages_header": true, "tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_response_metadata": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_rate_cards_method": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_purchase_orders_method": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_sales_orders_method": true, "tests/cartoncloud/test_client_signature.py::TestTokenManagement::test_token_refresh_on_expiry": true, "test_claude_integration.py": true, "test_claude_simple.py": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_http_error_handling": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_validation_error_handling": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_server_error_handling": true, "tests/test_browser_use.py": true, "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudAuth::test_get_token_failure": true, "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudEndpoints::test_list_rate_cards": true, "tests/cartoncloud/test_cartoncloud.py::TestCartonCloudEndpoints::test_create_purchase_order": true}