{"tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-GET-params0-https://api.cartoncloud.com/v1/rate-cards]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-GET-params1-https://api.cartoncloud.com/v1/rate-cards/rc_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards-POST-params2-https://api.cartoncloud.com/v1/rate-cards]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-PUT-params3-https://api.cartoncloud.com/v1/rate-cards/rc_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/rate-cards/rc_001-DELETE-params4-https://api.cartoncloud.com/v1/rate-cards/rc_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-GET-params5-https://api.cartoncloud.com/v1/purchase-orders]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-GET-params6-https://api.cartoncloud.com/v1/purchase-orders/po_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders-POST-params7-https://api.cartoncloud.com/v1/purchase-orders]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/purchase-orders/po_001-PUT-params8-https://api.cartoncloud.com/v1/purchase-orders/po_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-GET-params9-https://api.cartoncloud.com/v1/sales-orders]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-GET-params10-https://api.cartoncloud.com/v1/sales-orders/so_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders-POST-params11-https://api.cartoncloud.com/v1/sales-orders]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/sales-orders/so_001-PUT-params12-https://api.cartoncloud.com/v1/sales-orders/so_001]": true, "tests/cartoncloud/test_client_signature.py::TestEndpointCoverage::test_endpoint_signature[/v1/reports/bulk-charges-GET-params13-https://api.cartoncloud.com/v1/reports/bulk-charges]": true, "tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_total_pages_header": true, "tests/cartoncloud/test_client_signature.py::TestPaginationHandling::test_paginate_with_response_metadata": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_rate_cards_method": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_purchase_orders_method": true, "tests/cartoncloud/test_client_signature.py::TestConvenienceMethods::test_get_sales_orders_method": true, "tests/cartoncloud/test_client_signature.py::TestTokenManagement::test_token_refresh_on_expiry": true, "test_claude_integration.py": true, "test_claude_simple.py": true, "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_api_initialization": true, "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_make_request_includes_version_header": true, "tests/cartoncloud/test_client_signature.py::TestCartonCloudAPISignature::test_error_handling_structure": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_http_error_handling": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_validation_error_handling": true, "tests/cartoncloud/test_client_signature.py::TestErrorHandling::test_server_error_handling": true, "tests/test_browser_use.py": true}