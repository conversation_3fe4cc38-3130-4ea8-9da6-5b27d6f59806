"""API endpoint helpers for CartonCloud integration.

DEPRECATED: These functions are deprecated in favor of the new CartonCloudAPI class.
Use CartonCloudAPI instead for new code.
"""

import os
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List, Optional, Dict, Any
import requests
import logging
import warnings

from .auth import get_token
from .api import CartonCloudAPI, CartonCloudAPIError
from .models import (
    RateCard,
    PurchaseOrder,
    SalesOrder,
    BulkChargeReportLine,
    ApiResponse,
)

logger = logging.getLogger(__name__)

# Global instance for backward compatibility
_api_instance = None

def _get_api_instance() -> CartonCloudAPI:
    """Get or create a global API instance for backward compatibility."""
    global _api_instance
    if _api_instance is None:
        _api_instance = CartonCloudAPI()
    return _api_instance

def _deprecation_warning(func_name: str, replacement: str):
    """Issue deprecation warning for legacy functions."""
    warnings.warn(
        f"{func_name} is deprecated. Use {replacement} instead.",
        DeprecationWarning,
        stacklevel=3
    )


def request(
    method: str,
    endpoint: str,
    data: Optional[Dict[str, Any]] = None,
    params: Optional[Dict[str, Any]] = None,
    token: Optional[str] = None,
    base_url: Optional[str] = None,
) -> Dict[str, Any]:
    """Make authenticated request to CartonCloud API.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        method: HTTP method (GET, POST, PUT, DELETE)
        endpoint: API endpoint path (without base URL)
        data: Request body data for POST/PUT requests
        params: URL parameters for GET requests
        token: Bearer token (will be obtained automatically if not provided)
        base_url: API base URL (defaults to CARTONCLOUD_BASE_URL env var)
        
    Returns:
        Response JSON data
        
    Raises:
        requests.RequestException: If request fails
    """
    _deprecation_warning("request()", "CartonCloudAPI.make_request()")
    
    try:
        api = _get_api_instance()
        return api.make_request(method, endpoint, data=data, params=params)
    except CartonCloudAPIError as e:
        # Convert to requests.RequestException for backward compatibility
        raise requests.RequestException(str(e))


def list_rate_cards(
    customer_id: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    token: Optional[str] = None,
) -> List[RateCard]:
    """List rate cards from CartonCloud API.
    
    DEPRECATED: Use CartonCloudAPI.get_rate_cards() instead.
    
    Args:
        customer_id: Filter by customer ID
        status: Filter by status (active, inactive, expired)
        limit: Maximum number of results
        offset: Pagination offset
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        List of RateCard objects
    """
    _deprecation_warning("list_rate_cards()", "CartonCloudAPI.get_rate_cards()")
    
    api = _get_api_instance()
    params = {"limit": limit, "offset": offset}
    if customer_id:
        params["customer_id"] = customer_id
    if status:
        params["status"] = status
    
    # Use the new API's paginated method
    return list(api.get_rate_cards(**params))


def get_rate_card(rate_card_id: str, token: Optional[str] = None) -> Optional[RateCard]:
    """Get a specific rate card by ID.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        rate_card_id: Rate card ID
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        RateCard object if found, None otherwise
    """
    _deprecation_warning("get_rate_card()", "CartonCloudAPI.make_request()")
    
    try:
        api = _get_api_instance()
        response = api.make_request("GET", f"/v1/rate-cards/{rate_card_id}")
        return RateCard(**response.get("data", {}))
    except CartonCloudAPIError as e:
        if e.status_code == 404:
            return None
        raise


def create_purchase_order(po_data: PurchaseOrder, token: Optional[str] = None) -> PurchaseOrder:
    """Create a new purchase order.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        po_data: PurchaseOrder object with order details
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        Created PurchaseOrder object with assigned ID
    """
    _deprecation_warning("create_purchase_order()", "CartonCloudAPI.make_request()")
    
    api = _get_api_instance()
    po_dict = po_data.model_dump(exclude_none=True, exclude={"id", "created_at", "updated_at"})
    response = api.make_request("POST", "/v1/purchase-orders", data=po_dict)
    
    return PurchaseOrder(**response.get("data", {}))


def get_purchase_order(po_id: str, token: Optional[str] = None) -> Optional[PurchaseOrder]:
    """Get a specific purchase order by ID.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        po_id: Purchase order ID
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        PurchaseOrder object if found, None otherwise
    """
    _deprecation_warning("get_purchase_order()", "CartonCloudAPI.make_request()")
    
    try:
        api = _get_api_instance()
        response = api.make_request("GET", f"/v1/purchase-orders/{po_id}")
        return PurchaseOrder(**response.get("data", {}))
    except CartonCloudAPIError as e:
        if e.status_code == 404:
            return None
        raise


def create_sales_order(so_data: SalesOrder, token: Optional[str] = None) -> SalesOrder:
    """Create a new sales order.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        so_data: SalesOrder object with order details
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        Created SalesOrder object with assigned ID
    """
    _deprecation_warning("create_sales_order()", "CartonCloudAPI.make_request()")
    
    api = _get_api_instance()
    so_dict = so_data.model_dump(exclude_none=True, exclude={"id", "created_at", "updated_at"})
    response = api.make_request("POST", "/v1/sales-orders", data=so_dict)
    
    return SalesOrder(**response.get("data", {}))


def get_sales_order(so_id: str, token: Optional[str] = None) -> Optional[SalesOrder]:
    """Get a specific sales order by ID.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        so_id: Sales order ID
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        SalesOrder object if found, None otherwise
    """
    _deprecation_warning("get_sales_order()", "CartonCloudAPI.make_request()")
    
    try:
        api = _get_api_instance()
        response = api.make_request("GET", f"/v1/sales-orders/{so_id}")
        return SalesOrder(**response.get("data", {}))
    except CartonCloudAPIError as e:
        if e.status_code == 404:
            return None
        raise


def get_bulk_charges_report(
    start_date: datetime,
    end_date: datetime,
    customer_id: Optional[str] = None,
    token: Optional[str] = None,
) -> List[BulkChargeReportLine]:
    """Get bulk charges report for a date range.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Args:
        start_date: Report start date
        end_date: Report end date
        customer_id: Filter by customer ID
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        List of BulkChargeReportLine objects
    """
    _deprecation_warning("get_bulk_charges_report()", "CartonCloudAPI.make_request()")
    
    api = _get_api_instance()
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
    }
    if customer_id:
        params["customer_id"] = customer_id
    
    response = api.make_request("GET", "/v1/reports/bulk-charges", params=params)
    
    return [BulkChargeReportLine(**item) for item in response.get("data", [])]


def get_yesterday_bulk_charges(
    customer_id: Optional[str] = None,
    token: Optional[str] = None,
) -> List[BulkChargeReportLine]:
    """Get yesterday's bulk charges report.
    
    DEPRECATED: Use CartonCloudAPI.make_request() instead.
    
    Convenience function for nightly audit workflows.
    
    Args:
        customer_id: Filter by customer ID
        token: Bearer token (will be obtained automatically if not provided)
        
    Returns:
        List of BulkChargeReportLine objects
    """
    _deprecation_warning("get_yesterday_bulk_charges()", "CartonCloudAPI.make_request()")
    
    yesterday = datetime.now() - timedelta(days=1)
    start_date = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
    
    return get_bulk_charges_report(start_date, end_date, customer_id, token)
