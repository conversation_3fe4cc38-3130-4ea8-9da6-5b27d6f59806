"""Nightly audit workflow for CartonCloud billing verification."""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import yaml

from src.integrations.cartoncloud.endpoints import get_yesterday_bulk_charges
from src.integrations.cartoncloud.models import BulkChargeReportLine
from src.tools.rate_card_lookup import get_rate_card_lookup

logger = logging.getLogger(__name__)


def load_customer_uom_truth_table(file_path: str = "data/customer_uom.yml") -> Dict[str, Dict[str, str]]:
    """Load customer UoM truth table from YAML file.
    
    Args:
        file_path: Path to the YAML file containing customer UoM mappings
        
    Returns:
        Dictionary mapping customer_id -> charge_code -> expected_uom
    """
    truth_table_path = Path(file_path)
    if not truth_table_path.exists():
        logger.warning(f"Truth table file not found: {file_path}")
        return {}
    
    try:
        with open(truth_table_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        logger.error(f"Error loading truth table: {e}")
        return {}


def detect_uom_mismatches(
    charges: List[BulkChargeReportLine],
    truth_table: Dict[str, Dict[str, str]]
) -> List[Dict[str, Any]]:
    """Detect UoM mismatches between actual charges and expected values.
    
    Args:
        charges: List of charge lines from the report
        truth_table: Expected UoM mappings
        
    Returns:
        List of mismatch records for correction
    """
    mismatches = []
    
    for charge in charges:
        customer_mappings = truth_table.get(charge.customer_id, {})
        expected_uom = customer_mappings.get(charge.charge_code)
        
        if expected_uom and charge.unit_of_measure != expected_uom:
            mismatches.append({
                "charge_id": charge.id,
                "customer_id": charge.customer_id,
                "customer_name": charge.customer_name,
                "charge_code": charge.charge_code,
                "description": charge.description,
                "current_uom": charge.unit_of_measure,
                "expected_uom": expected_uom,
                "rate": str(charge.rate),
                "amount": str(charge.amount),
                "date_charged": charge.date_charged.isoformat(),
            })
    
    return mismatches


def detect_price_mismatches(
    charges: List[BulkChargeReportLine]
) -> List[Dict[str, Any]]:
    """Detect price mismatches between actual charges and rate card prices.
    
    Args:
        charges: List of charge lines from the report
        
    Returns:
        List of price mismatch records for correction
    """
    mismatches = []
    rate_lookup = get_rate_card_lookup()
    
    for charge in charges:
        try:
            # Try to find matching service in rate card by description
            matches = rate_lookup.search_by_service_name(charge.description)
            
            if not matches:
                # Try to normalize the charge code and look up by ID
                charge_code_normalized = charge.charge_code.lower().replace(' ', '_')
                rate_card_result = rate_lookup.lookup(charge_code_normalized, 1.0)
                
                if rate_card_result:
                    expected_price = rate_card_result.price_ex_gst
                    actual_price = charge.rate
                    
                    # Check if prices differ by more than 1 cent (to account for rounding)
                    if abs(expected_price - actual_price) > 0.01:
                        mismatches.append({
                            "charge_id": charge.id,
                            "customer_id": charge.customer_id,
                            "customer_name": charge.customer_name,
                            "charge_code": charge.charge_code,
                            "description": charge.description,
                            "actual_rate": str(charge.rate),
                            "expected_rate": str(expected_price),
                            "difference": str(abs(expected_price - actual_price)),
                            "unit_of_measure": charge.unit_of_measure,
                            "quantity": str(charge.quantity),
                            "amount": str(charge.amount),
                            "date_charged": charge.date_charged.isoformat(),
                            "service_id": charge_code_normalized,
                            "rate_card_unit": rate_card_result.unit
                        })
            else:
                # Use the best match (first in sorted results)
                best_match = matches[0]
                expected_price = best_match.price_ex_gst
                actual_price = charge.rate
                
                if abs(expected_price - actual_price) > 0.01:
                    mismatches.append({
                        "charge_id": charge.id,
                        "customer_id": charge.customer_id,
                        "customer_name": charge.customer_name,
                        "charge_code": charge.charge_code,
                        "description": charge.description,
                        "actual_rate": str(charge.rate),
                        "expected_rate": str(expected_price),
                        "difference": str(abs(expected_price - actual_price)),
                        "unit_of_measure": charge.unit_of_measure,
                        "quantity": str(charge.quantity),
                        "amount": str(charge.amount),
                        "date_charged": charge.date_charged.isoformat(),
                        "service_id": best_match.id,
                        "rate_card_unit": best_match.unit,
                        "matched_service": best_match.service
                    })
                    
        except Exception as e:
            logger.warning(f"Error checking price for charge {charge.id}: {e}")
            continue
    
    return mismatches


def queue_rate_card_updates(mismatches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Queue rate card update jobs for UI automation.
    
    Args:
        mismatches: List of UoM and price mismatch records
        
    Returns:
        List of queued update jobs
    """
    update_jobs = []
    
    # Group mismatches by customer and charge code
    grouped_updates = {}
    for mismatch in mismatches:
        key = (mismatch["customer_id"], mismatch["charge_code"])
        if key not in grouped_updates:
            grouped_updates[key] = mismatch
    
    # Create update jobs
    for (customer_id, charge_code), mismatch in grouped_updates.items():
        job = {
            "action": "update_rate_card",
            "customer_id": customer_id,
            "charge_code": charge_code,
            "priority": "high" if float(mismatch["amount"]) > 1000 else "normal",
            "scheduled_for": datetime.now().isoformat(),
        }
        
        # Add UoM update if present
        if "expected_uom" in mismatch:
            job.update({
                "new_uom": mismatch["expected_uom"],
                "current_uom": mismatch["current_uom"],
                "mismatch_type": "uom"
            })
        
        # Add price update if present
        if "expected_rate" in mismatch:
            job.update({
                "new_rate": mismatch["expected_rate"],
                "current_rate": mismatch["actual_rate"],
                "mismatch_type": "price",
                "service_id": mismatch.get("service_id"),
                "rate_card_unit": mismatch.get("rate_card_unit")
            })
        
        update_jobs.append(job)
    
    return update_jobs


async def run_nightly_audit(
    truth_table_path: str = "data/customer_uom.yml",
    output_path: str = "audit_results"
) -> Dict[str, Any]:
    """Run the nightly billing audit process.
    
    Args:
        truth_table_path: Path to customer UoM truth table
        output_path: Directory to save audit results
        
    Returns:
        Audit summary with findings and actions
    """
    audit_start = datetime.now()
    logger.info(f"Starting nightly audit at {audit_start}")
    
    try:
        # Load truth table
        truth_table = load_customer_uom_truth_table(truth_table_path)
        logger.info(f"Loaded truth table with {len(truth_table)} customers")
        
        # Get yesterday's charges
        charges = get_yesterday_bulk_charges()
        logger.info(f"Retrieved {len(charges)} charge lines from yesterday")
        
        # Detect mismatches
        uom_mismatches = detect_uom_mismatches(charges, truth_table)
        logger.info(f"Found {len(uom_mismatches)} UoM mismatches")
        
        price_mismatches = detect_price_mismatches(charges)
        logger.info(f"Found {len(price_mismatches)} price mismatches")
        
        # Combine mismatches for updating
        all_mismatches = uom_mismatches + price_mismatches
        
        # Queue updates
        update_jobs = queue_rate_card_updates(all_mismatches)
        logger.info(f"Queued {len(update_jobs)} rate card updates")
        
        # Generate audit summary
        audit_summary = {
            "audit_date": audit_start.isoformat(),
            "charges_reviewed": len(charges),
            "uom_mismatches_found": len(uom_mismatches),
            "price_mismatches_found": len(price_mismatches),
            "updates_queued": len(update_jobs),
            "customers_affected": len(set(m["customer_id"] for m in all_mismatches)),
            "total_amount_affected": sum(float(m["amount"]) for m in all_mismatches),
            "mismatches": all_mismatches,
            "update_jobs": update_jobs,
        }
        
        # Save results
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        audit_file = output_dir / f"audit_{audit_start.strftime('%Y%m%d_%H%M%S')}.json"
        with open(audit_file, 'w', encoding='utf-8') as f:
            json.dump(audit_summary, f, indent=2, default=str)
        
        logger.info(f"Audit completed. Results saved to {audit_file}")
        
        # Print summary to console (for Slack integration later)
        print(json.dumps(audit_summary, indent=2, default=str))
        
        return audit_summary
        
    except Exception as e:
        logger.error(f"Audit failed: {e}")
        return {
            "audit_date": audit_start.isoformat(),
            "status": "failed",
            "error": str(e),
        }


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(run_nightly_audit())
