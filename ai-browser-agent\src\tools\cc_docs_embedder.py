"""Enhanced CartonCloud documentation embedder for local and web content."""

import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
import asyncio
from datetime import datetime

from langchain.schema import Document
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter

from .cc_local_docs_loader import CartonCloudLocalDocsLoader

logger = logging.getLogger(__name__)


class CartonCloudDocumentEmbedder:
    """Enhanced embedder for CartonCloud documentation from multiple sources."""
    
    def __init__(
        self,
        vectorstore_path: Optional[str] = None,
        embedding_model: str = "text-embedding-3-small",
        embedding_provider: str = "openai",
        collection_name: str = "cartoncloud_docs",
        include_local_docs: bool = True,
        include_web_docs: bool = True,
        local_docs_dirs: Optional[List[str]] = None
    ):
        """Initialize the documentation embedder.
        
        Args:
            vectorstore_path: Path to store the vector database
            embedding_model: Embedding model to use
            embedding_provider: Provider for embeddings ('openai' only for now)
            collection_name: Name for the Chroma collection
            include_local_docs: Whether to include local documents
            include_web_docs: Whether to include web-scraped documents
            local_docs_dirs: Directories to scan for local documents
        """
        self.vectorstore_path = vectorstore_path or os.getenv("CC_DOCSTORE_PATH", ".cache/cc_vectorstore")
        self.embedding_model = embedding_model
        self.embedding_provider = embedding_provider
        self.collection_name = collection_name
        self.include_local_docs = include_local_docs
        self.include_web_docs = include_web_docs
        
        # Initialize embeddings based on provider
        self.embeddings = self._get_embeddings()
        
        if self.include_local_docs:
            self.local_loader = CartonCloudLocalDocsLoader(docs_directories=local_docs_dirs)
        
        # Ensure vectorstore directory exists
        Path(self.vectorstore_path).mkdir(parents=True, exist_ok=True)
    
    def _get_embeddings(self):
        """Get embeddings instance based on provider.
        
        Returns:
            Embeddings instance
        """
        if self.embedding_provider == "openai":
            return OpenAIEmbeddings(model=self.embedding_model)
        else:
            logger.warning(f"Unsupported embedding provider: {self.embedding_provider}, falling back to OpenAI")
            return OpenAIEmbeddings(model=self.embedding_model)
    
    def load_web_documents(self) -> List[Document]:
        """Load documents from web-scraped markdown files.
        
        Returns:
            List of documents from web scraping
        """
        if not self.include_web_docs:
            return []
        
        logger.info("Loading web-scraped documents")
        
        # Look for web-scraped documents in the docs directory
        web_docs_dir = Path("docs/cartoncloud")
        
        if not web_docs_dir.exists():
            logger.warning(f"Web docs directory not found: {web_docs_dir}")
            return []
        
        documents = []
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        for md_file in web_docs_dir.glob("*.md"):
            try:
                with open(md_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extract metadata from frontmatter
                metadata = {"source": str(md_file), "loader_type": "web_scraping"}
                
                if content.startswith("---"):
                    end_frontmatter = content.find("---", 3)
                    if end_frontmatter != -1:
                        frontmatter = content[3:end_frontmatter]
                        content = content[end_frontmatter + 3:].strip()
                        
                        # Parse simple frontmatter
                        for line in frontmatter.strip().split('\n'):
                            if ':' in line:
                                key, value = line.split(':', 1)
                                metadata[key.strip()] = value.strip()
                
                # Split into chunks
                chunks = text_splitter.split_text(content)
                
                for i, chunk in enumerate(chunks):
                    doc_metadata = metadata.copy()
                    doc_metadata.update({
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "content_type": "web_documentation"
                    })
                    
                    documents.append(Document(
                        page_content=chunk,
                        metadata=doc_metadata
                    ))
                    
            except Exception as e:
                logger.error(f"Error processing web doc {md_file}: {e}")
        
        logger.info(f"Loaded {len(documents)} chunks from web documents")
        return documents
    
    def load_all_documents(self) -> List[Document]:
        """Load documents from all configured sources.
        
        Returns:
            Combined list of all documents
        """
        all_documents = []
        
        # Load local documents
        if self.include_local_docs:
            try:
                local_docs = self.local_loader.load_all_documents()
                all_documents.extend(local_docs)
                logger.info(f"Added {len(local_docs)} local document chunks")
            except Exception as e:
                logger.error(f"Error loading local documents: {e}")
        
        # Load web documents
        if self.include_web_docs:
            try:
                web_docs = self.load_web_documents()
                all_documents.extend(web_docs)
                logger.info(f"Added {len(web_docs)} web document chunks")
            except Exception as e:
                logger.error(f"Error loading web documents: {e}")
        
        logger.info(f"Total documents loaded: {len(all_documents)}")
        return all_documents
    
    def build_vectorstore(
        self,
        documents: Optional[List[Document]] = None,
        force_rebuild: bool = False
    ) -> Chroma:
        """Build or update the vector store with documents.
        
        Args:
            documents: Documents to embed (loads all if None)
            force_rebuild: Whether to rebuild from scratch
            
        Returns:
            Chroma vector store instance
        """
        vectorstore_dir = Path(self.vectorstore_path)
        
        # Check if we need to rebuild
        if force_rebuild and vectorstore_dir.exists():
            logger.info("Force rebuilding - removing existing vectorstore")
            import shutil
            shutil.rmtree(vectorstore_dir)
            vectorstore_dir.mkdir(parents=True, exist_ok=True)
        
        # Load documents if not provided
        if documents is None:
            documents = self.load_all_documents()
        
        if not documents:
            logger.warning("No documents to embed")
            return None
        
        logger.info(f"Building vector store with {len(documents)} documents")
        
        # Create or update vector store
        if vectorstore_dir.exists() and not force_rebuild:
            # Load existing vectorstore and add new documents
            try:
                vectorstore = Chroma(
                    persist_directory=str(vectorstore_dir),
                    embedding_function=self.embeddings,
                    collection_name=self.collection_name
                )
                
                # Add new documents
                vectorstore.add_documents(documents)
                logger.info("Added documents to existing vector store")
                
            except Exception as e:
                logger.warning(f"Error updating existing vectorstore: {e}. Rebuilding...")
                # Fall back to rebuilding
                vectorstore = Chroma.from_documents(
                    documents=documents,
                    embedding=self.embeddings,
                    persist_directory=str(vectorstore_dir),
                    collection_name=self.collection_name
                )
        else:
            # Create new vectorstore
            vectorstore = Chroma.from_documents(
                documents=documents,
                embedding=self.embeddings,
                persist_directory=str(vectorstore_dir),
                collection_name=self.collection_name
            )
            logger.info("Created new vector store")
        
        logger.info(f"Vector store built and persisted to {vectorstore_dir}")
        return vectorstore
    
    def get_vectorstore(self) -> Optional[Chroma]:
        """Get existing vector store instance.
        
        Returns:
            Chroma vector store or None if not found
        """
        vectorstore_dir = Path(self.vectorstore_path)
        
        if not vectorstore_dir.exists():
            logger.warning(f"Vector store not found at {self.vectorstore_path}")
            return None
        
        try:
            vectorstore = Chroma(
                persist_directory=str(vectorstore_dir),
                embedding_function=self.embeddings,
                collection_name=self.collection_name
            )
            return vectorstore
        except Exception as e:
            logger.error(f"Error loading vector store: {e}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the documentation system.
        
        Returns:
            Dictionary with system statistics
        """
        stats = {
            "vectorstore_path": self.vectorstore_path,
            "vectorstore_exists": Path(self.vectorstore_path).exists(),
            "embedding_model": self.embedding_model,
            "collection_name": self.collection_name,
            "includes_local_docs": self.include_local_docs,
            "includes_web_docs": self.include_web_docs,
        }
        
        # Get vectorstore stats
        vectorstore = self.get_vectorstore()
        if vectorstore:
            try:
                collection = vectorstore._collection
                stats["document_count"] = collection.count()
                stats["vectorstore_status"] = "available"
            except Exception as e:
                stats["vectorstore_status"] = f"error: {e}"
                stats["document_count"] = 0
        else:
            stats["vectorstore_status"] = "not_found"
            stats["document_count"] = 0
        
        # Get local docs stats
        if self.include_local_docs:
            local_stats = self.local_loader.get_stats()
            stats["local_docs"] = local_stats
        
        # Get web docs stats
        if self.include_web_docs:
            web_docs_dir = Path("docs/cartoncloud")
            web_stats = {
                "web_docs_dir": str(web_docs_dir),
                "web_docs_exist": web_docs_dir.exists(),
                "web_doc_files": len(list(web_docs_dir.glob("*.md"))) if web_docs_dir.exists() else 0
            }
            stats["web_docs"] = web_stats
        
        return stats
    
    def rebuild_vectorstore(self) -> bool:
        """Rebuild the entire vector store from scratch.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Rebuilding CartonCloud documentation vector store")
            
            # Load all documents
            documents = self.load_all_documents()
            
            if not documents:
                logger.error("No documents found to rebuild vectorstore")
                return False
            
            # Build vectorstore with force rebuild
            vectorstore = self.build_vectorstore(documents, force_rebuild=True)
            
            if vectorstore:
                logger.info("Vector store rebuilt successfully")
                return True
            else:
                logger.error("Failed to rebuild vector store")
                return False
                
        except Exception as e:
            logger.error(f"Error rebuilding vector store: {e}")
            return False


async def main():
    """Test the enhanced embedder."""
    logging.basicConfig(level=logging.INFO)
    
    embedder = CartonCloudDocumentEmbedder()
    
    # Get statistics
    stats = embedder.get_stats()
    print("Documentation System Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Test loading documents
    documents = embedder.load_all_documents()
    print(f"\nLoaded {len(documents)} total document chunks")
    
    # Show sample from each source type
    local_docs = [d for d in documents if d.metadata.get("loader_type") == "local_file"]
    web_docs = [d for d in documents if d.metadata.get("loader_type") == "web_scraping"]
    
    print(f"Local documents: {len(local_docs)}")
    print(f"Web documents: {len(web_docs)}")
    
    if local_docs:
        print(f"\nSample local document:")
        print(f"  Content: {local_docs[0].page_content[:100]}...")
        print(f"  Metadata: {local_docs[0].metadata}")
    
    if web_docs:
        print(f"\nSample web document:")
        print(f"  Content: {web_docs[0].page_content[:100]}...")
        print(f"  Metadata: {web_docs[0].metadata}")


if __name__ == "__main__":
    asyncio.run(main())
