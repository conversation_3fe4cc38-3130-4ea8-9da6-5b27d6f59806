from decimal import Decimal

import yaml

from scripts.convert_ratecard import RateCardEntry
from scripts.convert_ratecard import normalize_service_id
from scripts.convert_ratecard import parse_price_string
from scripts.convert_ratecard import write_yaml_file
from src.tools import rate_card_lookup


def test_parse_price_string_basic():
    price, unit, min_charge, notes = parse_price_string("$5.00 per pallet per week (min $40.00)")
    assert price == Decimal("5.00")
    assert unit == "pallet/week"
    assert min_charge == Decimal("40.00")
    assert notes is None

    price, unit, min_charge, notes = parse_price_string("$2.50 per sqm/cbm per week")
    assert price == Decimal("2.50")
    assert unit == "sqm/week"
    assert min_charge is None
    assert notes is None

    price, unit, min_charge, notes = parse_price_string("$15.00 per movement")
    assert price == Decimal("15.00")
    assert unit == "movement"
    assert min_charge is None
    assert notes is None

    price, unit, min_charge, notes = parse_price_string("$10.00")
    assert price == Decimal("10.00")
    assert unit == "unit"
    assert min_charge is None
    assert notes is not None


def test_normalize_service_id():
    assert normalize_service_id("Pallet Storage Per Week") == "pallet_storage_per_week"
    assert normalize_service_id("Pick & Pack - Small") == "pick_pack_small"
    assert normalize_service_id("  Movement  ") == "movement"


def test_write_and_read_yaml(tmp_path):
    entries = [
        RateCardEntry(
            id="pallet_storage_per_week",
            service="Pallet Storage Per Week",
            price_ex_gst=Decimal("5.00"),
            unit="pallet/week",
            min_charge=Decimal("40.00"),
            category="storage",
            notes=None
        ),
        RateCardEntry(
            id="pick_pack_small",
            service="Pick & Pack - Small",
            price_ex_gst=Decimal("2.00"),
            unit="unit",
            min_charge=None,
            category="handling",
            notes="Small items"
        ),
    ]
    yaml_path = tmp_path / "test_ratecard.yml"
    write_yaml_file(entries, yaml_path)
    with open(yaml_path, encoding="utf-8") as f:
        data = yaml.safe_load(f)
    assert "rate_cards" in data
    assert len(data["rate_cards"]) == 2
    assert data["rate_cards"][0]["id"] == "pallet_storage_per_week"
    assert data["rate_cards"][1]["service"] == "Pick & Pack - Small"


def test_rate_card_lookup_quote_logic(tmp_path, monkeypatch):
    # Create a fake rate card YAML
    entries = [
        RateCardEntry(
            id="pallet_storage_per_week",
            service="Pallet Storage Per Week",
            price_ex_gst=Decimal("5.00"),
            unit="pallet/week",
            min_charge=Decimal("40.00"),
            category="storage",
            notes=None
        ),
        RateCardEntry(
            id="pick_pack_small",
            service="Pick & Pack - Small",
            price_ex_gst=Decimal("2.00"),
            unit="unit",
            min_charge=None,
            category="handling",
            notes="Small items"
        ),
    ]
    yaml_path = tmp_path / "test_ratecard.yml"
    write_yaml_file(entries, yaml_path)

    # Patch environment to use this file
    monkeypatch.setenv("RATECARD_FILE", str(yaml_path))
    # Force reload
    if hasattr(rate_card_lookup, "_rate_card_lookup"):
        rate_card_lookup._rate_card_lookup = None

    # Test quote below min charge
    result = rate_card_lookup.lookup("pallet_storage_per_week", qty=2)
    assert result["service_id"] == "pallet_storage_per_week"
    assert result["price_ex_gst"] == 5.0
    assert result["quantity"] == 2.0
    assert result["extended_price_ex_gst"] == 40.0  # min charge applied
    assert result["min_charge_applied"] is True

    # Test quote above min charge
    result = rate_card_lookup.lookup("pallet_storage_per_week", qty=10)
    assert result["extended_price_ex_gst"] == 50.0
    assert result["min_charge_applied"] is False

    # Test quote for service without min charge
    result = rate_card_lookup.lookup("pick_pack_small", qty=3)
    assert result["extended_price_ex_gst"] == 6.0
    assert result["min_charge_applied"] is False

    # Test search by service name
    lookup_service = rate_card_lookup.get_rate_card_lookup()
    matches = lookup_service.search_by_service_name("pallet storage")
    assert any(m.id == "pallet_storage_per_week" for m in matches)
