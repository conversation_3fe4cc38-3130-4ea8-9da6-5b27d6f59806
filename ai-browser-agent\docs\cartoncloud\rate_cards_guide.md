---
title: "CartonCloud Rate Cards Guide"
content_type: "user_guide"
topic: "rate_cards"
---

# Rate Cards in CartonCloud

Rate cards are fundamental pricing structures in CartonCloud that define how much customers are charged for various services.

## Rate Card Types

CartonCloud supports several types of rate cards:

### Transport Rate Cards
- **Distance-based charges**: Charges calculated based on distance traveled
- **Zone-based charges**: Fixed charges based on delivery zones
- **Fuel surcharges**: Variable charges based on fuel price fluctuations
- **Minimum charges**: Minimum charge thresholds for orders

### Warehouse Rate Cards
- **Storage charges**: Monthly or daily storage fees
- **Handling charges**: Pick, pack, and processing fees
- **Receiving charges**: Inbound processing fees
- **Special services**: Value-added service charges

## API Endpoints

The Rate Cards API provides the following endpoints:

### GET /api/v2/rate-cards
Retrieve all rate cards for the authenticated account.

**Response:**
```json
{
  "data": [
    {
      "id": 123,
      "name": "Standard Transport Rates",
      "type": "transport",
      "active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### POST /api/v2/rate-cards
Create a new rate card.

**Request Body:**
```json
{
  "name": "New Rate Card",
  "type": "transport",
  "charges": [
    {
      "charge_code": "DELIVERY",
      "rate": 15.50,
      "unit": "per_delivery"
    }
  ]
}
```

### PUT /api/v2/rate-cards/{id}
Update an existing rate card.

### DELETE /api/v2/rate-cards/{id}
Delete a rate card (if not in use).

## Rate Card Configuration

Rate cards contain multiple charge codes, each with specific pricing rules:

- **Charge Code**: Unique identifier for the charge type
- **Rate**: The price amount
- **Unit**: How the charge is calculated (per_kg, per_m3, per_delivery, etc.)
- **Minimum**: Minimum charge amount
- **Maximum**: Maximum charge amount (optional)

## Units of Measure

CartonCloud supports various units of measure for rate calculations:

- **Weight**: kg, lb, g, oz, t
- **Volume**: m3, ft3, l, gal
- **Count**: per_item, per_pallet, per_carton
- **Distance**: km, mile
- **Time**: per_hour, per_day, per_month

## Best Practices

1. **Rate Card Naming**: Use descriptive names that indicate the service type and customer segment
2. **Regular Updates**: Review and update rates quarterly or as market conditions change
3. **Testing**: Use invoice simulation to test rate cards before activation
4. **Documentation**: Maintain clear documentation of all charge codes and their purposes
5. **Version Control**: Keep track of rate card changes for audit purposes

## Common Issues

### Rate Mismatch Errors
- Ensure charge codes match exactly between rate cards and orders
- Verify units of measure are consistent
- Check for missing charge codes in rate cards

### Invoice Discrepancies
- Use invoice simulation to preview charges before finalizing
- Verify customer-specific rate cards are properly assigned
- Check for overlapping rate cards that might cause conflicts
