{"summary": {"total": 4, "passed": 3, "partial": 0, "failed": 1, "success_rate": 75.0, "assessment": "fair", "duration": 54.06158924102783}, "results": [{"test_name": "<PERSON><PERSON>", "status": "failed", "start_time": "2025-05-24T15:42:08.974957", "command": "c:\\Users\\<USER>\\Desktop\\cartoncloud agent\\.venv\\Scripts\\python.exe -m ruff check src tests --statistics", "errors": ["Command failed with code 1"], "output": "72\tW293 \t[*] blank-line-with-whitespace\n24\tUP006\t[*] non-pep585-annotation\n 4\tI001 \t[*] unsorted-imports\n 4\tUP035\t[ ] deprecated-import\n 3\tUP007\t[*] non-pep604-annotation-union\n 2\tF401 \t[*] unused-import\n 1\tW291 \t[ ] trailing-whitespace\n 1\tF841 \t[ ] unused-variable\nFound 111 errors.\n[*] 105 fixable with the `--fix` option (2 hidden fixes can be enabled with the `--unsafe-fixes` option).\n", "return_code": 1, "end_time": "2025-05-24T15:42:09.153383"}, {"test_name": "component_imports", "status": "passed", "start_time": "2025-05-24T15:42:09.170561", "errors": [], "components_tested": ["✅ Gradio: Gradio 5.31.0", "✅ FastAPI: FastAPI OK", "✅ Playwright: Playwright OK", "✅ SQLModel: SQLModel OK", "✅ Gradio UI: Gradio: True, Agent: <PERSON><PERSON><PERSON>"], "end_time": "2025-05-24T15:42:20.819104"}, {"test_name": "mock_agent_functionality", "status": "passed", "start_time": "2025-05-24T15:42:20.819104", "errors": [], "scenarios_tested": ["✅ Using mock agent (expected)", "✅ Scenario 1: Response generated (456 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 2: Response generated (473 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 3: Response generated (481 chars)", "   ✅ Contains CartonCloud concepts", "   ✅ Mentions approval workflow", "✅ Scenario 4: Response generated (466 chars)", "   ✅ Contains CartonCloud concepts", "✅ Scenario 5: Response generated (475 chars)", "   ✅ Contains CartonCloud concepts"], "end_time": "2025-05-24T15:42:42.736834"}, {"test_name": "edge_cases", "status": "passed", "start_time": "2025-05-24T15:42:42.739740", "errors": [], "cases_tested": ["✅ Empty input: Handled gracefully", "✅ Very long input: Handled gracefully", "✅ Emoji input: Handled gracefully", "✅ SQL injection attempt: Handled gracefully", "✅ Dangerous request: Handled gracefully", "✅ Unicode input: Handled gracefully"], "end_time": "2025-05-24T15:43:03.026494"}], "timestamp": "2025-05-24T15:43:03.036546"}