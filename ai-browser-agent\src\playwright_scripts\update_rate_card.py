"""Update rate card charges via CartonCloud UI."""

import logging
from typing import Dict, Any, Optional
from playwright.async_api import Page

logger = logging.getLogger(__name__)


async def run(page: Page, **kwargs) -> Dict[str, Any]:
    """Update a single rate card charge row in CartonCloud UI.
    
    Args:
        page: Playwright page instance
        **kwargs: Required parameters:
            - rate_card_id: ID of the rate card to edit (optional, will search if not provided)
            - charge_code: Charge code to update
            - new_rate: New rate value to set (optional)
            - new_uom: New unit of measure (optional)
            - customer_id: Customer ID for navigation
            - service_id: Service ID from rate card lookup (optional)
            - mismatch_type: Type of mismatch - "uom", "price", or "both" (optional)
            
    Returns:
        Dictionary with operation result and details
    """
    charge_code = kwargs.get("charge_code")
    new_rate = kwargs.get("new_rate")
    new_uom = kwargs.get("new_uom")
    customer_id = kwargs.get("customer_id")
    rate_card_id = kwargs.get("rate_card_id")
    service_id = kwargs.get("service_id")
    mismatch_type = kwargs.get("mismatch_type", "unknown")
    
    if not charge_code or not customer_id:
        return {
            "success": False,
            "error": "Missing required parameters: charge_code, customer_id",
        }
    
    if not new_rate and not new_uom:
        return {
            "success": False,
            "error": "At least one of new_rate or new_uom must be provided",
        }
    
    try:
        logger.info(f"Updating rate card charge {charge_code} for customer {customer_id} (type: {mismatch_type})")
        
        # If no rate_card_id provided, navigate to customer rate cards and find the active one
        if not rate_card_id:
            await page.goto(f"/customers/{customer_id}/rate-cards")
            await page.wait_for_load_state("networkidle")
            
            # Find the active rate card
            active_rate_card = page.locator('[data-status="active"]').first
            if await active_rate_card.is_visible():
                rate_card_link = active_rate_card.locator('a[href*="/rate-cards/"]').first
                rate_card_url = await rate_card_link.get_attribute("href")
                rate_card_id = rate_card_url.split("/rate-cards/")[1].split("/")[0]
            else:
                return {
                    "success": False,
                    "error": "No active rate card found for customer",
                }
        
        # Navigate to rate cards page
        await page.goto(f"/customers/{customer_id}/rate-cards/{rate_card_id}/edit")
        await page.wait_for_load_state("networkidle")
          # Wait for the rate card editor to load
        await page.wait_for_selector('[data-testid="rate-card-editor"]', timeout=10000)
        
        # Find the charge row by charge code
        charge_row = page.locator(f'[data-charge-code="{charge_code}"]').first
        if not await charge_row.is_visible():
            # Try alternative selector
            charge_row = page.locator("tr").filter(has_text=charge_code).first
            
        if not await charge_row.is_visible():
            return {
                "success": False,
                "error": f"Charge code {charge_code} not found in rate card",
            }
        
        updates_made = []
        
        # Update rate if provided
        if new_rate:
            rate_field = charge_row.locator('input[data-field="rate"], input[name*="rate"]').first
            if await rate_field.is_visible():
                old_rate = await rate_field.input_value()
                await rate_field.click()
                await rate_field.fill("")  # Clear existing value
                await rate_field.fill(str(new_rate))
                updates_made.append(f"Rate: {old_rate} → {new_rate}")
                logger.info(f"Updated rate from {old_rate} to {new_rate}")
            else:
                logger.warning("Rate field not found for charge")
        
        # Update UoM if provided
                # Update UoM if provided
        if new_uom:
            uom_field = charge_row.locator('select[data-field="uom"], select[name*="uom"]').first
            if await uom_field.is_visible():
                old_uom = await uom_field.input_value()
                await uom_field.select_option(label=new_uom)
                updates_made.append(f"UoM: {old_uom} → {new_uom}")
                logger.info(f"Updated UoM from {old_uom} to {new_uom}")
            else:
                # Try input field if not a select
                uom_input = charge_row.locator('input[data-field="uom"], input[name*="uom"]').first
                if await uom_input.is_visible():
                    old_uom = await uom_input.input_value()
                    await uom_input.fill(new_uom)
                    updates_made.append(f"UoM: {old_uom} → {new_uom}")
                    logger.info(f"Updated UoM from {old_uom} to {new_uom}")
                else:
                    logger.warning("UoM field not found for charge")
        
        # Save the changes
        save_button = page.get_by_role("button", name="Save")
        if not await save_button.is_visible():
            save_button = page.get_by_role("button", name="Update")
        if not await save_button.is_visible():
            save_button = page.locator('button[type="submit"]').first
            
        await save_button.click()
        
        # Wait for success message or page navigation
        try:
            # Wait for either success message or redirect
            await page.wait_for_selector(
                '.alert-success, .toast-success, [data-testid="success-message"]',
                timeout=5000
            )
            success_message = "Rate card updated successfully"
        except:
            # Check if we're redirected back to rate card view
            await page.wait_for_url("**/rate-cards/**", timeout=5000)
            success_message = "Rate card update completed"
        
        logger.info(f"Successfully updated rate card {rate_card_id}")
        
        return {
            "success": True,
            "message": success_message,
            "rate_card_id": rate_card_id,
            "charge_code": charge_code,
            "new_rate": new_rate,
            "new_uom": new_uom,
            "updates_made": updates_made,
            "mismatch_type": mismatch_type,
            "service_id": service_id
        }
        
    except Exception as e:
        logger.error(f"Error updating rate card: {e}")
        return {
            "success": False,
            "error": str(e),
            "rate_card_id": rate_card_id,
            "charge_code": charge_code,
        }
