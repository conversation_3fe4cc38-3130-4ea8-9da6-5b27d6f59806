{"version": 3, "file": "Example17-j2bYHKVQ.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example17.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport { J as JSON } from \"./JSON.js\";\nconst css = {\n  code: \".container.svelte-v7ph9u img{width:100%;height:100%}.container.selected.svelte-v7ph9u{border-color:var(--border-color-accent)}.border.table.svelte-v7ph9u{border:1px solid var(--border-color-primary)}.container.table.svelte-v7ph9u{margin:0 auto;border-radius:var(--radius-lg);overflow:hidden;width:100%;height:100%;max-width:var(--size-40);max-height:var(--size-20);object-fit:cover}.container.gallery.svelte-v7ph9u{width:100%;max-width:100%;object-fit:cover;max-width:var(--size-40);max-height:var(--size-20);overflow:hidden}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import JSON from \\\\\"./shared/JSON.svelte\\\\\";\\\\nexport let value;\\\\nexport let theme_mode = \\\\\"system\\\\\";\\\\nlet show_indices = false;\\\\nlet label_height = 0;\\\\nexport let type;\\\\nexport let selected = false;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"container\\\\\"\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass:border={value}\\\\n>\\\\n\\\\t{#if value}\\\\n\\\\t\\\\t<JSON\\\\n\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\topen={true}\\\\n\\\\t\\\\t\\\\t{theme_mode}\\\\n\\\\t\\\\t\\\\t{show_indices}\\\\n\\\\t\\\\t\\\\t{label_height}\\\\n\\\\t\\\\t\\\\tinteractive={false}\\\\n\\\\t\\\\t\\\\tshow_copy_button={false}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.container :global(img) {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.selected {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.border.table {\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.container.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.gallery {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tmax-width: 100%;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8BC,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,UAAU,uBAAU,CACnB,YAAY,CAAE,IAAI,qBAAqB,CACxC,CACA,OAAO,oBAAO,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAC7C,CAEA,UAAU,oBAAO,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,KACb,CAEA,UAAU,sBAAS,CAClB,KAAK,CAAE,IAAI,CACX,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,QAAQ,CAAE,MACX\"}'\n};\nlet show_indices = false;\nlet label_height = 0;\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { theme_mode = \"system\" } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  $$result.css.add(css);\n  return `<div class=\"${[\n    \"container svelte-v7ph9u\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\") + \" \" + (value ? \"border\" : \"\")\n  ].join(\" \").trim()}\">${value ? `${validate_component(JSON, \"JSON\").$$render(\n    $$result,\n    {\n      value,\n      open: true,\n      theme_mode,\n      show_indices,\n      label_height,\n      interactive: false,\n      show_copy_button: false\n    },\n    {},\n    {}\n  )}` : ``} </div>`;\n});\nexport {\n  Example as default\n};\n"], "names": ["JSON"], "mappings": ";;;;;;;;;;AAEA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,+gBAA+gB;AACvhB,EAAE,GAAG,EAAE,miEAAmiE;AAC1iE,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,YAAY,GAAG,CAAC,CAAC;AAChB,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,yBAAyB;AAC7B,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAC;AACxJ,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ;AAC7E,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,UAAU;AAChB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpB,CAAC;;;;"}