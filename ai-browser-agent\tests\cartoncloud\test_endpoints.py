import pytest
import warnings
from unittest.mock import patch, MagicMock
from src.integrations.cartoncloud import endpoints
from src.integrations.cartoncloud.api import CartonCloudAPIError
from src.integrations.cartoncloud.models import RateCard, PurchaseOrder, SalesOrder, BulkChargeReportLine


def test_deprecation_warning(monkeypatch):
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        endpoints._deprecation_warning("foo", "bar")
        assert any("foo is deprecated. Use bar instead." in str(warn.message) for warn in w)


def test_request_delegates_and_warns(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"ok": True}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        result = endpoints.request("GET", "/v1/test")
        assert result == {"ok": True}
        assert any("request() is deprecated" in str(warn.message) for warn in w)


def test_request_raises_on_api_error(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.side_effect = CartonCloudAPIError("fail", status_code=400)
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    with pytest.raises(Exception):
        endpoints.request("GET", "/v1/test")


def test_list_rate_cards(monkeypatch):
    mock_api = MagicMock()
    mock_api.get_rate_cards.return_value = iter([RateCard(id="1"), RateCard(id="2")])
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        result = endpoints.list_rate_cards(customer_id="123", status="active")
        assert isinstance(result, list)
        assert all(isinstance(x, RateCard) for x in result)
        assert any("list_rate_cards() is deprecated" in str(warn.message) for warn in w)


def test_get_rate_card_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": {"id": "1"}}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        rc = endpoints.get_rate_card("1")
        assert isinstance(rc, RateCard)
        assert rc.id == "1"
        assert any("get_rate_card() is deprecated" in str(warn.message) for warn in w)


def test_get_rate_card_not_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.side_effect = CartonCloudAPIError("not found", status_code=404)
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    rc = endpoints.get_rate_card("doesnotexist")
    assert rc is None


def test_create_purchase_order(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": {"id": "po1"}}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    po = PurchaseOrder(id=None)
    result = endpoints.create_purchase_order(po)
    assert isinstance(result, PurchaseOrder)
    assert result.id == "po1"


def test_get_purchase_order_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": {"id": "po1"}}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    po = endpoints.get_purchase_order("po1")
    assert isinstance(po, PurchaseOrder)
    assert po.id == "po1"


def test_get_purchase_order_not_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.side_effect = CartonCloudAPIError("not found", status_code=404)
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    po = endpoints.get_purchase_order("doesnotexist")
    assert po is None


def test_create_sales_order(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": {"id": "so1"}}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    so = SalesOrder(id=None)
    result = endpoints.create_sales_order(so)
    assert isinstance(result, SalesOrder)
    assert result.id == "so1"


def test_get_sales_order_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": {"id": "so1"}}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    so = endpoints.get_sales_order("so1")
    assert isinstance(so, SalesOrder)
    assert so.id == "so1"


def test_get_sales_order_not_found(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.side_effect = CartonCloudAPIError("not found", status_code=404)
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    so = endpoints.get_sales_order("doesnotexist")
    assert so is None


def test_get_bulk_charges_report(monkeypatch):
    mock_api = MagicMock()
    mock_api.make_request.return_value = {"data": [{"id": "c1"}, {"id": "c2"}]}
    monkeypatch.setattr(endpoints, "_get_api_instance", lambda: mock_api)
    from datetime import datetime, timedelta
    start = datetime.now() - timedelta(days=2)
    end = datetime.now() - timedelta(days=1)
    result = endpoints.get_bulk_charges_report(start, end)
    assert isinstance(result, list)
    assert all(isinstance(x, BulkChargeReportLine) for x in result)


def test_get_yesterday_bulk_charges(monkeypatch):
    # Just test that it calls get_bulk_charges_report with correct args
    called = {}
    def fake_get_bulk_charges_report(start, end, customer_id, token):
        called['called'] = True
        return [BulkChargeReportLine(id="c1"), BulkChargeReportLine(id="c2")]
    monkeypatch.setattr(endpoints, "get_bulk_charges_report", fake_get_bulk_charges_report)
    result = endpoints.get_yesterday_bulk_charges()
    assert called['called']
    assert isinstance(result, list)
    assert all(isinstance(x, BulkChargeReportLine) for x in result)
