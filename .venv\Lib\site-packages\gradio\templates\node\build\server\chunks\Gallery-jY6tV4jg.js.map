{"version": 3, "file": "Gallery-jY6tV4jg.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Gallery.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute, escape, each } from \"svelte/internal\";\nimport { B as BlockLabel, q as Image, k as Empty, I as IconButtonWrapper, h as IconButton, D as Download, F as FullscreenButton, j as ShareButton, G as Clear, H as Play } from \"./client.js\";\nimport { M as ModifyUpload } from \"./ModifyUpload.js\";\nimport { I as Image$1 } from \"./Image.js\";\nimport \"./ImagePreview.js\";\nimport { V as Video } from \"./Video.js\";\nimport { d as dequal } from \"./index8.js\";\nimport { createEventDispatcher, onMount, tick } from \"svelte\";\nimport { u as uploadToHuggingFace } from \"./utils.js\";\nasync function format_gallery_for_sharing(value) {\n  if (!value)\n    return \"\";\n  let urls = await Promise.all(\n    value.map(async ([image, _]) => {\n      if (image === null || !image.url)\n        return \"\";\n      return await uploadToHuggingFace(image.url);\n    })\n  );\n  return `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`).join(\"\")}</div>`;\n}\nconst css = {\n  code: 'button.svelte-842rpi.svelte-842rpi{width:var(--size-full);height:var(--size-full);object-fit:contain;display:block;border-radius:var(--radius-lg)}.preview.svelte-842rpi.svelte-842rpi{display:flex;position:absolute;flex-direction:column;z-index:var(--layer-2);border-radius:calc(var(--block-radius) - var(--block-border-width));-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);width:var(--size-full);height:var(--size-full)}.preview.minimal.svelte-842rpi.svelte-842rpi{width:fit-content;height:fit-content}.preview.svelte-842rpi.svelte-842rpi::before{content:\"\";position:absolute;z-index:var(--layer-below);background:var(--background-fill-primary);opacity:0.9;width:var(--size-full);height:var(--size-full)}.fixed-height.svelte-842rpi.svelte-842rpi{min-height:var(--size-80);max-height:55vh}@media(min-width: 1280px){.fixed-height.svelte-842rpi.svelte-842rpi{min-height:450px}}.media-button.svelte-842rpi.svelte-842rpi{height:calc(100% - 60px);width:100%;display:flex}.media-button.svelte-842rpi img,.media-button.svelte-842rpi video{width:var(--size-full);height:var(--size-full);object-fit:contain}.thumbnails.svelte-842rpi img{object-fit:cover;width:var(--size-full);height:var(--size-full)}.thumbnails.svelte-842rpi svg{position:absolute;top:var(--size-2);left:var(--size-2);width:50%;height:50%;opacity:50%}.preview.svelte-842rpi img.with-caption{height:var(--size-full)}.preview.minimal.svelte-842rpi img.with-caption{height:auto}.caption.svelte-842rpi.svelte-842rpi{padding:var(--size-2) var(--size-3);overflow:hidden;color:var(--block-label-text-color);font-weight:var(--weight-semibold);text-align:center;text-overflow:ellipsis;white-space:nowrap;align-self:center}.thumbnails.svelte-842rpi.svelte-842rpi{display:flex;position:absolute;bottom:0;justify-content:center;align-items:center;gap:var(--spacing-lg);width:var(--size-full);height:var(--size-14);overflow-x:scroll}.thumbnail-item.svelte-842rpi.svelte-842rpi{--ring-color:transparent;position:relative;box-shadow:inset 0 0 0 1px var(--ring-color),\\n\t\t\tvar(--shadow-drop);border:1px solid var(--border-color-primary);border-radius:var(--button-small-radius);background:var(--background-fill-secondary);aspect-ratio:var(--ratio-square);width:var(--size-full);height:var(--size-full);overflow:clip}.thumbnail-item.svelte-842rpi.svelte-842rpi:hover{--ring-color:var(--color-accent);border-color:var(--color-accent);filter:brightness(1.1)}.thumbnail-item.selected.svelte-842rpi.svelte-842rpi{--ring-color:var(--color-accent);border-color:var(--color-accent)}.thumbnail-item.svelte-842rpi svg{position:absolute;top:50%;left:50%;width:50%;height:50%;opacity:50%;transform:translate(-50%, -50%)}.thumbnail-item.svelte-842rpi video{width:var(--size-full);height:var(--size-full);overflow:hidden;object-fit:cover}.thumbnail-small.svelte-842rpi.svelte-842rpi{flex:none;transform:scale(0.9);transition:0.075s;width:var(--size-9);height:var(--size-9)}.thumbnail-small.selected.svelte-842rpi.svelte-842rpi{--ring-color:var(--color-accent);transform:scale(1);border-color:var(--color-accent)}.grid-wrap.svelte-842rpi.svelte-842rpi{position:relative;padding:var(--size-2);height:var(--size-full);overflow-y:scroll}.grid-container.svelte-842rpi.svelte-842rpi{display:grid;position:relative;grid-template-rows:repeat(var(--grid-rows), minmax(100px, 1fr));grid-template-columns:repeat(var(--grid-cols), minmax(100px, 1fr));grid-auto-rows:minmax(100px, 1fr);gap:var(--spacing-lg)}.thumbnail-lg.svelte-842rpi>img{width:var(--size-full);height:var(--size-full);overflow:hidden;object-fit:var(--object-fit)}.thumbnail-lg.svelte-842rpi:hover .caption-label.svelte-842rpi{opacity:0.5}.caption-label.svelte-842rpi.svelte-842rpi{position:absolute;right:var(--block-label-margin);bottom:var(--block-label-margin);z-index:var(--layer-1);border-top:1px solid var(--border-color-primary);border-left:1px solid var(--border-color-primary);border-radius:var(--block-label-radius);background:var(--background-fill-secondary);padding:var(--block-label-padding);max-width:80%;overflow:hidden;font-size:var(--block-label-text-size);text-align:left;text-overflow:ellipsis;white-space:nowrap}.grid-wrap.minimal.svelte-842rpi.svelte-842rpi{padding:0}',\n  map: `{\"version\":3,\"file\":\"Gallery.svelte\",\"sources\":[\"Gallery.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { BlockLabel, Empty, ShareButton, IconButton, IconButtonWrapper, FullscreenButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { Video } from \\\\\"@gradio/video/shared\\\\\";\\\\nimport { dequal } from \\\\\"dequal\\\\\";\\\\nimport { createEventDispatcher, onMount } from \\\\\"svelte\\\\\";\\\\nimport { tick } from \\\\\"svelte\\\\\";\\\\nimport { Download, Image as ImageIcon, Clear, Play } from \\\\\"@gradio/icons\\\\\";\\\\nimport { FileData } from \\\\\"@gradio/client\\\\\";\\\\nimport { format_gallery_for_sharing } from \\\\\"./utils\\\\\";\\\\nexport let show_label = true;\\\\nexport let label;\\\\nexport let value = null;\\\\nexport let columns = [2];\\\\nexport let rows = void 0;\\\\nexport let height = \\\\\"auto\\\\\";\\\\nexport let preview;\\\\nexport let allow_preview = true;\\\\nexport let object_fit = \\\\\"cover\\\\\";\\\\nexport let show_share_button = false;\\\\nexport let show_download_button = false;\\\\nexport let i18n;\\\\nexport let selected_index = null;\\\\nexport let interactive;\\\\nexport let _fetch;\\\\nexport let mode = \\\\\"normal\\\\\";\\\\nexport let show_fullscreen_button = true;\\\\nexport let display_icon_button_wrapper_top_corner = false;\\\\nexport let fullscreen = false;\\\\nlet is_full_screen = false;\\\\nlet image_container;\\\\nconst dispatch = createEventDispatcher();\\\\nlet was_reset = true;\\\\n$: was_reset = value == null || value.length === 0 ? true : was_reset;\\\\nlet resolved_value = null;\\\\n$: resolved_value = value == null ? null : value.map((data) => {\\\\n    if (\\\\\"video\\\\\" in data) {\\\\n        return {\\\\n            video: data.video,\\\\n            caption: data.caption\\\\n        };\\\\n    }\\\\n    else if (\\\\\"image\\\\\" in data) {\\\\n        return { image: data.image, caption: data.caption };\\\\n    }\\\\n    return {};\\\\n});\\\\nlet prev_value = value;\\\\nif (selected_index == null && preview && value?.length) {\\\\n    selected_index = 0;\\\\n}\\\\nlet old_selected_index = selected_index;\\\\n$: if (!dequal(prev_value, value)) {\\\\n    if (was_reset) {\\\\n        selected_index = preview && value?.length ? 0 : null;\\\\n        was_reset = false;\\\\n    }\\\\n    else {\\\\n        if (selected_index !== null && value !== null) {\\\\n            selected_index = Math.max(0, Math.min(selected_index, value.length - 1));\\\\n        }\\\\n        else {\\\\n            selected_index = null;\\\\n        }\\\\n    }\\\\n    dispatch(\\\\\"change\\\\\");\\\\n    prev_value = value;\\\\n}\\\\n$: previous = ((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) % (resolved_value?.length ?? 0);\\\\n$: next = ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\\\\nfunction handle_preview_click(event) {\\\\n    const element = event.target;\\\\n    const x = event.offsetX;\\\\n    const width = element.offsetWidth;\\\\n    const centerX = width / 2;\\\\n    if (x < centerX) {\\\\n        selected_index = previous;\\\\n    }\\\\n    else {\\\\n        selected_index = next;\\\\n    }\\\\n}\\\\nfunction on_keydown(e) {\\\\n    switch (e.code) {\\\\n        case \\\\\"Escape\\\\\":\\\\n            e.preventDefault();\\\\n            selected_index = null;\\\\n            break;\\\\n        case \\\\\"ArrowLeft\\\\\":\\\\n            e.preventDefault();\\\\n            selected_index = previous;\\\\n            break;\\\\n        case \\\\\"ArrowRight\\\\\":\\\\n            e.preventDefault();\\\\n            selected_index = next;\\\\n            break;\\\\n        default:\\\\n            break;\\\\n    }\\\\n}\\\\n$: {\\\\n    if (selected_index !== old_selected_index) {\\\\n        old_selected_index = selected_index;\\\\n        if (selected_index !== null) {\\\\n            if (resolved_value != null) {\\\\n                selected_index = Math.max(0, Math.min(selected_index, resolved_value.length - 1));\\\\n            }\\\\n            dispatch(\\\\\"select\\\\\", {\\\\n                index: selected_index,\\\\n                value: resolved_value?.[selected_index]\\\\n            });\\\\n        }\\\\n    }\\\\n}\\\\n$: if (allow_preview) {\\\\n    scroll_to_img(selected_index);\\\\n}\\\\nlet el = [];\\\\nlet container_element;\\\\nasync function scroll_to_img(index) {\\\\n    if (typeof index !== \\\\\"number\\\\\")\\\\n        return;\\\\n    await tick();\\\\n    if (el[index] === void 0)\\\\n        return;\\\\n    el[index]?.focus();\\\\n    const { left: container_left, width: container_width } = container_element.getBoundingClientRect();\\\\n    const { left, width } = el[index].getBoundingClientRect();\\\\n    const relative_left = left - container_left;\\\\n    const pos = relative_left + width / 2 - container_width / 2 + container_element.scrollLeft;\\\\n    if (container_element && typeof container_element.scrollTo === \\\\\"function\\\\\") {\\\\n        container_element.scrollTo({\\\\n            left: pos < 0 ? 0 : pos,\\\\n            behavior: \\\\\"smooth\\\\\"\\\\n        });\\\\n    }\\\\n}\\\\nlet window_height = 0;\\\\nasync function download(file_url, name) {\\\\n    let response;\\\\n    try {\\\\n        response = await _fetch(file_url);\\\\n    }\\\\n    catch (error) {\\\\n        if (error instanceof TypeError) {\\\\n            window.open(file_url, \\\\\"_blank\\\\\", \\\\\"noreferrer\\\\\");\\\\n            return;\\\\n        }\\\\n        throw error;\\\\n    }\\\\n    const blob = await response.blob();\\\\n    const url = URL.createObjectURL(blob);\\\\n    const link = document.createElement(\\\\\"a\\\\\");\\\\n    link.href = url;\\\\n    link.download = name;\\\\n    link.click();\\\\n    URL.revokeObjectURL(url);\\\\n}\\\\n$: selected_media = selected_index != null && resolved_value != null ? resolved_value[selected_index] : null;\\\\nonMount(() => {\\\\n    document.addEventListener(\\\\\"fullscreenchange\\\\\", () => {\\\\n        is_full_screen = !!document.fullscreenElement;\\\\n    });\\\\n});\\\\n<\\/script>\\\\n\\\\n<svelte:window bind:innerHeight={window_height} />\\\\n\\\\n{#if show_label}\\\\n\\\\t<BlockLabel {show_label} Icon={ImageIcon} label={label || \\\\\"Gallery\\\\\"} />\\\\n{/if}\\\\n{#if value == null || resolved_value == null || resolved_value.length === 0}\\\\n\\\\t<Empty unpadded_box={true} size=\\\\\"large\\\\\"><ImageIcon /></Empty>\\\\n{:else}\\\\n\\\\t<div class=\\\\\"gallery-container\\\\\" bind:this={image_container}>\\\\n\\\\t\\\\t{#if selected_media && allow_preview}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\ton:keydown={on_keydown}\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"preview\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:minimal={mode === \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<IconButtonWrapper\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if show_download_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Download}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel={i18n(\\\\\"common.download\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconst image =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\"image\\\\\" in selected_media\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? selected_media?.image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: selected_media?.video;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (image == null) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\treturn;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tconst { url, orig_name } = image;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (url) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdownload(url, orig_name ?? \\\\\"image\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<FullscreenButton {fullscreen} on:fullscreen />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if show_share_button}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-button\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<ShareButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:share\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={resolved_value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tformatter={format_gallery_for_sharing}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if !is_full_screen}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Close\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected_index = null;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"preview_close\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</IconButtonWrapper>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"media-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={\\\\\"image\\\\\" in selected_media\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? (event) => handle_preview_click(event)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"height: calc(100% - {selected_media.caption\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? '80px'\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: '60px'})\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"detailed view of selected image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if \\\\\"image\\\\\" in selected_media}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"detailed-image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={selected_media.image.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={selected_media.caption || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={selected_media.caption || null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass={selected_media.caption && \\\\\"with-caption\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={selected_media.video.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={\\\\\"detailed-video\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={selected_media.caption || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloop={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tis_stream={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmuted={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tcontrols={true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t{#if selected_media?.caption}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<caption class=\\\\\"caption\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{selected_media.caption}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</caption>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:this={container_element}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"thumbnails scroll-hide\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"container_el\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each resolved_value as media, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tbind:this={el[i]}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (selected_index = i)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"thumbnail-item thumbnail-small\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selected={selected_index === i && mode !== \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={\\\\\"Thumbnail \\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(i + 1) +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\\" of \\\\\" +\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tresolved_value.length}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if \\\\\"image\\\\\" in media}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={media.image.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={media.caption || null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={\\\\\"thumbnail \\\\\" + (i + 1)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Play />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={media.video.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={media.caption || null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tis_stream={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={\\\\\"thumbnail \\\\\" + (i + 1)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloop={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass=\\\\\"grid-wrap\\\\\"\\\\n\\\\t\\\\t\\\\tclass:minimal={mode === \\\\\"minimal\\\\\"}\\\\n\\\\t\\\\t\\\\tclass:fixed-height={mode !== \\\\\"minimal\\\\\" && (!height || height == \\\\\"auto\\\\\")}\\\\n\\\\t\\\\t\\\\tclass:hidden={is_full_screen}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if interactive && selected_index === null}\\\\n\\\\t\\\\t\\\\t\\\\t<ModifyUpload {i18n} on:clear={() => (value = [])} />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"grid-container\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:pt-6={show_label}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#each resolved_value as entry, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"thumbnail-item thumbnail-lg\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selected={selected_index === i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (selected_index === null && allow_preview) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"preview_open\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected_index = i;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-label={\\\\\"Thumbnail \\\\\" + (i + 1) + \\\\\" of \\\\\" + resolved_value.length}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if \\\\\"image\\\\\" in entry}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt={entry.caption || \\\\\"\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={typeof entry.image === \\\\\"string\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t? entry.image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t: entry.image.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Play />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Video\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={entry.video.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={entry.caption || null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tis_stream={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-testid={\\\\\"thumbnail \\\\\" + (i + 1)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloop={false}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if entry.caption}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"caption-label\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{entry.caption}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style lang=\\\\\"postcss\\\\\">\\\\n\\\\t.image-container {\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\t.image-container :global(img),\\\\n\\\\tbutton {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.preview {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tz-index: var(--layer-2);\\\\n\\\\t\\\\tborder-radius: calc(var(--block-radius) - var(--block-border-width));\\\\n\\\\t\\\\t-webkit-backdrop-filter: blur(8px);\\\\n\\\\t\\\\tbackdrop-filter: blur(8px);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.preview.minimal {\\\\n\\\\t\\\\twidth: fit-content;\\\\n\\\\t\\\\theight: fit-content;\\\\n\\\\t}\\\\n\\\\n\\\\t.preview::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: var(--layer-below);\\\\n\\\\t\\\\tbackground: var(--background-fill-primary);\\\\n\\\\t\\\\topacity: 0.9;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.fixed-height {\\\\n\\\\t\\\\tmin-height: var(--size-80);\\\\n\\\\t\\\\tmax-height: 55vh;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 1280px) {\\\\n\\\\t\\\\t.fixed-height {\\\\n\\\\t\\\\t\\\\tmin-height: 450px;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.media-button {\\\\n\\\\t\\\\theight: calc(100% - 60px);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t}\\\\n\\\\t.media-button :global(img),\\\\n\\\\t.media-button :global(video) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\t.thumbnails :global(img) {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\t.thumbnails :global(svg) {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: var(--size-2);\\\\n\\\\t\\\\tleft: var(--size-2);\\\\n\\\\t\\\\twidth: 50%;\\\\n\\\\t\\\\theight: 50%;\\\\n\\\\t\\\\topacity: 50%;\\\\n\\\\t}\\\\n\\\\t.preview :global(img.with-caption) {\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.preview.minimal :global(img.with-caption) {\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: crosshair;\\\\n\\\\t}\\\\n\\\\n\\\\t.caption {\\\\n\\\\t\\\\tpadding: var(--size-2) var(--size-3);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\tfont-weight: var(--weight-semibold);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\talign-self: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnails {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-14);\\\\n\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item {\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\tinset 0 0 0 1px var(--ring-color),\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--button-small-radius);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\taspect-ratio: var(--ratio-square);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\toverflow: clip;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item:hover {\\\\n\\\\t\\\\t--ring-color: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t\\\\tfilter: brightness(1.1);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item.selected {\\\\n\\\\t\\\\t--ring-color: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item :global(svg) {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 50%;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\twidth: 50%;\\\\n\\\\t\\\\theight: 50%;\\\\n\\\\t\\\\topacity: 50%;\\\\n\\\\t\\\\ttransform: translate(-50%, -50%);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item :global(video) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-small {\\\\n\\\\t\\\\tflex: none;\\\\n\\\\t\\\\ttransform: scale(0.9);\\\\n\\\\t\\\\ttransition: 0.075s;\\\\n\\\\t\\\\twidth: var(--size-9);\\\\n\\\\t\\\\theight: var(--size-9);\\\\n\\\\t}\\\\n\\\\t.thumbnail-small.selected {\\\\n\\\\t\\\\t--ring-color: var(--color-accent);\\\\n\\\\t\\\\ttransform: scale(1);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-small > img {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tobject-fit: var(--object-fit);\\\\n\\\\t}\\\\n\\\\n\\\\t.grid-wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\toverflow-y: scroll;\\\\n\\\\t}\\\\n\\\\n\\\\t.grid-container {\\\\n\\\\t\\\\tdisplay: grid;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\\\\n\\\\t\\\\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\\\\n\\\\t\\\\tgrid-auto-rows: minmax(100px, 1fr);\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-lg > :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tobject-fit: var(--object-fit);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-lg:hover .caption-label {\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t.caption-label {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tright: var(--block-label-margin);\\\\n\\\\t\\\\tbottom: var(--block-label-margin);\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t\\\\tborder-top: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-left: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--block-label-radius);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\tpadding: var(--block-label-padding);\\\\n\\\\t\\\\tmax-width: 80%;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tfont-size: var(--block-label-text-size);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.grid-wrap.minimal {\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqXC,kCAAO,CACN,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OAAO,CACnB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,oCAAS,CACR,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,cAAc,CAAE,MAAM,CACtB,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,aAAa,CAAE,KAAK,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,CAAC,CACpE,uBAAuB,CAAE,KAAK,GAAG,CAAC,CAClC,eAAe,CAAE,KAAK,GAAG,CAAC,CAC1B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,QAAQ,oCAAS,CAChB,KAAK,CAAE,WAAW,CAClB,MAAM,CAAE,WACT,CAEA,oCAAQ,QAAS,CAChB,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,aAAa,CAAC,CAC3B,UAAU,CAAE,IAAI,yBAAyB,CAAC,CAC1C,OAAO,CAAE,GAAG,CACZ,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,yCAAc,CACb,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,IACb,CAEA,MAAO,YAAY,MAAM,CAAE,CAC1B,yCAAc,CACb,UAAU,CAAE,KACb,CACD,CAEA,yCAAc,CACb,MAAM,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACzB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IACV,CACA,2BAAa,CAAS,GAAI,CAC1B,2BAAa,CAAS,KAAO,CAC5B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OACb,CACA,yBAAW,CAAS,GAAK,CACxB,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CACA,yBAAW,CAAS,GAAK,CACxB,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,IAAI,CAAE,IAAI,QAAQ,CAAC,CACnB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GAAG,CACX,OAAO,CAAE,GACV,CACA,sBAAQ,CAAS,gBAAkB,CAClC,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,QAAQ,sBAAQ,CAAS,gBAAkB,CAC1C,MAAM,CAAE,IACT,CAMA,oCAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,QAAQ,CAAE,MAAM,CAChB,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,WAAW,CAAE,IAAI,iBAAiB,CAAC,CACnC,UAAU,CAAE,MAAM,CAClB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,MACb,CAEA,uCAAY,CACX,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,CAAC,CACT,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,MACb,CAEA,2CAAgB,CACf,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAAC;AACrC,GAAG,IAAI,aAAa,CAAC,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,QAAQ,CAAE,IACX,CAEA,2CAAe,MAAO,CACrB,YAAY,CAAE,mBAAmB,CACjC,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,MAAM,CAAE,WAAW,GAAG,CACvB,CAEA,eAAe,qCAAU,CACxB,YAAY,CAAE,mBAAmB,CACjC,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,6BAAe,CAAS,GAAK,CAC5B,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,IAAI,CAAE,GAAG,CACT,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GAAG,CACX,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,IAAI,CAChC,CAEA,6BAAe,CAAS,KAAO,CAC9B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,QAAQ,CAAE,MAAM,CAChB,UAAU,CAAE,KACb,CAEA,4CAAiB,CAChB,IAAI,CAAE,IAAI,CACV,SAAS,CAAE,MAAM,GAAG,CAAC,CACrB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB,CACA,gBAAgB,qCAAU,CACzB,YAAY,CAAE,mBAAmB,CACjC,SAAS,CAAE,MAAM,CAAC,CAAC,CACnB,YAAY,CAAE,IAAI,cAAc,CACjC,CASA,sCAAW,CACV,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,MACb,CAEA,2CAAgB,CACf,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,kBAAkB,CAAE,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAChE,qBAAqB,CAAE,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CACnE,cAAc,CAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAClC,GAAG,CAAE,IAAI,YAAY,CACtB,CAEA,2BAAa,CAAW,GAAK,CAC5B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,QAAQ,CAAE,MAAM,CAChB,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,2BAAa,MAAM,CAAC,4BAAe,CAClC,OAAO,CAAE,GACV,CAEA,0CAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,oBAAoB,CAAC,CAChC,MAAM,CAAE,IAAI,oBAAoB,CAAC,CACjC,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CACjD,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAClD,aAAa,CAAE,IAAI,oBAAoB,CAAC,CACxC,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,OAAO,CAAE,IAAI,qBAAqB,CAAC,CACnC,SAAS,CAAE,GAAG,CACd,QAAQ,CAAE,MAAM,CAChB,SAAS,CAAE,IAAI,uBAAuB,CAAC,CACvC,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,MACd,CAEA,UAAU,oCAAS,CAClB,OAAO,CAAE,CACV\"}`\n};\nconst Gallery = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let selected_media;\n  let { show_label = true } = $$props;\n  let { label } = $$props;\n  let { value = null } = $$props;\n  let { columns = [2] } = $$props;\n  let { rows = void 0 } = $$props;\n  let { height = \"auto\" } = $$props;\n  let { preview } = $$props;\n  let { allow_preview = true } = $$props;\n  let { object_fit = \"cover\" } = $$props;\n  let { show_share_button = false } = $$props;\n  let { show_download_button = false } = $$props;\n  let { i18n } = $$props;\n  let { selected_index = null } = $$props;\n  let { interactive } = $$props;\n  let { _fetch } = $$props;\n  let { mode = \"normal\" } = $$props;\n  let { show_fullscreen_button = true } = $$props;\n  let { display_icon_button_wrapper_top_corner = false } = $$props;\n  let { fullscreen = false } = $$props;\n  let is_full_screen = false;\n  let image_container;\n  const dispatch = createEventDispatcher();\n  let was_reset = true;\n  let resolved_value = null;\n  let prev_value = value;\n  if (selected_index == null && preview && value?.length) {\n    selected_index = 0;\n  }\n  let old_selected_index = selected_index;\n  let el = [];\n  let container_element;\n  async function scroll_to_img(index) {\n    if (typeof index !== \"number\")\n      return;\n    await tick();\n    if (el[index] === void 0)\n      return;\n    el[index]?.focus();\n    const { left: container_left, width: container_width } = container_element.getBoundingClientRect();\n    const { left, width } = el[index].getBoundingClientRect();\n    const relative_left = left - container_left;\n    relative_left + width / 2 - container_width / 2 + container_element.scrollLeft;\n  }\n  onMount(() => {\n    document.addEventListener(\"fullscreenchange\", () => {\n      is_full_screen = !!document.fullscreenElement;\n    });\n  });\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.columns === void 0 && $$bindings.columns && columns !== void 0)\n    $$bindings.columns(columns);\n  if ($$props.rows === void 0 && $$bindings.rows && rows !== void 0)\n    $$bindings.rows(rows);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.preview === void 0 && $$bindings.preview && preview !== void 0)\n    $$bindings.preview(preview);\n  if ($$props.allow_preview === void 0 && $$bindings.allow_preview && allow_preview !== void 0)\n    $$bindings.allow_preview(allow_preview);\n  if ($$props.object_fit === void 0 && $$bindings.object_fit && object_fit !== void 0)\n    $$bindings.object_fit(object_fit);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.selected_index === void 0 && $$bindings.selected_index && selected_index !== void 0)\n    $$bindings.selected_index(selected_index);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props._fetch === void 0 && $$bindings._fetch && _fetch !== void 0)\n    $$bindings._fetch(_fetch);\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  if ($$props.display_icon_button_wrapper_top_corner === void 0 && $$bindings.display_icon_button_wrapper_top_corner && display_icon_button_wrapper_top_corner !== void 0)\n    $$bindings.display_icon_button_wrapper_top_corner(display_icon_button_wrapper_top_corner);\n  if ($$props.fullscreen === void 0 && $$bindings.fullscreen && fullscreen !== void 0)\n    $$bindings.fullscreen(fullscreen);\n  $$result.css.add(css);\n  was_reset = value == null || value.length === 0 ? true : was_reset;\n  resolved_value = value == null ? null : value.map((data) => {\n    if (\"video\" in data) {\n      return { video: data.video, caption: data.caption };\n    } else if (\"image\" in data) {\n      return { image: data.image, caption: data.caption };\n    }\n    return {};\n  });\n  {\n    if (!dequal(prev_value, value)) {\n      if (was_reset) {\n        selected_index = preview && value?.length ? 0 : null;\n        was_reset = false;\n      } else {\n        if (selected_index !== null && value !== null) {\n          selected_index = Math.max(0, Math.min(selected_index, value.length - 1));\n        } else {\n          selected_index = null;\n        }\n      }\n      dispatch(\"change\");\n      prev_value = value;\n    }\n  }\n  {\n    {\n      if (selected_index !== old_selected_index) {\n        old_selected_index = selected_index;\n        if (selected_index !== null) {\n          if (resolved_value != null) {\n            selected_index = Math.max(0, Math.min(selected_index, resolved_value.length - 1));\n          }\n          dispatch(\"select\", {\n            index: selected_index,\n            value: resolved_value?.[selected_index]\n          });\n        }\n      }\n    }\n  }\n  ((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) % (resolved_value?.length ?? 0);\n  ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\n  {\n    if (allow_preview) {\n      scroll_to_img(selected_index);\n    }\n  }\n  selected_media = selected_index != null && resolved_value != null ? resolved_value[selected_index] : null;\n  return ` ${show_label ? `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n    $$result,\n    {\n      show_label,\n      Icon: Image,\n      label: label || \"Gallery\"\n    },\n    {},\n    {}\n  )}` : ``} ${value == null || resolved_value == null || resolved_value.length === 0 ? `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n    default: () => {\n      return `${validate_component(Image, \"ImageIcon\").$$render($$result, {}, {}, {})}`;\n    }\n  })}` : `<div class=\"gallery-container\"${add_attribute(\"this\", image_container, 0)}>${selected_media && allow_preview ? `<button class=\"${[\"preview svelte-842rpi\", mode === \"minimal\" ? \"minimal\" : \"\"].join(\" \").trim()}\">${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render(\n    $$result,\n    {\n      display_top_corner: display_icon_button_wrapper_top_corner\n    },\n    {},\n    {\n      default: () => {\n        return `${show_download_button ? `${validate_component(IconButton, \"IconButton\").$$render(\n          $$result,\n          {\n            Icon: Download,\n            label: i18n(\"common.download\")\n          },\n          {},\n          {}\n        )}` : ``} ${show_fullscreen_button ? `${validate_component(FullscreenButton, \"FullscreenButton\").$$render($$result, { fullscreen }, {}, {})}` : ``} ${show_share_button ? `<div class=\"icon-button\">${validate_component(ShareButton, \"ShareButton\").$$render(\n          $$result,\n          {\n            i18n,\n            value: resolved_value,\n            formatter: format_gallery_for_sharing\n          },\n          {},\n          {}\n        )}</div>` : ``} ${!is_full_screen ? `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Clear, label: \"Close\" }, {}, {})}` : ``}`;\n      }\n    }\n  )} <button class=\"media-button svelte-842rpi\" style=\"${\"height: calc(100% - \" + escape(selected_media.caption ? \"80px\" : \"60px\", true) + \")\"}\" aria-label=\"detailed view of selected image\">${\"image\" in selected_media ? `${validate_component(Image$1, \"Image\").$$render(\n    $$result,\n    {\n      \"data-testid\": \"detailed-image\",\n      src: selected_media.image.url,\n      alt: selected_media.caption || \"\",\n      title: selected_media.caption || null,\n      class: selected_media.caption && \"with-caption\",\n      loading: \"lazy\"\n    },\n    {},\n    {}\n  )}` : `${validate_component(Video, \"Video\").$$render(\n    $$result,\n    {\n      src: selected_media.video.url,\n      \"data-testid\": \"detailed-video\",\n      alt: selected_media.caption || \"\",\n      loading: \"lazy\",\n      loop: false,\n      is_stream: false,\n      muted: false,\n      controls: true\n    },\n    {},\n    {}\n  )}`}</button> ${selected_media?.caption ? `<caption class=\"caption svelte-842rpi\">${escape(selected_media.caption)}</caption>` : ``} <div class=\"thumbnails scroll-hide svelte-842rpi\" data-testid=\"container_el\"${add_attribute(\"this\", container_element, 0)}>${each(resolved_value, (media, i) => {\n    return `<button class=\"${[\n      \"thumbnail-item thumbnail-small svelte-842rpi\",\n      selected_index === i && mode !== \"minimal\" ? \"selected\" : \"\"\n    ].join(\" \").trim()}\"${add_attribute(\"aria-label\", \"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length, 0)}${add_attribute(\"this\", el[i], 0)}>${\"image\" in media ? `${validate_component(Image$1, \"Image\").$$render(\n      $$result,\n      {\n        src: media.image.url,\n        title: media.caption || null,\n        \"data-testid\": \"thumbnail \" + (i + 1),\n        alt: \"\",\n        loading: \"lazy\"\n      },\n      {},\n      {}\n    )}` : `${validate_component(Play, \"Play\").$$render($$result, {}, {}, {})} ${validate_component(Video, \"Video\").$$render(\n      $$result,\n      {\n        src: media.video.url,\n        title: media.caption || null,\n        is_stream: false,\n        \"data-testid\": \"thumbnail \" + (i + 1),\n        alt: \"\",\n        loading: \"lazy\",\n        loop: false\n      },\n      {},\n      {}\n    )}`} </button>`;\n  })}</div></button>` : ``} <div class=\"${[\n    \"grid-wrap svelte-842rpi\",\n    (mode === \"minimal\" ? \"minimal\" : \"\") + \" \" + (mode !== \"minimal\" && (!height || height == \"auto\") ? \"fixed-height\" : \"\") + \" \" + (is_full_screen ? \"hidden\" : \"\")\n  ].join(\" \").trim()}\">${interactive && selected_index === null ? `${validate_component(ModifyUpload, \"ModifyUpload\").$$render($$result, { i18n }, {}, {})}` : ``} <div class=\"${[\"grid-container svelte-842rpi\", show_label ? \"pt-6\" : \"\"].join(\" \").trim()}\" style=\"${\"--grid-cols:\" + escape(columns, true) + \"; --grid-rows:\" + escape(rows, true) + \"; --object-fit: \" + escape(object_fit, true) + \"; height: \" + escape(height, true) + \";\"}\">${each(resolved_value, (entry, i) => {\n    return `<button class=\"${[\n      \"thumbnail-item thumbnail-lg svelte-842rpi\",\n      selected_index === i ? \"selected\" : \"\"\n    ].join(\" \").trim()}\"${add_attribute(\"aria-label\", \"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length, 0)}>${\"image\" in entry ? `${validate_component(Image$1, \"Image\").$$render(\n      $$result,\n      {\n        alt: entry.caption || \"\",\n        src: typeof entry.image === \"string\" ? entry.image : entry.image.url,\n        loading: \"lazy\"\n      },\n      {},\n      {}\n    )}` : `${validate_component(Play, \"Play\").$$render($$result, {}, {}, {})} ${validate_component(Video, \"Video\").$$render(\n      $$result,\n      {\n        src: entry.video.url,\n        title: entry.caption || null,\n        is_stream: false,\n        \"data-testid\": \"thumbnail \" + (i + 1),\n        alt: \"\",\n        loading: \"lazy\",\n        loop: false\n      },\n      {},\n      {}\n    )}`} ${entry.caption ? `<div class=\"caption-label svelte-842rpi\">${escape(entry.caption)} </div>` : ``} </button>`;\n  })}</div></div></div>`}`;\n});\nexport {\n  Gallery as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AASA,eAAe,0BAA0B,CAAC,KAAK,EAAE;AACjD,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,IAAI,IAAI,GAAG,MAAM,OAAO,CAAC,GAAG;AAC9B,IAAI,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK;AACpC,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG;AACtC,QAAQ,OAAO,EAAE,CAAC;AAClB,MAAM,OAAO,MAAM,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAClD,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,uDAAuD,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,EAAE,GAAG,CAAC,0BAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACpJ,CAAC;AACD,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,6lIAA6lI;AACrmI,EAAE,GAAG,EAAE,CAAC,youBAAyouB,CAAC;AAClpuB,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,UAAU,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,cAAc,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,sBAAsB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClD,EAAE,IAAI,EAAE,sCAAsC,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnE,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAEvC,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC;AAC5B,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;AACzB,EAAE,IAAI,cAAc,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE,MAAM,EAAE;AAC1D,IAAI,cAAc,GAAG,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,kBAAkB,GAAG,cAAc,CAAC;AAC1C,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;AACd,EAAE,IAAI,iBAAiB,CAAC;AACxB,EAAE,eAAe,aAAa,CAAC,KAAK,EAAE;AACtC,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;AACjC,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,EAAE,CAAC;AACjB,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AAC5B,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;AACvB,IAAI,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;AACvG,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAC9D,IAAI,MAAM,aAAa,GAAG,IAAI,GAAG,cAAc,CAAC;AAChD,IAAI,aAAa,GAAG,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,iBAAiB,CAAC,UAAU,CAAC;AACnF,GAAG;AAMH,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,IAAI,OAAO,CAAC,sCAAsC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sCAAsC,IAAI,sCAAsC,KAAK,KAAK,CAAC;AACzK,IAAI,UAAU,CAAC,sCAAsC,CAAC,sCAAsC,CAAC,CAAC;AAC9F,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,SAAS,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;AACrE,EAAE,cAAc,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC9D,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACzB,MAAM,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AAC1D,KAAK,MAAM,IAAI,OAAO,IAAI,IAAI,EAAE;AAChC,MAAM,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE;AACF,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;AACpC,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,cAAc,GAAG,OAAO,IAAI,KAAK,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7D,QAAQ,SAAS,GAAG,KAAK,CAAC;AAC1B,OAAO,MAAM;AACb,QAAQ,IAAI,cAAc,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACvD,UAAU,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACnF,SAAS,MAAM;AACf,UAAU,cAAc,GAAG,IAAI,CAAC;AAChC,SAAS;AACT,OAAO;AACP,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACzB,MAAM,UAAU,GAAG,KAAK,CAAC;AACzB,KAAK;AACL,GAAG;AACH,EAAE;AACF,IAAI;AACJ,MAAM,IAAI,cAAc,KAAK,kBAAkB,EAAE;AACjD,QAAQ,kBAAkB,GAAG,cAAc,CAAC;AAC5C,QAAQ,IAAI,cAAc,KAAK,IAAI,EAAE;AACrC,UAAU,IAAI,cAAc,IAAI,IAAI,EAAE;AACtC,YAAY,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9F,WAAW;AACX,UAAU,QAAQ,CAAC,QAAQ,EAAE;AAC7B,YAAY,KAAK,EAAE,cAAc;AACjC,YAAY,KAAK,EAAE,cAAc,GAAG,cAAc,CAAC;AACnD,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,CAAC,CAAC,cAAc,IAAI,CAAC,KAAK,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AAC9F,EAAE,CAAC,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,KAAK,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AAC9D,EAAE;AACF,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,aAAa,CAAC,cAAc,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,cAAc,GAAG,cAAc,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,GAAG,cAAc,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;AAC5G,EAAE,OAAO,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAClF,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,UAAU;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE,KAAK,IAAI,SAAS;AAC/B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC3L,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,8BAA8B,EAAE,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,cAAc,IAAI,aAAa,GAAG,CAAC,eAAe,EAAE,CAAC,uBAAuB,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ;AAClS,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,kBAAkB,EAAE,sCAAsC;AAChE,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,QAAQ;AAC1B,YAAY,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAC1C,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,sBAAsB,GAAG,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AACrQ,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI;AAChB,YAAY,KAAK,EAAE,cAAc;AACjC,YAAY,SAAS,EAAE,0BAA0B;AACjD,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAoB,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAK,CAAC,CAAC,CAAC;AACjK,OAAO;AACP,KAAK;AACL,GAAG,CAAC,mDAAmD,EAAE,sBAAsB,GAAG,MAAM,CAAC,cAAc,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,+CAA+C,EAAE,OAAO,IAAI,cAAc,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC5Q,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,GAAG,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG;AACnC,MAAM,GAAG,EAAE,cAAc,CAAC,OAAO,IAAI,EAAE;AACvC,MAAM,KAAK,EAAE,cAAc,CAAC,OAAO,IAAI,IAAI;AAC3C,MAAM,KAAK,EAAE,cAAc,CAAC,OAAO,IAAI,cAAc;AACrD,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACtD,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG;AACnC,MAAM,aAAa,EAAE,gBAAgB;AACrC,MAAM,GAAG,EAAE,cAAc,CAAC,OAAO,IAAI,EAAE;AACvC,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,uCAAuC,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,6EAA6E,EAAE,aAAa,CAAC,MAAM,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK;AACvS,IAAI,OAAO,CAAC,eAAe,EAAE;AAC7B,MAAM,8CAA8C;AACpD,MAAM,cAAc,KAAK,CAAC,IAAI,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,EAAE;AAClE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC1N,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG;AAC5B,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI;AACpC,QAAQ,aAAa,EAAE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,OAAO,EAAE,MAAM;AACvB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC3H,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG;AAC5B,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI;AACpC,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,aAAa,EAAE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,OAAO,EAAE,MAAM;AACvB,QAAQ,IAAI,EAAE,KAAK;AACnB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACpB,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE;AAC1C,IAAI,yBAAyB;AAC7B,IAAI,CAAC,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,KAAK,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,cAAc,GAAG,EAAE,CAAC,GAAG,GAAG,IAAgC,EAAE,CAAC;AACtK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,WAAW,IAAI,cAAc,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,8BAA8B,EAAE,UAAU,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,cAAc,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,kBAAkB,GAAG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK;AAC1d,IAAI,OAAO,CAAC,eAAe,EAAE;AAC7B,MAAM,2CAA2C;AACjD,MAAM,cAAc,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE;AAC5C,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxL,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;AAChC,QAAQ,GAAG,EAAE,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG;AAC5E,QAAQ,OAAO,EAAE,MAAM;AACvB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAC3H,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG;AAC5B,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI;AACpC,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,aAAa,EAAE,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAQ,GAAG,EAAE,EAAE;AACf,QAAQ,OAAO,EAAE,MAAM;AACvB,QAAQ,IAAI,EAAE,KAAK;AACnB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,CAAC,yCAAyC,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACvH,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC3B,CAAC;;;;"}