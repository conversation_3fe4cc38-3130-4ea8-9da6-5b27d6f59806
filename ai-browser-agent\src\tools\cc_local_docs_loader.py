"""Local CartonCloud documentation loader for RAG system."""

import asyncio
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any

from langchain.schema import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PDFPlumberLoader
from langchain_community.document_loaders import TextLoader
from langchain_community.document_loaders import UnstructuredWordDocumentLoader

logger = logging.getLogger(__name__)


class CartonCloudLocalDocsLoader:
    """Load and process local CartonCloud documentation files."""

    SUPPORTED_EXTENSIONS = {
        '.pdf': PDFPlumberLoader,
        '.doc': UnstructuredWordDocumentLoader,
        '.docx': UnstructuredWordDocumentLoader,
        '.txt': TextLoader,
        '.md': TextLoader,
    }

    def __init__(
        self,
        docs_directories: list[str] | None = None,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        include_metadata: bool = True
    ):
        """Initialize the local docs loader.
        
        Args:
            docs_directories: List of directories to scan for documents
            chunk_size: Target chunk size for text splitting
            chunk_overlap: Overlap between chunks
            include_metadata: Whether to include file metadata
        """
        self.docs_directories = docs_directories or self._get_default_directories()
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.include_metadata = include_metadata

        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )

    def _get_default_directories(self) -> list[str]:
        """Get default directories to scan for CartonCloud docs.
        
        Returns:
            List of default directory paths
        """
        default_dirs = [
            "docs/cartoncloud",
            "data/cartoncloud",
            os.path.expanduser("~/Documents/CartonCloud"),
            os.path.expanduser("~/Downloads/CartonCloud"),
        ]

        # Add environment variable paths
        env_paths = os.getenv("CC_LOCAL_DOCS_PATHS", "").split(";")
        for path in env_paths:
            if path.strip():
                default_dirs.append(path.strip())

        return default_dirs

    def discover_documents(self) -> list[Path]:
        """Discover all supported documents in configured directories.
        
        Returns:
            List of document file paths
        """
        document_files = []

        for directory in self.docs_directories:
            dir_path = Path(directory)
            if not dir_path.exists():
                logger.warning(f"Directory not found: {directory}")
                continue

            logger.info(f"Scanning directory: {directory}")

            for file_path in dir_path.rglob("*"):
                if (file_path.is_file() and
                    file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS):
                    document_files.append(file_path)
                    logger.debug(f"Found document: {file_path}")

        logger.info(f"Discovered {len(document_files)} documents")
        return document_files

    def load_document(self, file_path: Path) -> list[Document]:
        """Load a single document and extract its content.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            List of Document objects
        """
        try:
            file_extension = file_path.suffix.lower()

            if file_extension not in self.SUPPORTED_EXTENSIONS:
                logger.warning(f"Unsupported file type: {file_extension}")
                return []

            loader_class = self.SUPPORTED_EXTENSIONS[file_extension]
            loader = loader_class(str(file_path))

            # Load document
            documents = loader.load()

            # Process and enhance metadata
            for doc in documents:
                if self.include_metadata:
                    self._enhance_metadata(doc, file_path)

            logger.debug(f"Loaded {len(documents)} pages from {file_path}")
            return documents

        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
            return []

    def _enhance_metadata(self, document: Document, file_path: Path) -> None:
        """Enhance document metadata with file information.
        
        Args:
            document: Document to enhance
            file_path: Original file path
        """
        stat = file_path.stat()

        document.metadata.update({
            "source_file": str(file_path),
            "file_name": file_path.name,
            "file_extension": file_path.suffix,
            "file_size": stat.st_size,
            "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "loader_type": "local_file",
            "content_type": self._get_content_type(file_path),
        })

    def _get_content_type(self, file_path: Path) -> str:
        """Determine content type based on file path and name.
        
        Args:
            file_path: File path to analyze
            
        Returns:
            Content type string
        """
        file_name_lower = file_path.name.lower()
        path_str_lower = str(file_path).lower()

        # Categorize based on keywords in path/filename
        if any(keyword in path_str_lower for keyword in ["api", "endpoint", "swagger"]):
            return "api_documentation"
        elif any(keyword in file_name_lower for keyword in ["rate", "pricing", "charge"]):
            return "rate_card_documentation"
        elif any(keyword in file_name_lower for keyword in ["order", "purchase", "sale"]):
            return "order_documentation"
        elif any(keyword in file_name_lower for keyword in ["guide", "tutorial", "manual"]):
            return "user_guide"
        elif any(keyword in file_name_lower for keyword in ["config", "setup", "install"]):
            return "configuration"
        else:
            return "general_documentation"

    def chunk_documents(self, documents: list[Document]) -> list[Document]:
        """Split documents into chunks for embedding.
        
        Args:
            documents: List of documents to chunk
            
        Returns:
            List of chunked documents
        """
        chunked_documents = []

        for doc in documents:
            # Split document content
            chunks = self.text_splitter.split_text(doc.page_content)

            for i, chunk in enumerate(chunks):
                # Create new document for each chunk
                chunk_metadata = doc.metadata.copy()
                chunk_metadata.update({
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "chunk_size": len(chunk),
                })

                chunked_documents.append(Document(
                    page_content=chunk,
                    metadata=chunk_metadata
                ))

        return chunked_documents

    def load_all_documents(self) -> list[Document]:
        """Load and chunk all documents from configured directories.
        
        Returns:
            List of all chunked documents ready for embedding
        """
        logger.info("Loading all local CartonCloud documents")

        # Discover all documents
        file_paths = self.discover_documents()

        if not file_paths:
            logger.warning("No documents found in configured directories")
            return []

        # Load all documents
        all_documents = []
        for file_path in file_paths:
            documents = self.load_document(file_path)
            all_documents.extend(documents)

        logger.info(f"Loaded {len(all_documents)} document pages")

        # Chunk documents
        chunked_documents = self.chunk_documents(all_documents)

        logger.info(f"Created {len(chunked_documents)} chunks from local documents")
        return chunked_documents

    def get_stats(self) -> dict[str, Any]:
        """Get statistics about local documents.
        
        Returns:
            Dictionary with document statistics
        """
        file_paths = self.discover_documents()

        stats = {
            "total_files": len(file_paths),
            "directories_scanned": len(self.docs_directories),
            "directories_found": sum(1 for d in self.docs_directories if Path(d).exists()),
            "file_types": {},
            "content_types": {},
            "total_size": 0,
        }

        # Analyze files
        for file_path in file_paths:
            ext = file_path.suffix.lower()
            content_type = self._get_content_type(file_path)

            stats["file_types"][ext] = stats["file_types"].get(ext, 0) + 1
            stats["content_types"][content_type] = stats["content_types"].get(content_type, 0) + 1
            stats["total_size"] += file_path.stat().st_size

        return stats


async def main():
    """Test the local document loader."""
    logging.basicConfig(level=logging.INFO)

    loader = CartonCloudLocalDocsLoader()

    # Get statistics
    stats = loader.get_stats()
    print("Local Document Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    # Load documents
    documents = loader.load_all_documents()
    print(f"\nLoaded {len(documents)} chunks")

    if documents:
        print("\nSample document:")
        sample = documents[0]
        print(f"Content preview: {sample.page_content[:200]}...")
        print(f"Metadata: {sample.metadata}")


if __name__ == "__main__":
    asyncio.run(main())
