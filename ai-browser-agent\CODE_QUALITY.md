# Code Quality Setup

This document outlines the code quality tools and practices implemented in this project.

## 🛠️ Tools Overview

### Linting & Formatting
- **Ruff**: Fast Python linter and formatter
- **MyPy**: Static type checking
- **Pytest**: Testing framework with async support

### Configuration Files
- `ruff.toml`: Ruff configuration
- `mypy.ini`: MyPy configuration  
- `pytest.ini`: Pytest configuration

## 🚀 Quick Start

### Install Development Dependencies
```bash
pip install -r requirements.txt
```

### Run All Checks
```bash
# Linting
python -m ruff check src tests

# Auto-fix issues
python -m ruff check src tests --fix

# Format code
python -m ruff format src tests

# Type checking
python -m mypy src tests --ignore-missing-imports

# Run tests
python -m pytest tests/ -v
```

## 📋 Pre-commit Checklist

Before committing code, ensure:

1. ✅ **Ruff linting passes**: `ruff check src tests`
2. ✅ **Code is formatted**: `ruff format src tests`
3. ✅ **Type checking passes**: `mypy src tests --ignore-missing-imports`
4. ✅ **Tests pass**: `pytest tests/ -v`

## 🔧 IDE Integration

### VS Code
Install these extensions:
- Python
- Pylance
- Ruff

Add to `.vscode/settings.json`:
```json
{
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.formatting.provider": "ruff",
    "python.analysis.typeCheckingMode": "strict"
}
```

### PyCharm
1. Install Ruff plugin
2. Configure MyPy as external tool
3. Set up pytest as test runner

## 📊 Code Quality Standards

### Ruff Rules
- **E**: pycodestyle errors
- **W**: pycodestyle warnings  
- **F**: pyflakes
- **I**: isort (import sorting)
- **B**: flake8-bugbear
- **C4**: flake8-comprehensions
- **N**: pep8-naming
- **UP**: pyupgrade

### MyPy Configuration
- Strict mode enabled
- Missing imports ignored for third-party libraries
- Untyped definitions disallowed

### Test Requirements
- Minimum 90% code coverage target
- All tests must pass
- Async tests supported via pytest-asyncio

## 🔄 CI/CD Pipeline

GitHub Actions automatically runs:
1. **Lint Check**: Ruff linting
2. **Format Check**: Code formatting validation
3. **Type Check**: MyPy static analysis
4. **Tests**: Full test suite with coverage
5. **Security**: Bandit and Safety checks

## 🐛 Common Issues & Solutions

### Import Errors
- Ensure all imports are at the top of files
- Use absolute imports: `from src.module import Class`
- Add missing dependencies to `requirements.txt`

### Type Errors
- Add type hints to function signatures
- Use `# type: ignore` sparingly for third-party issues
- Import types: `from typing import List, Dict, Optional`

### Test Failures
- Check async test decorators: `@pytest.mark.asyncio`
- Verify test isolation and cleanup
- Use proper mocking for external dependencies

## 📈 Metrics

Current code quality status:
- ✅ **Ruff**: All checks passing
- ✅ **MyPy**: Type checking configured
- ✅ **Pytest**: Test framework ready
- ✅ **CI/CD**: GitHub Actions pipeline active

## 🔗 Resources

- [Ruff Documentation](https://docs.astral.sh/ruff/)
- [MyPy Documentation](https://mypy.readthedocs.io/)
- [Pytest Documentation](https://docs.pytest.org/)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
