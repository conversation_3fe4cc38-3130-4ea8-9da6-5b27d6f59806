.gradio-container-5-31-0,
.gradio-container-5-31-0 *,
.gradio-container-5-31-0 ::before,
.gradio-container-5-31-0 ::after {
	box-sizing: border-box;
	border-width: 0;
	border-style: solid;
}

.gradio-container-5-31-0 html {
	-webkit-text-size-adjust: 100%;
	line-height: 1.5;
	font-family: var(--font-sans);
	-moz-tab-size: 4;
	tab-size: 2;
}

body {
	margin: 0;
	line-height: inherit;
}

.gradio-container-5-31-0 hr {
	border-top-width: 1px;
	height: 0;
	color: inherit;
}

.gradio-container-5-31-0 abbr:where([title]) {
	text-decoration: underline dotted;
}

.gradio-container-5-31-0 h1,
.gradio-container-5-31-0 h2,
.gradio-container-5-31-0 h3,
.gradio-container-5-31-0 h4,
.gradio-container-5-31-0 h5,
.gradio-container-5-31-0 h6 {
	font-weight: inherit;
	font-size: inherit;
}

.gradio-container-5-31-0 a {
	color: inherit;
	text-decoration: inherit;
}

.gradio-container-5-31-0 b,
.gradio-container-5-31-0 strong {
	font-weight: bolder;
}

.gradio-container-5-31-0 code,
.gradio-container-5-31-0 kbd,
.gradio-container-5-31-0 samp,
.gradio-container-5-31-0 pre {
	font-family: -var(--font-mono);
}

.gradio-container-5-31-0 small {
	font-size: 80%;
}

.gradio-container-5-31-0 sub,
.gradio-container-5-31-0 sup {
	position: relative;
	vertical-align: baseline;
	font-size: 75%;
	line-height: 0;
}

.gradio-container-5-31-0 sub {
	bottom: -0.25em;
}

.gradio-container-5-31-0 sup {
	top: -0.5em;
}

.gradio-container-5-31-0 table {
	border-color: inherit;

	text-indent: 0;
}

.gradio-container-5-31-0 button,
.gradio-container-5-31-0 input,
.gradio-container-5-31-0 optgroup,
.gradio-container-5-31-0 select,
.gradio-container-5-31-0 textarea {
	margin: 0;
	padding: 0;
	color: inherit;
	font-weight: inherit;
	font-size: 100%;
	line-height: inherit;
	font-family: inherit;
}

.gradio-container-5-31-0 button,
.gradio-container-5-31-0 select {
	text-transform: none;
}

.gradio-container-5-31-0 button,
.gradio-container-5-31-0 [type="button"],
.gradio-container-5-31-0 [type="reset"],
.gradio-container-5-31-0 [type="submit"] {
	-webkit-appearance: button;
	background-image: none;
	background-color: transparent;
}

.gradio-container-5-31-0 :-moz-focusring {
	outline: auto;
}

.gradio-container-5-31-0 :-moz-ui-invalid {
	box-shadow: none;
}

.gradio-container-5-31-0 progress {
	vertical-align: baseline;
}

.gradio-container-5-31-0 ::-webkit-inner-spin-button,
.gradio-container-5-31-0 ::-webkit-outer-spin-button {
	height: auto;
}

.gradio-container-5-31-0 [type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}

.gradio-container-5-31-0 ::-webkit-search-decoration {
	-webkit-appearance: none;
}

.gradio-container-5-31-0 ::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}

.gradio-container-5-31-0 summary {
	display: list-item;
}

.gradio-container-5-31-0 blockquote,
.gradio-container-5-31-0 dl,
.gradio-container-5-31-0 dd,
.gradio-container-5-31-0 h1,
.gradio-container-5-31-0 h2,
.gradio-container-5-31-0 h3,
.gradio-container-5-31-0 h4,
.gradio-container-5-31-0 h5,
.gradio-container-5-31-0 h6,
.gradio-container-5-31-0 hr,
.gradio-container-5-31-0 figure,
.gradio-container-5-31-0 p,
.gradio-container-5-31-0 pre {
	margin: 0;
}

.gradio-container-5-31-0 fieldset {
	margin: 0;
	padding: 0;
}

.gradio-container-5-31-0 legend {
	padding: 0;
}

.gradio-container-5-31-0 ol,
.gradio-container-5-31-0 ul,
.gradio-container-5-31-0 menu {
	margin: 0;
	padding: 0;
}

.gradio-container-5-31-0 textarea {
	resize: vertical;
}

.gradio-container-5-31-0 input::placeholder,
.gradio-container-5-31-0 textarea::placeholder {
	opacity: 1;
	color: --color-var(--color-grey-400);
}

.gradio-container-5-31-0 button,
.gradio-container-5-31-0 [role="button"] {
	cursor: pointer;
}

.gradio-container-5-31-0 :disabled {
	cursor: default;
}

.gradio-container-5-31-0 img,
.gradio-container-5-31-0 svg,
.gradio-container-5-31-0 video,
.gradio-container-5-31-0 canvas,
.gradio-container-5-31-0 audio,
.gradio-container-5-31-0 iframe,
.gradio-container-5-31-0 embed,
.gradio-container-5-31-0 object {
	display: block;
	vertical-align: middle;
}

.gradio-container-5-31-0 img,
.gradio-container-5-31-0 video {
	max-width: 100%;
	height: auto;
	margin: 0;
}

.gradio-container-5-31-0 [hidden] {
	display: none;
}

.gradio-container-5-31-0 [type="text"],
.gradio-container-5-31-0 [type="email"],
.gradio-container-5-31-0 [type="url"],
.gradio-container-5-31-0 [type="password"],
.gradio-container-5-31-0 [type="number"],
.gradio-container-5-31-0 [type="date"],
.gradio-container-5-31-0 [type="datetime-local"],
.gradio-container-5-31-0 [type="month"],
.gradio-container-5-31-0 [type="search"],
.gradio-container-5-31-0 [type="tel"],
.gradio-container-5-31-0 [type="time"],
.gradio-container-5-31-0 [type="week"],
.gradio-container-5-31-0 [multiple],
.gradio-container-5-31-0 textarea,
.gradio-container-5-31-0 select {
	--tw-shadow: 0 0 #0000;
	appearance: none;
	border-width: 1px;
	border-color: #6b7280;
	border-radius: 0px;
	background-color: #fff;
	padding-top: 0.5rem;
	padding-right: 0.75rem;
	padding-bottom: 0.5rem;
	padding-left: 0.75rem;
	font-size: 1rem;
	line-height: 1.5rem;
}

.gradio-container-5-31-0 [type="checkbox"],
.gradio-container-5-31-0 [type="radio"] {
	color-adjust: exact;
	display: inline-block;
	flex-shrink: 0;
	vertical-align: middle;
	appearance: none;
	border-width: 1px;
	background-origin: border-box;

	padding: 0;
	width: 1rem;
	height: 1rem;
	color: #2563eb;
	user-select: none;
}
.gradio-container-5-31-0 [type="checkbox"]:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

.gradio-container-5-31-0 [type="radio"]:checked {
	background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

.gradio-container-5-31-0 select {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
	background-position: right 0.5rem center;
	background-size: 1.5em 1.5em;
	background-repeat: no-repeat;
	padding-right: 2.5rem;
}

.gradio-container-5-31-0 [type="checkbox"]:checked,
.gradio-container-5-31-0 [type="radio"]:checked {
	background-position: center;
	background-size: 100% 100%;
	background-repeat: no-repeat;
}

.gradio-container-5-31-0 [type="checkbox"]:checked:hover,
.gradio-container-5-31-0 [type="checkbox"]:checked:focus,
.gradio-container-5-31-0 [type="radio"]:checked:hover,
.gradio-container-5-31-0 [type="radio"]:checked:focus {
	border-color: transparent;
}

.gradio-container-5-31-0 [type="checkbox"]:focus-visible,
.gradio-container-5-31-0 [type="checkbox"]:focus-visible,
.gradio-container-5-31-0 [type="radio"]:focus-visible,
.gradio-container-5-31-0 [type="radio"]:focus-visible {
	outline: none;
}
/*
 Custom element styles
 */
.gradio-container-5-31-0 gradio-lite {
	display: flex;
}

/*
	To avoid FOUC of custom elements
	Refs:
	https://www.abeautifulsite.net/posts/flash-of-undefined-custom-elements/#the-%3Adefined-selector
	https://github.com/pyscript/pypercard/blob/66d3550abd8e478f9389e6b87790d5985e55ef7f/static/pyscript.css#L6-L8
	*/
.gradio-container-5-31-0 gradio-lite:not(:defined) {
	display: none;
}

.gradio-container-5-31-0 .scroll-hide {
	-ms-overflow-style: none;
	scrollbar-width: none;
}

.gradio-container-5-31-0 .sr-only {
	clip: rect(0, 0, 0, 0);
	position: absolute;
	margin: -1px;
	border-width: 0;
	padding: 0;
	width: 1px;
	height: 1px;
	overflow: hidden;
	white-space: nowrap;
}

.gradio-container-5-31-0 .scroll-hide::-webkit-scrollbar {
	display: none;
}

.gradio-container-5-31-0 {
	-webkit-text-size-adjust: 100%; /* 2 */
	line-height: 1.5; /* 1 */
	font-family: var(--font); /* 4 */
	-moz-tab-size: 4; /* 3 */
	tab-size: 4; /* 3 */
}

.gradio-container-5-31-0 .cropper-container {
	position: relative;
	-ms-touch-action: none;
	touch-action: none;
	font-size: 0;
	line-height: 0;
	direction: ltr;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.gradio-container-5-31-0 .cropper-container img {
	display: block;
	image-orientation: 0deg;
	width: 100%;
	min-width: 0 !important;
	max-width: none !important;
	height: 100%;
	min-height: 0 !important;
	max-height: none !important;
}

.gradio-container-5-31-0 .cropper-wrap-box,
.gradio-container-5-31-0 .cropper-canvas,
.gradio-container-5-31-0 .cropper-drag-box,
.gradio-container-5-31-0 .cropper-crop-box,
.gradio-container-5-31-0 .cropper-modal {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.gradio-container-5-31-0 .cropper-wrap-box,
.gradio-container-5-31-0 .cropper-canvas {
	overflow: hidden;
}

.gradio-container-5-31-0 .cropper-drag-box {
	opacity: 0;
	background-color: #fff;
}

.gradio-container-5-31-0 .cropper-modal {
	opacity: 0.5;
	background-color: #000;
}

.gradio-container-5-31-0 .cropper-view-box {
	display: block;
	outline: 1px solid #39f;
	outline-color: rgba(51, 153, 255, 0.75);
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.gradio-container-5-31-0 .cropper-dashed {
	display: block;
	position: absolute;
	opacity: 0.5;
	border: 0 dashed #eee;
}

.gradio-container-5-31-0 .cropper-dashed.dashed-h {
	top: calc(100% / 3);
	left: 0;
	border-top-width: 1px;
	border-bottom-width: 1px;
	width: 100%;
	height: calc(100% / 3);
}

.gradio-container-5-31-0 .cropper-dashed.dashed-v {
	top: 0;
	left: calc(100% / 3);
	border-right-width: 1px;
	border-left-width: 1px;
	width: calc(100% / 3);
	height: 100%;
}

.gradio-container-5-31-0 .cropper-center {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	opacity: 0.75;
	width: 0;
	height: 0;
}

.gradio-container-5-31-0 .cropper-center::before,
.gradio-container-5-31-0 .cropper-center::after {
	display: block;
	position: absolute;
	background-color: #eee;
	content: " ";
}

.gradio-container-5-31-0 .cropper-center::before {
	top: 0;
	left: -3px;
	width: 7px;
	height: 1px;
}

.gradio-container-5-31-0 .cropper-center::after {
	top: -3px;
	left: 0;
	width: 1px;
	height: 7px;
}

.gradio-container-5-31-0 .cropper-face,
.gradio-container-5-31-0 .cropper-line,
.gradio-container-5-31-0 .cropper-point {
	display: block;
	position: absolute;
	opacity: 0.1;
	width: 100%;
	height: 100%;
}

.gradio-container-5-31-0 .cropper-face {
	top: 0;
	left: 0;
	background-color: #fff;
}

.gradio-container-5-31-0 .cropper-line {
	background-color: #39f;
}

.gradio-container-5-31-0 .cropper-line.line-e {
	top: 0;
	right: -3px;
	cursor: ew-resize;
	width: 5px;
}

.gradio-container-5-31-0 .cropper-line.line-n {
	top: -3px;
	left: 0;
	cursor: ns-resize;
	height: 5px;
}

.gradio-container-5-31-0 .cropper-line.line-w {
	top: 0;
	left: -3px;
	cursor: ew-resize;
	width: 5px;
}

.gradio-container-5-31-0 .cropper-line.line-s {
	bottom: -3px;
	left: 0;
	cursor: ns-resize;
	height: 5px;
}

.gradio-container-5-31-0 .cropper-point {
	opacity: 0.75;
	background-color: #39f;
	width: 5px;
	height: 5px;
}

.gradio-container-5-31-0 .cropper-point.point-e {
	top: 50%;
	right: -3px;
	cursor: ew-resize;
	margin-top: -3px;
}

.gradio-container-5-31-0 .cropper-point.point-n {
	top: -3px;
	left: 50%;
	cursor: ns-resize;
	margin-left: -3px;
}

.gradio-container-5-31-0 .cropper-point.point-w {
	top: 50%;
	left: -3px;
	cursor: ew-resize;
	margin-top: -3px;
}

.gradio-container-5-31-0 .cropper-point.point-s {
	bottom: -3px;
	left: 50%;
	cursor: s-resize;
	margin-left: -3px;
}

.gradio-container-5-31-0 .cropper-point.point-ne {
	top: -3px;
	right: -3px;
	cursor: nesw-resize;
}

.gradio-container-5-31-0 .cropper-point.point-nw {
	top: -3px;
	left: -3px;
	cursor: nwse-resize;
}

.gradio-container-5-31-0 .cropper-point.point-sw {
	bottom: -3px;
	left: -3px;
	cursor: nesw-resize;
}

.gradio-container-5-31-0 .cropper-point.point-se {
	right: -3px;
	bottom: -3px;
	opacity: 1;
	cursor: nwse-resize;
	width: 20px;
	height: 20px;
}

@media (min-width: 768px) {
	.gradio-container-5-31-0 .cropper-point.point-se {
		width: 15px;
		height: 15px;
	}
}

@media (min-width: 992px) {
	.gradio-container-5-31-0 .cropper-point.point-se {
		width: 10px;
		height: 10px;
	}
}

@media (min-width: 1200px) {
	.gradio-container-5-31-0 .cropper-point.point-se {
		opacity: 0.75;
		width: 5px;
		height: 5px;
	}
}

.gradio-container-5-31-0 .cropper-point.point-se::before {
	display: block;
	position: absolute;
	right: -50%;
	bottom: -50%;
	opacity: 0;
	background-color: #39f;
	width: 200%;
	height: 200%;
	content: " ";
}

.gradio-container-5-31-0 .cropper-invisible {
	opacity: 0;
}

.gradio-container-5-31-0 .cropper-bg {
	background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");
}

.gradio-container-5-31-0 .cropper-hide {
	display: block;
	position: absolute;
	width: 0;
	height: 0;
}

.gradio-container-5-31-0 .cropper-hidden {
	display: none !important;
}

.gradio-container-5-31-0 .cropper-move {
	cursor: move;
}

.gradio-container-5-31-0 .cropper-crop {
	cursor: crosshair;
}

.gradio-container-5-31-0 .cropper-disabled .cropper-drag-box,
.gradio-container-5-31-0 .cropper-disabled .cropper-face,
.gradio-container-5-31-0 .cropper-disabled .cropper-line,
.gradio-container-5-31-0 .cropper-disabled .cropper-point {
	cursor: not-allowed;
}
/**
  * THIS IS AN AUTO-GENERATED FILE
  * Edit Pollen config to update
  */
:root {
  --scale-0: 1rem;
  --scale-1: 1.125rem;
  --scale-2: 1.25rem;
  --scale-3: 1.5rem;
  --scale-4: 1.875rem;
  --scale-5: 2.25rem;
  --scale-6: 3rem;
  --scale-7: 3.75rem;
  --scale-8: 4.5rem;
  --scale-9: 6rem;
  --scale-10: 8rem;
  --scale-000: 0.75rem;
  --scale-00: 0.875rem;
  --scale-fluid-0: clamp(0.875rem, 0.8rem + 0.25vw, 1rem);
  --scale-fluid-1: clamp(1rem, 0.925rem + 0.25vw, 1.125rem);
  --scale-fluid-2: clamp(1.125rem, 1.05rem + 0.25vw, 1.25rem);
  --scale-fluid-3: clamp(1.25rem, 1.1rem + 0.5vw, 1.5rem);
  --scale-fluid-4: clamp(1.5rem, 1.275rem + 0.75vw, 1.875rem);
  --scale-fluid-5: clamp(1.875rem, 1.65rem + 0.75vw, 2.25rem);
  --scale-fluid-6: clamp(2.25rem, 1.8rem + 1.5vw, 3rem);
  --scale-fluid-7: clamp(3rem, 2.55rem + 1.5vw, 3.75rem);
  --scale-fluid-8: clamp(3.75rem, 3.3rem + 1.5vw, 4.5rem);
  --scale-fluid-9: clamp(4.5rem, 3.6rem + 3vw, 6rem);
  --scale-fluid-10: clamp(6rem, 4.8rem + 4vw, 8rem);
  --scale-fluid-000: clamp(0.625rem, 0.55rem + 0.25vw, 0.75rem);
  --scale-fluid-00: clamp(0.75rem, 0.675rem + 0.25vw, 0.875rem);
  --font-sans: IBM Plex Sans, ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  --font-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: IBM Plex Mono, ui-monospace, SFMono-Regular, Menlo, Monaco,
    Consolas, "Liberation Mono", "Courier New", monospace;
  --weight-light: 300;
  --weight-regular: 400;
  --weight-medium: 500;
  --weight-semibold: 600;
  --weight-bold: 700;
  --weight-extrabold: 800;
  --weight-black: 900;
  --line-none: 1;
  --line-xs: 1.125;
  --line-sm: 1.4;
  --line-md: 1.5;
  --line-lg: 1.625;
  --line-xl: 2;
  --letter-xs: -0.05em;
  --letter-sm: -0.025em;
  --letter-none: 0em;
  --letter-lg: 0.025em;
  --letter-xl: 0.05em;
  --prose-xs: 45ch;
  --prose-sm: 55ch;
  --prose-md: 65ch;
  --prose-lg: 75ch;
  --prose-xl: 85ch;
  --size-1: 4px;
  --size-2: 8px;
  --size-3: 12px;
  --size-4: 16px;
  --size-5: 20px;
  --size-6: 24px;
  --size-7: 28px;
  --size-8: 32px;
  --size-9: 36px;
  --size-10: 40px;
  --size-11: 44px;
  --size-12: 48px;
  --size-14: 56px;
  --size-16: 64px;
  --size-20: 80px;
  --size-24: 96px;
  --size-28: 112px;
  --size-32: 128px;
  --size-36: 144px;
  --size-40: 160px;
  --size-44: 176px;
  --size-48: 192px;
  --size-52: 208px;
  --size-56: 224px;
  --size-60: 240px;
  --size-64: 256px;
  --size-72: 288px;
  --size-80: 320px;
  --size-96: 384px;
  --size-px: 1px;
  --size-full: 100%;
  --size-screen: 100vw;
  --size-min: min-content;
  --size-max: max-content;
  --size-0-5: 2px;
  --size-1-5: 6px;
  --size-2-5: 10px;
  --size-screen-h: 100vh;
  --width-xs: 480px;
  --width-sm: 640px;
  --width-md: 768px;
  --width-lg: 1024px;
  --width-xl: 1280px;
  --ratio-square: 1/1;
  --ratio-portrait: 3/4;
  --ratio-landscape: 4/3;
  --ratio-tall: 2/3;
  --ratio-wide: 3/2;
  --ratio-widescreen: 16/9;
  --ratio-golden: 1.618/1;
  --radius-100: 100%;
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-full: 9999px;
  --radius-2xl: 16px;
  --radius-3xl: 22px;
  --blur-xs: blur(4px);
  --blur-sm: blur(8px);
  --blur-md: blur(16px);
  --blur-lg: blur(24px);
  --blur-xl: blur(40px);
  --layer-1: 10;
  --layer-2: 20;
  --layer-3: 30;
  --layer-4: 40;
  --layer-5: 50;
  --layer-below: -1;
  --layer-top: 2147483647;
  --shadow-xs: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-sm: 0 4px 6px -2px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 12px 16px -4px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 24px -4px rgba(0, 0, 0, 0.1),
    0 8px 8px -4px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 24px 48px -12px rgba(0, 0, 0, 0.25);
  --ease-in-sine: cubic-bezier(0.47, 0, 0.745, 0.715);
  --ease-out-sine: cubic-bezier(0.39, 0.575, 0.565, 1);
  --ease-in-out-sine: cubic-bezier(0.445, 0.05, 0.55, 0.95);
  --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
  --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
  --ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-in-quart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
  --ease-out-quart: cubic-bezier(0.165, 0.84, 0.44, 1);
  --ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
  --ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
  --ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
  --ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);
  --ease-in-expo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
  --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-in-out-expo: cubic-bezier(1, 0, 0, 1);
  --ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.335);
  --ease-out-circ: cubic-bezier(0.075, 0.82, 0.165, 1);
  --ease-in-out-circ: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  --ease-in-back: cubic-bezier(0.6, -0.28, 0.735, 0.045);
  --ease-out-back: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --elevation-1: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --elevation-2: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --elevation-3: 0 4px 6px -2px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.06);
  --elevation-4: 0 12px 16px -4px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --elevation-5: 0 20px 24px -4px rgba(0, 0, 0, 0.1),
    0 8px 8px -4px rgba(0, 0, 0, 0.04);
  --elevation-6: 0 24px 48px -12px rgba(0, 0, 0, 0.25);
  --elevation-7: 0 32px 64px -12px rgba(0, 0, 0, 0.2);
  --color-grey-50: #f9fafb;
  --color-grey-100: #f3f4f6;
  --color-grey-200: #e5e7eb;
  --color-grey-300: #d1d5db;
  --color-grey-400: #9ca3af;
  --color-grey-500: #6b7280;
  --color-grey-600: #4b5563;
  --color-grey-700: #374151;
  --color-grey-800: #1f2937;
  --color-grey-900: #111827;
  --color-black: #14141b;
  --color-grey: #6b7280;
  --color-red-300: #fca5a5;
  --color-red-500: #ef4444;
  --color-red-700: #b91c1c;
  --color-red: #ef4444;
  --color-green-300: #86efac;
  --color-green-500: #22c55e;
  --color-green-700: #15803d;
  --color-green: #22c55e;
  --color-blue-300: #93c5fd;
  --color-blue-500: #0ea5e9;
  --color-blue-700: #1d4ed8;
  --color-blue: #0ea5e9;
  --color-pink-300: #fbb6ce;
  --color-pink-500: #ed64a6;
  --color-pink-700: #d53f8c;
  --color-pink: var(--color-pink-500);
  --color-purple-300: #b794f4;
  --color-purple-500: #805ad5;
  --color-purple-700: #6b46c1;
  --color-purple: var(--color-purple-500);
  --color-teal-300: #81e6d9;
  --color-teal-500: #38b2ac;
  --color-teal-700: #2c7a7b;
  --color-teal: var(--color-teal-500);
  --color-yellow-300: #fde047;
  --color-yellow-500: #eab308;
  --color-yellow-700: #a16207;
  --color-yellow: #eab308;
  --color-orange-300: #ffb066;
  --color-orange-500: #ff7c00;
  --color-orange-700: #ce6400;
  --color-orange: #f97316;
  --color-brown-300: #a1887f;
  --color-brown-500: #795548;
  --color-brown-700: #5d4037;
  --color-brown: var(--color-brown-500);
  --color-blue-10: #fafcff;
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-400: #60a5fa;
  --color-blue-600: #2563eb;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;
  --color-blue-950: #1c366b;
  --color-grey-10: #fdfdfe;
  --color-grey-950: #0b0f19;
  --color-red-10: #fffbfb;
  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-400: #f87171;
  --color-red-600: #dc2626;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;
  --color-red-950: #63171a;
  --color-green-10: #f9fefc;
  --color-green-50: #ecfdf5;
  --color-green-100: #d1fae5;
  --color-green-200: #bbf7d0;
  --color-green-400: #4ade80;
  --color-green-600: #16a34a;
  --color-green-800: #166534;
  --color-green-900: #14532d;
  --color-green-950: #134227;
  --color-orange-10: #fffbf6;
  --color-orange-50: #fff2e5;
  --color-orange-100: #ffe5cc;
  --color-orange-200: #ffd8b4;
  --color-orange-400: #ff9633;
  --color-orange-600: #ee7400;
  --color-orange-800: #a45000;
  --color-orange-900: #5c2d00;
  --color-orange-950: #3c1f00;
  --color-yellow-10: #fffef8;
  --color-yellow-50: #fffbeb;
  --color-yellow-100: #fff9c2;
  --color-yellow-200: #fef08a;
  --color-yellow-400: #facc15;
  --color-yellow-600: #ca8a04;
  --color-yellow-800: #854d0e;
  --color-yellow-900: #713f12;
  --color-yellow-950: #633112;
  --grid-2: repeat(2, minmax(0, 1fr));
  --grid-3: repeat(3, minmax(0, 1fr));
  --grid-4: repeat(4, minmax(0, 1fr));
  --grid-5: repeat(5, minmax(0, 1fr));
  --grid-6: repeat(6, minmax(0, 1fr));
  --grid-7: repeat(7, minmax(0, 1fr));
  --grid-8: repeat(8, minmax(0, 1fr));
  --grid-9: repeat(9, minmax(0, 1fr));
  --grid-10: repeat(10, minmax(0, 1fr));
  --grid-11: repeat(11, minmax(0, 1fr));
  --grid-12: repeat(12, minmax(0, 1fr));
  --grid-page-width: var(--width-xl);
  --grid-page-gutter: 5vw;
  --grid-page-main: 2 / 3;
  --grid-page: minmax(var(--grid-page-gutter), 1fr)
    minmax(0, var(--grid-page-width)) minmax(var(--grid-page-gutter), 1fr);
}
.gradio-container-5-31-0 .prose {
	font-weight: var(--prose-text-weight);
	font-size: var(--text-md);
}

.gradio-container-5-31-0 .prose * {
	color: var(--body-text-color);
}

.gradio-container-5-31-0 .prose p {
	margin-bottom: var(--spacing-sm);
	line-height: var(--line-lg);
}

/* headings
–––––––––––––––––––––––––––––––––––––––––––––––––– */

.gradio-container-5-31-0 .prose h1,
.gradio-container-5-31-0 .prose h2,
.gradio-container-5-31-0 .prose h3,
.gradio-container-5-31-0 .prose h4,
.gradio-container-5-31-0 .prose h5 {
	margin: var(--spacing-xxl) 0 var(--spacing-lg);
	font-weight: var(--prose-header-text-weight);
	line-height: 1.3;
	color: var(--body-text-color);
}

.gradio-container-5-31-0 .prose > *:first-child {
	margin-top: 0;
}

.gradio-container-5-31-0 .prose h1 {
	font-size: var(--text-xxl);
}

.gradio-container-5-31-0 .prose h2 {
	font-size: var(--text-xl);
}

.gradio-container-5-31-0 .prose h3 {
	font-size: var(--text-lg);
}

.gradio-container-5-31-0 .prose h4 {
	font-size: 1.1em;
}

.gradio-container-5-31-0 .prose h5 {
	font-size: 1.05em;
}

/* lists
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.gradio-container-5-31-0 .prose ul {
	list-style: circle inside;
}
.gradio-container-5-31-0 .prose ol {
	list-style: decimal inside;
}

.gradio-container-5-31-0 .prose ul > p,
.gradio-container-5-31-0 .prose li > p {
	display: inline;
}
.gradio-container-5-31-0 .prose ol,
.gradio-container-5-31-0 .prose ul {
	margin-top: 0;
	padding-left: 0;
}
.gradio-container-5-31-0 .prose ul ul,
.gradio-container-5-31-0 .prose ul ol,
.gradio-container-5-31-0 .prose ol ol,
.gradio-container-5-31-0 .prose ol ul {
	margin: 0.5em 0 0.5em 0em;
	font-size: 90%;
	padding-inline-start: 2em;
}
.gradio-container-5-31-0 .prose li {
	margin-bottom: 0.5em;
}

/* code
–––––––––––––––––––––––––––––––––––––––––––––––––– */

/* tables
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.gradio-container-5-31-0 .prose th,
.gradio-container-5-31-0 .prose td {
	border-bottom: 1px solid #e1e1e1;
	padding: var(--text-xs) var(--text-md);
	text-align: left;
}

/* spacing
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.gradio-container-5-31-0 .prose button,
.gradio-container-5-31-0 .prose .button {
	margin-bottom: var(--spacing-sm);
}
.gradio-container-5-31-0 .prose input,
.gradio-container-5-31-0 .prose textarea,
.gradio-container-5-31-0 .prose select,
.gradio-container-5-31-0 .prose fieldset {
	margin-bottom: var(--spacing-sm);
}
.gradio-container-5-31-0 .prose pre,
.gradio-container-5-31-0 .prose blockquote,
.gradio-container-5-31-0 .prose dl,
.gradio-container-5-31-0 .prose figure,
.gradio-container-5-31-0 .prose table,
.gradio-container-5-31-0 .prose p,
.gradio-container-5-31-0 .prose ul,
.gradio-container-5-31-0 .prose ol,
.gradio-container-5-31-0 .prose form {
	margin-bottom: var(--spacing-md);
}

.gradio-container-5-31-0 .prose table,
.gradio-container-5-31-0 .prose tr,
.gradio-container-5-31-0 .prose td,
.gradio-container-5-31-0 .prose th {
	border: 1px solid var(--body-text-color);
}
.gradio-container-5-31-0 .prose table {
	border-collapse: collapse;
	margin-bottom: var(--spacing-xxl);
}

/* links
–––––––––––––––––––––––––––––––––––––––––––––––––– */
.gradio-container-5-31-0 .prose a {
	color: var(--link-text-color);
	text-decoration: underline;
}

.gradio-container-5-31-0 .prose a:visited {
	color: var(--link-text-color-visited);
}

.gradio-container-5-31-0 .prose a:hover {
	color: var(--link-text-color-hover);
}
.gradio-container-5-31-0 .prose a:active {
	color: var(--link-text-color-active);
}

/* misc
–––––––––––––––––––––––––––––––––––––––––––––––––– */

.gradio-container-5-31-0 .prose hr {
	margin-top: 3em;
	margin-bottom: 3.5em;
	border-width: 0;
	border-top: 1px solid #e1e1e1;
}

.gradio-container-5-31-0 .prose blockquote {
	margin: var(--size-6) 0 !important;
	border-left: 5px solid var(--border-color-primary);
	padding-left: var(--size-2);
}

.gradio-container-5-31-0 .prose :last-child {
	margin-bottom: 0 !important;
}
body{background:var(--body-background-fill);color:var(--body-text-color)}