"""Unit tests for CartonCloud API integration."""

import pytest
import responses
from datetime import datetime
from decimal import Decimal

from src.integrations.cartoncloud.auth import get_token
from src.integrations.cartoncloud.api import CartonCloudAPI, CartonCloudAPIError
# Import deprecated functions for backward compatibility testing
from src.integrations.cartoncloud.endpoints import (
    request,
    list_rate_cards,
    create_purchase_order,
    get_bulk_charges_report
)
from src.integrations.cartoncloud.models import PurchaseOrder, PurchaseOrderLine


class TestCartonCloudAuth:
    """Test CartonCloud authentication."""
    
    @responses.activate
    def test_get_token_success(self):
        """Test successful token retrieval."""
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/oauth/token",
            json={"access_token": "test_token_123", "expires_in": 3600},
            status=200
        )
        
        token = get_token("test_client", "test_secret")
        assert token == "test_token_123"
    
    @responses.activate
    def test_get_token_failure(self):
        """Test token retrieval failure."""
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/oauth/token",
            json={"error": "invalid_client"},
            status=401
        )
        
        with pytest.raises(Exception):
            get_token("invalid_client", "invalid_secret")


class TestCartonCloudEndpoints:
    """Test CartonCloud API endpoints."""
    
    @responses.activate
    def test_list_rate_cards(self):
        """Test listing rate cards."""
        responses.add(
            responses.GET,
            "https://api.cartoncloud.com/v1/rate-cards",
            json={
                "data": [
                    {
                        "id": "rc_001",
                        "name": "Standard Rates",
                        "customer_id": "cust_001",
                        "effective_date": "2024-01-01T00:00:00Z",
                        "status": "active",
                        "charge_lines": []
                    }
                ]
            },
            status=200
        )
        
        # Mock token
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/oauth/token",
            json={"access_token": "test_token"},
            status=200
        )
        
        rate_cards = list_rate_cards()
        assert len(rate_cards) == 1
        assert rate_cards[0].id == "rc_001"
        assert rate_cards[0].name == "Standard Rates"
    
    @responses.activate
    def test_create_purchase_order(self):
        """Test creating a purchase order."""
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/v1/purchase-orders",
            json={
                "data": {
                    "id": "po_001",
                    "po_number": "PO-2024-001",
                    "supplier_id": "supp_001",
                    "order_date": "2024-01-01T00:00:00Z",
                    "status": "draft",
                    "subtotal": "1000.00",
                    "total_amount": "1100.00",
                    "lines": []
                }
            },
            status=201
        )
        
        # Mock token
        responses.add(
            responses.POST,
            "https://api.cartoncloud.com/oauth/token",
            json={"access_token": "test_token"},
            status=200
        )
        
        po_data = PurchaseOrder(
            po_number="PO-2024-001",
            supplier_id="supp_001",
            order_date=datetime(2024, 1, 1),
            subtotal=Decimal("1000.00"),
            total_amount=Decimal("1100.00"),
            lines=[]
        )
        
        created_po = create_purchase_order(po_data)
        assert created_po.id == "po_001"
        assert created_po.po_number == "PO-2024-001"


@pytest.mark.asyncio
class TestCartonCloudIntegration:
    """Integration tests for CartonCloud features."""
    
    async def test_nightly_audit_workflow(self):
        """Test the nightly audit workflow with mocked data."""
        # This would be an integration test with mocked API responses
        # Testing the full workflow from charge retrieval to update queuing
        pass
