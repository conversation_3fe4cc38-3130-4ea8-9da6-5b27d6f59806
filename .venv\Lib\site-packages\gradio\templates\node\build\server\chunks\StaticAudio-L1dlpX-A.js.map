{"version": 3, "file": "StaticAudio-L1dlpX-A.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/StaticAudio.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport { B as <PERSON><PERSON><PERSON><PERSON>, M as Music, I as IconButtonWrapper, h as <PERSON><PERSON><PERSON>utton, D as Download, j as ShareButton, k as Empty } from \"./client.js\";\nimport { u as uploadToHuggingFace } from \"./utils.js\";\nimport { A as AudioPlayer } from \"./AudioPlayer.js\";\nimport { createEventDispatcher } from \"svelte\";\nimport { D as DownloadLink } from \"./DownloadLink.js\";\nconst StaticAudio = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value = null } = $$props;\n  let { label } = $$props;\n  let { show_label = true } = $$props;\n  let { show_download_button = true } = $$props;\n  let { show_share_button = false } = $$props;\n  let { i18n } = $$props;\n  let { waveform_settings = {} } = $$props;\n  let { waveform_options = { show_recording_waveform: true } } = $$props;\n  let { editable = true } = $$props;\n  let { loop } = $$props;\n  let { display_icon_button_wrapper_top_corner = false } = $$props;\n  const dispatch = createEventDispatcher();\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.display_icon_button_wrapper_top_corner === void 0 && $$bindings.display_icon_button_wrapper_top_corner && display_icon_button_wrapper_top_corner !== void 0)\n    $$bindings.display_icon_button_wrapper_top_corner(display_icon_button_wrapper_top_corner);\n  value && dispatch(\"change\", value);\n  return `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n    $$result,\n    {\n      show_label,\n      Icon: Music,\n      float: false,\n      label: label || i18n(\"audio.audio\")\n    },\n    {},\n    {}\n  )} ${value !== null ? `${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render(\n    $$result,\n    {\n      display_top_corner: display_icon_button_wrapper_top_corner\n    },\n    {},\n    {\n      default: () => {\n        return `${show_download_button ? `${validate_component(DownloadLink, \"DownloadLink\").$$render(\n          $$result,\n          {\n            href: value.is_stream ? value.url?.replace(\"playlist.m3u8\", \"playlist-file\") : value.url,\n            download: value.orig_name || value.path\n          },\n          {},\n          {\n            default: () => {\n              return `${validate_component(IconButton, \"IconButton\").$$render(\n                $$result,\n                {\n                  Icon: Download,\n                  label: i18n(\"common.download\")\n                },\n                {},\n                {}\n              )}`;\n            }\n          }\n        )}` : ``} ${show_share_button ? `${validate_component(ShareButton, \"ShareButton\").$$render(\n          $$result,\n          {\n            i18n,\n            formatter: async (value2) => {\n              if (!value2)\n                return \"\";\n              let url = await uploadToHuggingFace(value2.url);\n              return `<audio controls src=\"${url}\"></audio>`;\n            },\n            value\n          },\n          {},\n          {}\n        )}` : ``}`;\n      }\n    }\n  )} ${validate_component(AudioPlayer, \"AudioPlayer\").$$render(\n    $$result,\n    {\n      value,\n      label,\n      i18n,\n      waveform_settings,\n      waveform_options,\n      editable,\n      loop\n    },\n    {},\n    {}\n  )}` : `${validate_component(Empty, \"Empty\").$$render($$result, { size: \"small\" }, {}, {\n    default: () => {\n      return `${validate_component(Music, \"Music\").$$render($$result, {}, {}, {})}`;\n    }\n  })}`}`;\n});\nexport {\n  StaticAudio as default\n};\n"], "names": ["AudioPlayer"], "mappings": ";;;;;;;;;;;AAMK,MAAC,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,iBAAiB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC;AACzE,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,sCAAsC,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnE,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,sCAAsC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sCAAsC,IAAI,sCAAsC,KAAK,KAAK,CAAC;AACzK,IAAI,UAAU,CAAC,sCAAsC,CAAC,sCAAsC,CAAC,CAAC;AAC9F,EAAE,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrC,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,UAAU;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;AACzC,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ;AAC9F,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,kBAAkB,EAAE,sCAAsC;AAChE,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACrG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,eAAe,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,GAAG;AACpG,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI;AACnD,WAAW;AACX,UAAU,EAAE;AACZ,UAAU;AACV,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC7E,gBAAgB,QAAQ;AACxB,gBAAgB;AAChB,kBAAkB,IAAI,EAAE,QAAQ;AAChC,kBAAkB,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAChD,iBAAiB;AACjB,gBAAgB,EAAE;AAClB,gBAAgB,EAAE;AAClB,eAAe,CAAC,CAAC,CAAC;AAClB,aAAa;AACb,WAAW;AACX,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAClG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI;AAChB,YAAY,SAAS,EAAE,OAAO,MAAM,KAAK;AACzC,cAAc,IAAI,CAAC,MAAM;AACzB,gBAAgB,OAAO,EAAE,CAAC;AAC1B,cAAc,IAAI,GAAG,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9D,cAAc,OAAO,CAAC,qBAAqB,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAC7D,aAAa;AACb,YAAY,KAAK;AACjB,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAACA,aAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAC9D,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,IAAI;AACV,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,QAAQ;AACd,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AACxF,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACpF,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC;;;;"}