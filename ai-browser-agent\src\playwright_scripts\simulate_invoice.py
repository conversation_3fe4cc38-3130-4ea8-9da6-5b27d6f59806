"""Simulate invoice charges and download PDF via CartonCloud UI."""

import asyncio
import logging
from pathlib import Path
from typing import Any

from playwright.async_api import Download
from playwright.async_api import Page

logger = logging.getLogger(__name__)


async def run(page: Page, **kwargs) -> dict[str, Any]:
    """Run invoice simulation and download PDF in CartonCloud UI.
    
    Args:
        page: Playwright page instance
        **kwargs: Required parameters:
            - customer_id: Customer ID for simulation
            - simulation_period: Period for simulation (e.g., "last_month", "last_week")
            - download_path: Path to save the downloaded PDF (optional)
            
    Returns:
        Dictionary with operation result and download details
    """
    customer_id = kwargs.get("customer_id")
    simulation_period = kwargs.get("simulation_period", "last_month")
    download_path = kwargs.get("download_path", "./downloads")

    if not customer_id:
        return {
            "success": False,
            "error": "Missing required parameter: customer_id",
        }

    try:
        logger.info(f"Running invoice simulation for customer {customer_id}")

        # Navigate to invoice simulation page
        await page.goto(f"/customers/{customer_id}/simulate-charges")
        await page.wait_for_load_state("networkidle")

        # Wait for simulation form to load
        await page.wait_for_selector('[data-testid="simulation-form"], .simulation-form', timeout=10000)

        # Set simulation period
        period_selector = page.locator('select[name="period"], select[data-field="period"]').first
        if await period_selector.is_visible():
            await period_selector.select_option(value=simulation_period)
        else:
            # Try to find period buttons or date inputs
            period_button = page.get_by_role("button", name=simulation_period.replace("_", " ").title())
            if await period_button.is_visible():
                await period_button.click()

        # Set additional simulation parameters if available
        include_adjustments = page.locator('input[name="include_adjustments"]').first
        if await include_adjustments.is_visible():
            await include_adjustments.check()

        # Start simulation
        simulate_button = page.get_by_role("button", name="Run Simulation")
        if not await simulate_button.is_visible():
            simulate_button = page.get_by_role("button", name="Simulate Charges")
        if not await simulate_button.is_visible():
            simulate_button = page.locator('button[type="submit"]').first

        await simulate_button.click()

        # Wait for simulation to complete
        logger.info("Waiting for simulation to complete...")
        await page.wait_for_selector(
            '.simulation-complete, [data-testid="simulation-results"], .results-table',
            timeout=30000
        )

        # Wait a bit more for results to fully load
        await asyncio.sleep(2)

        # Look for download button
        download_button = page.get_by_role("button", name="Download PDF")
        if not await download_button.is_visible():
            download_button = page.get_by_role("button", name="Export PDF")
        if not await download_button.is_visible():
            download_button = page.get_by_role("link", name="Download")
        if not await download_button.is_visible():
            download_button = page.locator('a[href*=".pdf"], button[data-export="pdf"]').first

        if not await download_button.is_visible():
            return {
                "success": False,
                "error": "Download button not found after simulation",
            }

        # Setup download handling
        download_path_obj = Path(download_path)
        download_path_obj.mkdir(parents=True, exist_ok=True)

        # Start download
        async with page.expect_download() as download_info:
            await download_button.click()

        download: Download = await download_info.value

        # Save the download
        suggested_filename = download.suggested_filename
        if not suggested_filename:
            suggested_filename = f"invoice_simulation_{customer_id}_{simulation_period}.pdf"

        file_path = download_path_obj / suggested_filename
        await download.save_as(file_path)

        logger.info(f"Successfully downloaded simulation PDF to {file_path}")

        return {
            "success": True,
            "message": "Invoice simulation completed and PDF downloaded",
            "customer_id": customer_id,
            "simulation_period": simulation_period,
            "file_path": str(file_path),
            "filename": suggested_filename,
        }

    except TimeoutError:
        logger.error("Timeout waiting for simulation to complete")
        return {
            "success": False,
            "error": "Timeout waiting for simulation to complete",
            "customer_id": customer_id,
        }

    except Exception as e:
        logger.error(f"Error running invoice simulation: {e}")
        return {
            "success": False,
            "error": str(e),
            "customer_id": customer_id,
        }
